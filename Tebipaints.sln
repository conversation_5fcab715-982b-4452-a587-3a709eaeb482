
Microsoft Visual Studio Solution File, Format Version 12.00
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{23CAD4A5-B335-4D7A-B7F4-29015552A4FA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tebipaints.Domain", "src\Tebipaints.Domain\Tebipaints.Domain.csproj", "{802950DC-F370-42CE-B8A5-6C64F8F9C9D1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tebipaints.Application", "src\Tebipaints.Application\Tebipaints.Application.csproj", "{0C88681A-04A7-487A-8125-A2E0517ABBEF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tebipaints.Infrastructure", "src\Tebipaints.Infrastructure\Tebipaints.Infrastructure.csproj", "{DC03706C-9DD1-4B0B-A6DB-7280EE48F879}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tebipaints.Api", "src\Tebipaints.Api\Tebipaints.Api.csproj", "{5528BAF3-E71F-4EC3-AB70-96CB50D49CA1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{A6D4FF37-CC7E-4340-96B6-92CDAF41770A}"
	ProjectSection(SolutionItems) = preProject
		compose.yaml = compose.yaml
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Tebipaints.Templates", "src\Tebipaints.Templates\Tebipaints.Templates.csproj", "{D306BF73-A5B2-4C80-8F37-91C254E950AD}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{802950DC-F370-42CE-B8A5-6C64F8F9C9D1} = {23CAD4A5-B335-4D7A-B7F4-29015552A4FA}
		{0C88681A-04A7-487A-8125-A2E0517ABBEF} = {23CAD4A5-B335-4D7A-B7F4-29015552A4FA}
		{DC03706C-9DD1-4B0B-A6DB-7280EE48F879} = {23CAD4A5-B335-4D7A-B7F4-29015552A4FA}
		{5528BAF3-E71F-4EC3-AB70-96CB50D49CA1} = {23CAD4A5-B335-4D7A-B7F4-29015552A4FA}
		{D306BF73-A5B2-4C80-8F37-91C254E950AD} = {23CAD4A5-B335-4D7A-B7F4-29015552A4FA}
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{802950DC-F370-42CE-B8A5-6C64F8F9C9D1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{802950DC-F370-42CE-B8A5-6C64F8F9C9D1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{802950DC-F370-42CE-B8A5-6C64F8F9C9D1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{802950DC-F370-42CE-B8A5-6C64F8F9C9D1}.Release|Any CPU.Build.0 = Release|Any CPU
		{0C88681A-04A7-487A-8125-A2E0517ABBEF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0C88681A-04A7-487A-8125-A2E0517ABBEF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0C88681A-04A7-487A-8125-A2E0517ABBEF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0C88681A-04A7-487A-8125-A2E0517ABBEF}.Release|Any CPU.Build.0 = Release|Any CPU
		{DC03706C-9DD1-4B0B-A6DB-7280EE48F879}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC03706C-9DD1-4B0B-A6DB-7280EE48F879}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC03706C-9DD1-4B0B-A6DB-7280EE48F879}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC03706C-9DD1-4B0B-A6DB-7280EE48F879}.Release|Any CPU.Build.0 = Release|Any CPU
		{5528BAF3-E71F-4EC3-AB70-96CB50D49CA1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5528BAF3-E71F-4EC3-AB70-96CB50D49CA1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5528BAF3-E71F-4EC3-AB70-96CB50D49CA1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5528BAF3-E71F-4EC3-AB70-96CB50D49CA1}.Release|Any CPU.Build.0 = Release|Any CPU
		{D306BF73-A5B2-4C80-8F37-91C254E950AD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D306BF73-A5B2-4C80-8F37-91C254E950AD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D306BF73-A5B2-4C80-8F37-91C254E950AD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D306BF73-A5B2-4C80-8F37-91C254E950AD}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
