services:
  tebipaints.api:
    image: tebipaints.api
    build:
      context: .
      dockerfile: src/Tebipaints.Api/Dockerfile
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_HTTP_PORTS=5224
      - CHROME_ENDPOINT=http://tebipaints-chrome:3000
    ports:
      - "5224:5224"
      - "7215:7215"
    volumes:
      - ~/.aspnet/https:/https:ro
    depends_on:
      - tebipaints-db
  
  tebipaints-db:
    image: postgres:latest
    container_name: Tebipaints.Db
    environment:
      - POSTGRES_DB=tebipaints
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - . / .containers/database:/var/lib/postgresql/data
    ports:
      - "5432:5432"
        
  tebipaints-seq:
    image: datalust/seq:latest
    container_name: Tebipaints.Seq
    environment:
      - ACCEPT_EULA=Y
    ports:
      - "5341:5341"
      - "8081:80"
  
  tebipaints-chrome:
    image: browserless/chrome
    container_name: tebipaints.chrome
    ports:
      - "3001:3000"
    environment:
      - PORT=3000
      - CONNECTION_TIMEOUT=60000
      - MAX_CONCURRENT_SESSIONS=5