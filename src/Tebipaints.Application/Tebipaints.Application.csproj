<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Dapper" Version="2.1.66" />
      <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
      <PackageReference Include="MediatR" Version="12.4.1" />
      <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.2" />
      <PackageReference Include="Serilog" Version="4.2.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Tebipaints.Domain\Tebipaints.Domain.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Formulation\ArchiveFormulation\" />
    </ItemGroup>

</Project>
