using FluentValidation;

namespace Tebipaints.Application.Inventories.CreateProductInventory;

public class CreateProductInventoryCommandValidator : AbstractValidator<CreateProductInventoryCommand>
{
    public CreateProductInventoryCommandValidator()
    {
        RuleFor(v => v.ProductId).NotEmpty();
        RuleFor(v => v.InventoryLocation).NotEmpty();
        RuleFor(v => v.MinimumStockLevel).GreaterThan(0);
        RuleFor(v => v.ReorderPoint).GreaterThan(0);
    }
}