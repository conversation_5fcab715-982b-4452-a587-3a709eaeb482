using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Inventory;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.Product.Errors;

namespace Tebipaints.Application.Inventories.CreateProductInventory;

public class CreateProductInventoryCommandHandler : ICommandHandler<CreateProductInventoryCommand, Guid>
{
    private readonly IProductInventoryRepository _inventoryRepository;
    private readonly IProductRepository _productRepository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateProductInventoryCommandHandler(
        IProductInventoryRepository inventoryRepository,
        IProductRepository productRepository,
        IUnitOfWork unitOfWork)
    {
        _inventoryRepository = inventoryRepository;
        _productRepository = productRepository;
        _unitOfWork = unitOfWork;
    }
    
    public async Task<Result<Guid>> Handle(
        CreateProductInventoryCommand command,
        CancellationToken cancellationToken)
    {
        var product = await _productRepository
            .GetByIdAsync(command.ProductId, cancellationToken);

        if (product is null)
        {
            return Result.Failure<Guid>(ProductErrors.NotFound(command.ProductId));
        }
        
        if (!Enum.TryParse<InventoryLocation>(command.InventoryLocation, true, out var location))
        {
            return Result.Failure<Guid>(InventoryErrors.InvalidInventoryLocation());
        }

        var existingInventory = await _inventoryRepository
            .GetByProductAndLocationAsync(command.ProductId, location, cancellationToken);

        if (existingInventory is not null)
        {
            return Result.Failure<Guid>(InventoryErrors.AlreadyExists(command.ProductId, command.InventoryLocation));
        }

        

        var inventory = ProductInventory.Create(
            command.ProductId,
            location,
            command.MinimumStockLevel,
            command.ReorderPoint);

        if (inventory.IsFailure)
        {
            return Result.Failure<Guid>(inventory.Error);
        }

        _inventoryRepository.Add(inventory.Value);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success(inventory.Value.Id);
    }
}