using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Application.Inventories.ListMaterialInventory;

public class ListMaterialInventoryQueryHandler : IQueryHandler<ListMaterialInventoryQuery,  IReadOnlyList<MaterialInventoryResponse>>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;

    public ListMaterialInventoryQueryHandler(ISqlConnectionFactory sqlConnectionFactory)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
    }

    public async Task<Result<IReadOnlyList<MaterialInventoryResponse>>> Handle(ListMaterialInventoryQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();

        const string sql = @"
            SELECT
                mi.material_id AS MaterialId,
                mi.id AS InventoryId,
                m.code AS Code,
                m.name AS Name,
                m.description AS Description,
                m.type AS Type,
                m.default_unit AS DefaultUnit,
                m.status AS MaterialStatus,
                inv.location AS Location,
                COALESCE(SUM(CASE mit.type
                    WHEN 'StockIn' THEN mit.quantity
                    WHEN 'StockOut' THEN -mit.quantity
                    ELSE 0 END), 0) AS CurrentStock,
                COALESCE(res.ReservedStock, 0) AS ReservedStock,
                COALESCE(res.ActiveReservationsCount, 0) AS ActiveReservationsCount,
                inv.minimum_stock_level AS MinimumStockLevel,
                inv.reorder_point AS ReorderPoint,
                last_movement.LastStockMovement,
                last_receipt.LastGoodsReceipt
            FROM material_inventories mi
            INNER JOIN materials m ON mi.material_id = m.id
            INNER JOIN inventory_double inv ON mi.id = inv.id
            LEFT JOIN material_inventory_transactions mit ON mit.inventory_double_id = mi.id
            LEFT JOIN (
                SELECT
                    inventory_id,
                    SUM(quantity) AS ReservedStock,
                    COUNT(*) AS ActiveReservationsCount
                FROM material_inventory_reservations
                GROUP BY inventory_id
            ) res ON res.inventory_id = mi.id
            LEFT JOIN (
                SELECT
                    inventory_double_id,
                    MAX(timestamp) AS LastStockMovement
                FROM material_inventory_transactions
                GROUP BY inventory_double_id
            ) last_movement ON last_movement.inventory_double_id = mi.id
            LEFT JOIN (
                SELECT
                    inventory_double_id,
                    MAX(timestamp) AS LastGoodsReceipt
                FROM material_inventory_transactions
                WHERE reference LIKE 'Goods Receipt:%'
                GROUP BY inventory_double_id
            ) last_receipt ON last_receipt.inventory_double_id = mi.id
            GROUP BY mi.material_id, mi.id, m.code, m.name, m.description, m.type,
                     m.default_unit, m.status, inv.location, inv.minimum_stock_level,
                     inv.reorder_point, res.ReservedStock, res.ActiveReservationsCount,
                     last_movement.LastStockMovement, last_receipt.LastGoodsReceipt
            ORDER BY inv.location, m.code
        ";

        var rows = (await connection.QueryAsync<MaterialInventoryRow>(sql)).ToList();

        var result = rows.Select(r => new MaterialInventoryResponse
        {
            // Core Identity
            MaterialId = r.MaterialId,
            InventoryId = r.InventoryId,
            Code = r.Code,
            Name = r.Name,
            Description = r.Description,

            // Material Properties
            Type = r.Type,
            DefaultUnit = r.DefaultUnit,
            MaterialStatus = r.MaterialStatus,
            Location = r.Location,

            // Stock Levels
            CurrentStock = r.CurrentStock,
            ReservedStock = r.ReservedStock,
            AvailableStock = r.CurrentStock - r.ReservedStock,

            // Reorder Management
            MinimumStockLevel = r.MinimumStockLevel,
            ReorderPoint = r.ReorderPoint,
            IsLowStock = r.CurrentStock <= r.ReorderPoint,

            // Activity Metrics
            ActiveReservationsCount = r.ActiveReservationsCount,
            LastStockMovement = r.LastStockMovement,
            LastGoodsReceipt = r.LastGoodsReceipt
        }).ToList();

        return Result.Success<IReadOnlyList<MaterialInventoryResponse>>(result);
    }
    
    private class MaterialInventoryRow
    {
        public Guid MaterialId { get; set; }
        public Guid InventoryId { get; set; }
        public string Code { get; set; } = null!;
        public string Name { get; set; } = null!;
        public string Description { get; set; } = null!;
        public string Type { get; set; } = null!;
        public string DefaultUnit { get; set; } = null!;
        public string MaterialStatus { get; set; } = null!;
        public string Location { get; set; } = null!;
        public double CurrentStock { get; set; }
        public double ReservedStock { get; set; }
        public double MinimumStockLevel { get; set; }
        public double ReorderPoint { get; set; }
        public int ActiveReservationsCount { get; set; }
        public DateTime? LastStockMovement { get; set; }
        public DateTime? LastGoodsReceipt { get; set; }
    }
}
