namespace Tebipaints.Application.Inventories.ListMaterialInventory;

public class MaterialInventoryResponse
{
    // Core Identity
    public Guid MaterialId { get; set; }
    public Guid InventoryId { get; set; }
    public string Code { get; set; } = null!;
    public string Name { get; set; } = null!;
    public string Description { get; set; } = null!;

    // Material Properties
    public string Type { get; set; } = null!;
    public string DefaultUnit { get; set; } = null!;
    public string MaterialStatus { get; set; } = null!;
    public string Location { get; set; } = null!;

    // Stock Levels
    public double CurrentStock { get; set; }
    public double ReservedStock { get; set; }
    public double AvailableStock { get; set; }

    // Reorder Management (Critical Addition)
    public double MinimumStockLevel { get; set; }
    public double ReorderPoint { get; set; }
    public bool IsLowStock { get; set; }

    // Activity Metrics (High Value Addition)
    public int ActiveReservationsCount { get; set; }
    public DateTime? LastStockMovement { get; set; }
    public DateTime? LastGoodsReceipt { get; set; }

    // Computed Properties
    public bool RequiresReorder => IsLowStock;
    public bool HasRecentActivity => LastStockMovement.HasValue &&
                                   LastStockMovement.Value > DateTime.UtcNow.AddDays(-30);
    public bool IsStaleInventory => !HasRecentActivity && CurrentStock > 0;
}