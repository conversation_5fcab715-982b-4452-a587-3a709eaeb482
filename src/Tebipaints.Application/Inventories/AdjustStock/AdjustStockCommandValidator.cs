using FluentValidation;

namespace Tebipaints.Application.Inventories.AdjustStock;

public class AdjustStockCommandValidator : AbstractValidator<AdjustStockCommand>
{
    public AdjustStockCommandValidator()
    {
        RuleFor(a => a.InventoryId).NotEmpty();
        RuleFor(a => a.VariantId).NotEmpty();
        RuleFor(a => a.Packaging).NotEmpty();
        RuleFor(a => a.Reason).NotEmpty();
        RuleFor(a => a.NewQuantity).NotEmpty().GreaterThan(0);
    }
}