using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Inventory;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Inventories.AdjustStock;

public class AdjustStockCommandHandler : ICommandHandler<AdjustStockCommand>
{
    private readonly IProductInventoryRepository _inventoryRepository;
    private readonly IUnitOfWork _unitOfWork;

    public AdjustStockCommandHandler(
        IProductInventoryRepository inventoryRepository,
        IUnitOfWork unitOfWork)
    {
        _inventoryRepository = inventoryRepository;
        _unitOfWork = unitOfWork;
    }


    public async Task<Result> Handle(AdjustStockCommand request, CancellationToken cancellationToken)
    {
        var unitOfMeasure = UnitOfMeasureConverter.FromString(request.Packaging.Unit);
        if (unitOfMeasure.IsFailure)
        {
            return Result.Failure(unitOfMeasure.Error);
        }
        var capacity = new Measurement(request.Packaging.Capacity, unitOfMeasure.Value);

        var currency = Currency.FromCode(request.Packaging.Currency);
        var price = Money.FromDecimal(request.Packaging.UnitPrice, currency);

        if (!Enum.TryParse<PackagingType>(request.Packaging.PackagingType, true, out var packagingType))
        {
            return Result.Failure(InventoryErrors.InvalidPackagingType());
        }
        
        var packagingOption = PackagingOption.Create(
            packagingType,
            request.Packaging.Material,
            capacity,
            price);
        
        var inventory = await _inventoryRepository
            .GetByIdAsync(request.InventoryId, cancellationToken);

        if (inventory is null)
        {
            return Result.Failure(InventoryErrors.NotFound(request.InventoryId));
        }

        var variantPackaging = VariantPackaging.Create(
            request.VariantId,
            packagingOption.Value.Type,
            packagingOption.Value.Capacity);
        
        var currentStock = inventory.VariantStockLevels.GetValueOrDefault(variantPackaging, 0);
        
        if (currentStock == request.NewQuantity)
        {
            return Result.Success(); // No adjustment needed
        }
        
        if (request.NewQuantity > currentStock)
        {
            var addResult = inventory.AddVariantStock(
                request.VariantId,
                packagingOption.Value,
                request.NewQuantity - currentStock,
                $"Stock Adjustment: {request.Reason}");

            if (addResult.IsFailure)
            {
                return addResult;
            }
        }
        else
        {
            var removeResult = inventory.RemoveVariantStock(
                request.VariantId,
                packagingOption.Value,
                currentStock - request.NewQuantity,
                $"Stock Adjustment: {request.Reason}");

            if (removeResult.IsFailure)
            {
                return removeResult;
            }
        }
        
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}