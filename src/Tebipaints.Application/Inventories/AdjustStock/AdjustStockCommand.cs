using Tebipaints.Application.Abstractions.Messaging;

namespace Tebipaints.Application.Inventories.AdjustStock;

public record AdjustStockCommand(
    Guid InventoryId,
    Guid VariantId,
    PackagingOptionDto Packaging,
    int NewQuantity,
    string Reason) : ICommand;
    
public record PackagingOptionDto(
    string PackagingType,
    string Material,
    decimal Capacity,
    string Unit,
    decimal UnitPrice,
    string Currency);