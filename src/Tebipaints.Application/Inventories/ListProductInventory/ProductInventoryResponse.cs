namespace Tebipaints.Application.Inventories.ListProductInventory;

using System.Collections.Generic;

public class ProductInventoryResponse
{
    public string ProductName { get; set; } = null!;
    public string Sku { get; set; } = null!;
    public string Color { get; set; } = null!;
    public List<ProductVariantInventoryDto> Variants { get; set; } = new();
    public int TotalQuantity { get; set; }
    public string Location { get; set; } = null!;
}

public class ProductVariantInventoryDto
{
    public Guid VariantId { get; set; }
    public string VariantSku { get; set; } = null!;
    public string VariantColor { get; set; } = null!;
    public decimal VariantVolumeValue { get; set; }
    public string VariantVolumeUnit { get; set; } = null!;
    public string PackagingType { get; set; } = null!;
    public decimal PackagingCapacityValue { get; set; }
    public string PackagingCapacityUnit { get; set; } = null!;
    public int StockLevel { get; set; }
}