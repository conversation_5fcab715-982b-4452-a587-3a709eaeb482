using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Application.Inventories.ListProductInventory;

public class ListProductInventoryQueryHandler : IQueryHandler<ListProductInventoryQuery, IReadOnlyList<ProductInventoryResponse>>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;

    public ListProductInventoryQueryHandler(ISqlConnectionFactory sqlConnectionFactory)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
    }

    public async Task<Result<IReadOnlyList<ProductInventoryResponse>>> Handle(ListProductInventoryQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();

        const string sql = @"
            SELECT 
                p.name_value AS ""ProductName"",
                p.sku_value AS ""ProductSku"",
                p.color_value AS ""ProductColor"",
                inv.location AS ""Location"",
                pvsl.variant_packaging_variant_id AS ""VariantId"",
                pv.sku_value AS ""VariantSku"",
                pv.color_value AS ""VariantColor"",
                pv.volume_value AS ""VariantVolumeValue"",
                pv.volume_unit AS ""VariantVolumeUnit"",
                pvsl.variant_packaging_type AS ""PackagingType"",
                pvsl.variant_packaging_capacity_value AS ""PackagingCapacityValue"",
                pvsl.variant_packaging_capacity_unit AS ""PackagingCapacityUnit"",
                pvsl.stock_level AS ""StockLevel""
            FROM product_inventories pi
            INNER JOIN products p ON pi.product_id = p.id
            INNER JOIN inventory_int inv ON pi.id = inv.id
            LEFT JOIN product_variant_stock_levels pvsl ON pvsl.inventory_id = pi.id
            LEFT JOIN product_variants pv ON pvsl.variant_packaging_variant_id = pv.id
            ORDER BY p.name_value, inv.location
        ";

        var rows = (await connection.QueryAsync<RawProductInventoryRow>(sql)).ToList();

        var grouped = rows
            .GroupBy(r => new
            {
                r.ProductName,
                r.ProductSku,
                r.ProductColor,
                r.Location
            })
            .Select(g =>
            {
                var variants = g
                    .Where(x => x.VariantId != null)
                    .Select(x => new ProductVariantInventoryDto
                    {
                        VariantId = x.VariantId,
                        VariantSku = x.VariantSku,
                        VariantColor = x.VariantColor,
                        VariantVolumeValue = x.VariantVolumeValue,
                        VariantVolumeUnit = x.VariantVolumeUnit,
                        PackagingType = x.PackagingType,
                        PackagingCapacityValue = x.PackagingCapacityValue,
                        PackagingCapacityUnit = x.PackagingCapacityUnit,
                        StockLevel = x.StockLevel
                    })
                    .ToList();

                return new ProductInventoryResponse
                {
                    ProductName = g.Key.ProductName,
                    Sku = g.Key.ProductSku,
                    Color = g.Key.ProductColor,
                    Location = g.Key.Location,
                    Variants = variants,
                    TotalQuantity = variants.Sum(v => v.StockLevel)
                };
            })
            .ToList();


        return Result.Success<IReadOnlyList<ProductInventoryResponse>>(grouped);
    }
}

public class RawProductInventoryRow
{
    public string ProductName { get; set; }
    public string ProductSku { get; set; }
    public string ProductColor { get; set; }
    public string Location { get; set; }
    public Guid VariantId { get; set; }
    public string VariantSku { get; set; }
    public string VariantColor { get; set; }
    public decimal VariantVolumeValue { get; set; }
    public string VariantVolumeUnit { get; set; }
    public string PackagingType { get; set; }
    public decimal PackagingCapacityValue { get; set; }
    public string PackagingCapacityUnit { get; set; }
    public int StockLevel { get; set; }
}
