using FluentValidation;

namespace Tebipaints.Application.Inventories.TransferStock;

public class TransferStockCommandValidator : AbstractValidator<TransferStockCommand>
{
    public TransferStockCommandValidator()
    {
        RuleFor(t => t.SourceInventoryId).NotEmpty();
        RuleFor(t => t.DestinationInventoryId).NotEmpty();
        RuleFor(t => t.VariantId).NotEmpty();
        RuleFor(t => t.Quantity).NotEmpty().GreaterThan(0);
        RuleFor(t => t.Packaging).NotEmpty();
        RuleFor(t => t.Reference).NotEmpty();
    }
}