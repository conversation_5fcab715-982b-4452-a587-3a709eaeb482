using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Application.Inventories.AdjustStock;
using Tebipaints.Domain.Product;

namespace Tebipaints.Application.Inventories.TransferStock;

public record TransferStockCommand(
    Guid SourceInventoryId,
    Guid DestinationInventoryId,
    Guid VariantId,
    PackagingOptionDto Packaging,
    int Quantity,
    string Reference) : ICommand;