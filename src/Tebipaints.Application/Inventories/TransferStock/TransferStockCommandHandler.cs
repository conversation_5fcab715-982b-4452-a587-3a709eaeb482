using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Inventory;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Inventories.TransferStock;

public class TransferStockCommandHandler : ICommandHandler<TransferStockCommand>
{
    private readonly IProductInventoryRepository _inventoryRepository;
    private readonly IUnitOfWork _unitOfWork;

    public TransferStockCommandHandler(
        IProductInventoryRepository inventoryRepository,
        IUnitOfWork unitOfWork)
    {
        _inventoryRepository = inventoryRepository;
        _unitOfWork = unitOfWork;
    }


    public async Task<Result> Handle(TransferStockCommand request, CancellationToken cancellationToken)
    {
        var unitOfMeasure = UnitOfMeasureConverter.FromString(request.Packaging.Unit);
        if (unitOfMeasure.IsFailure)
        {
            return Result.Failure(unitOfMeasure.Error);
        }
        var capacity = new Measurement(request.Packaging.Capacity, unitOfMeasure.Value);

        var currency = Currency.FromCode(request.Packaging.Currency);
        var price = Money.FromDecimal(request.Packaging.UnitPrice, currency);

        if (!Enum.TryParse<PackagingType>(request.Packaging.PackagingType, true, out var packagingType))
        {
            return Result.Failure(InventoryErrors.InvalidPackagingType());
        }
        
        var packagingOption = PackagingOption.Create(
            packagingType,
            request.Packaging.Material,
            capacity,
            price);
        
        var sourceInventory = await _inventoryRepository
            .GetByIdAsync(request.SourceInventoryId, cancellationToken);

        if (sourceInventory is null)
        {
            return Result.Failure(InventoryErrors.NotFound(request.SourceInventoryId));
        }

        var destinationInventory = await _inventoryRepository
            .GetByIdAsync(request.DestinationInventoryId, cancellationToken);

        if (destinationInventory is null)
        {
            return Result.Failure(InventoryErrors.NotFound(request.DestinationInventoryId));
        }
        
        var result = sourceInventory.TransferVariantStock(
            destinationInventory,
            request.VariantId,
            packagingOption.Value,
            request.Quantity,
            request.Reference);

        if (result.IsFailure)
        {
            return result;
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}