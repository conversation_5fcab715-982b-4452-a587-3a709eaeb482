using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Inventory;

namespace Tebipaints.Application.Inventories.UpdateReorderPoints;

public class UpdateReorderPointsCommandHandler : ICommandHandler<UpdateReorderPointsCommand>
{
    private readonly IProductInventoryRepository _inventoryRepository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateReorderPointsCommandHandler(
        IProductInventoryRepository inventoryRepository,
        IUnitOfWork unitOfWork)
    {
        _inventoryRepository = inventoryRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(
        UpdateReorderPointsCommand command,
        CancellationToken cancellationToken)
    {
        var inventory = await _inventoryRepository
            .GetByIdAsync(command.InventoryId, cancellationToken);

        if (inventory is null)
        {
            return Result.Failure(InventoryErrors.NotFound(command.InventoryId));
        }

        var result = inventory.UpdateReorderPoints(
            command.MinimumStockLevel,
            command.ReorderPoint);

        if (result.IsFailure)
        {
            return result;
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}