using FluentValidation;

namespace Tebipaints.Application.Inventories.UpdateReorderPoints;

public class UpdateReorderPointsCommandValidator : AbstractValidator<UpdateReorderPointsCommand>
{
    public UpdateReorderPointsCommandValidator()
    {
        RuleFor(u => u.InventoryId).NotEmpty();
        RuleFor(u => u.MinimumStockLevel).NotEmpty();
        RuleFor(u => u.ReorderPoint).NotEmpty();
    }
}