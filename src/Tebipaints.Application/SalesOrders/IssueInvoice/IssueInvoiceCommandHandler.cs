using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Invoice;
using Tebipaints.Domain.Invoice.Services;
using Tebipaints.Domain.SalesOrder;

namespace Tebipaints.Application.SalesOrders.IssueInvoice;

public class IssueInvoiceCommandHandler : ICommandHandler<IssueInvoiceCommand, Guid>
{
    private readonly ISalesOrderRepository _salesOrderRepository;
    private readonly IUnitOfWork _unitOfWork;

    public IssueInvoiceCommandHandler(
        ISalesOrderRepository salesOrderRepository,
        IInvoiceRepository invoiceRepository,
        IUnitOfWork unitOfWork)
    {
        _salesOrderRepository = salesOrderRepository;
        _unitOfWork = unitOfWork;
    }
    
    public async Task<Result<Guid>> Handle(IssueInvoiceCommand request, CancellationToken cancellationToken)
    {
        var salesOrder = await _salesOrderRepository
            .GetByIdAsync(request.SalesOrderId, cancellationToken);

        if (salesOrder is null)
        {
            return Result.Failure<Guid>(
                SalesOrderErrors.NotFound(request.SalesOrderId));
        }

        var issueResult = salesOrder.IssueInvoice();
        if (issueResult.IsFailure)
        {
            return Result.Failure<Guid>(issueResult.Error);
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return Result.Success(salesOrder.Id);
    }
}