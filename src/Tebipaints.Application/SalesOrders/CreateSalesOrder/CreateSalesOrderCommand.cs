using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.SalesOrders.CreateSalesOrder;

public record CreateSalesOrderCommand(
    List<OrderLineItemDto> LineItems,
    List<DiscountDto>? Discounts = null) : ICommand<Guid>
{
    public Guid? CustomerId { get; init; }
    public string? WalkInCustomerName { get; init; }
}

public sealed record OrderLineItemDto(
    Guid ProductId,
    string Sku,
    string Description,
    int Quantity,
    decimal UnitPrice,
    string Currency);

public sealed record DiscountDto(
    Guid DiscountPolicyId,
    Guid? LineItemId = null); 