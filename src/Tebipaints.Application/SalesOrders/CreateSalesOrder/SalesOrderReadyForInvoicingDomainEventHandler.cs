using MediatR;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Invoice;
using Tebipaints.Domain.Invoice.Services;
using Tebipaints.Domain.SalesOrder;
using Tebipaints.Domain.SalesOrder.Events;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.SalesOrders.CreateSalesOrder;

public class SalesOrderReadyForInvoicingDomainEventHandler : INotificationHandler<SalesOrderReadyForInvoicingDomainEvent>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IInvoiceRepository _invoiceRepository;
    private readonly IDocumentNumberGenerator _documentNumberGenerator;
    private readonly ISalesOrderRepository _salesOrderRepository;
    private readonly InvoiceTotalsCalculationService _totalsCalculationService;

    public SalesOrderReadyForInvoicingDomainEventHandler(
        IInvoiceRepository invoiceRepository,
        IUnitOfWork unitOfWork,
        ISalesOrderRepository salesOrderRepository,
        InvoiceTotalsCalculationService totalsCalculationService, IDocumentNumberGenerator documentNumberGenerator)
    {
        _invoiceRepository = invoiceRepository;
        _unitOfWork = unitOfWork;
        _salesOrderRepository = salesOrderRepository;
        _totalsCalculationService = totalsCalculationService;
        _documentNumberGenerator = documentNumberGenerator;
    }

    public async Task Handle(
    SalesOrderReadyForInvoicingDomainEvent notification,
    CancellationToken cancellationToken)
{
    var invoiceNumber = await _documentNumberGenerator.GetNextInvoiceNumberAsync(cancellationToken);

    // Convert to invoice line items - line items already include their discounts
    var lineItems = new List<LineItem>();
    foreach (var item in notification.LineItems)
    {
        var lineItemResult = LineItem.Create(
            item.ProductId,
            item.Sku,
            item.Description,
            item.UnitPrice,
            item.Quantity,
            item.LineItemDiscount);  // Pass the already calculated discount

        if (lineItemResult.IsFailure)
        {
            return;
        }

        lineItems.Add(lineItemResult.Value);
    }

    var dueDate = DateTime.UtcNow.AddDays(30);

    var invoiceResult = Invoice.Create(
        invoiceId: null,
        customerId: notification.CustomerId,
        walkInCustomerName: notification.WalkInCustomerName,
        invoiceNumber,
        amountPaid: 0,
        taxRate: 0.15m, // Could be configurable
        dueDate,
        notification.OrderLevelDiscount,  // Pass the already calculated order-level discount
        lineItems,
        _totalsCalculationService);

    if (invoiceResult.IsSuccess)
    {
        var invoice = invoiceResult.Value;
        var issueResult = invoice.IssueInvoice(dueDate);
        
        if (issueResult.IsSuccess)
        {
            var salesOrder = await _salesOrderRepository
                .GetByIdAsync(notification.SalesOrderId, cancellationToken);

            if (salesOrder is not null)
            {
                var setInvoiceResult = salesOrder.SetInvoiceId(invoice.Id);
                if (setInvoiceResult.IsSuccess)
                {
                    _invoiceRepository.Add(invoice);
                    _salesOrderRepository.UpdateAsync(salesOrder, cancellationToken);
                    await _unitOfWork.SaveChangesAsync(cancellationToken);
                }
            }
        }
    }
}
}