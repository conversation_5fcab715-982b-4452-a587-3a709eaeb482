using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Inventory;
using Tebipaints.Domain.SalesOrder;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.SalesOrders.CreateSalesOrder;

public class CreateSalesOrderCommandHandler : ICommandHandler<CreateSalesOrderCommand, Guid>
{
    private readonly ISalesOrderRepository _salesOrderRepository;
    private readonly IDiscountPolicyRepository _discountPolicyRepository;
    private readonly IDocumentNumberGenerator _documentNumberGenerator;
    private readonly IUnitOfWork _unitOfWork;

    public CreateSalesOrderCommandHandler(
        ISalesOrderRepository salesOrderRepository,
        IUnitOfWork unitOfWork, IDiscountPolicyRepository discountPolicyRepository, IDocumentNumberGenerator documentNumberGenerator)
    {
        _salesOrderRepository = salesOrderRepository;
        _unitOfWork = unitOfWork;
        _discountPolicyRepository = discountPolicyRepository;
        _documentNumberGenerator = documentNumberGenerator;
    }
    public async Task<Result<Guid>> Handle(CreateSalesOrderCommand request, CancellationToken cancellationToken)
    {
        // Validate that either CustomerId or WalkInCustomerName is provided, but not both
        if (request.CustomerId.HasValue && !string.IsNullOrWhiteSpace(request.WalkInCustomerName))
        {
            return Result.Failure<Guid>(
                new Error(
                    "CreateSalesOrder.InvalidCustomerInfo",
                    "Cannot specify both CustomerId and WalkInCustomerName"));
        }

        if (!request.CustomerId.HasValue && string.IsNullOrWhiteSpace(request.WalkInCustomerName))
        {
            return Result.Failure<Guid>(
                new Error(
                    "CreateSalesOrder.MissingCustomerInfo",
                    "Either CustomerId or WalkInCustomerName must be provided"));
        }
        
        // Fetch next order number sequence
        var orderNumber = await _documentNumberGenerator.GetNextSalesOrderNumberAsync(cancellationToken);

        // Convert DTOs to domain objects
        var lineItems = new List<SalesOrderLineItem>();
        foreach (var item in request.LineItems)
        {
            var sku = SKU.TryParse(item.Sku, out var skuResult);
            if (!sku)
            {
                return Result.Failure<Guid>(SalesOrderErrors.InvalidSku());
            }

            if (skuResult is null)
            {
                return Result.Failure<Guid>(SalesOrderErrors.InvalidSku());
            }

            var lineItem = SalesOrderLineItem.Create(
                item.ProductId,
                skuResult,
                item.Description,
                item.Quantity,
                new Money(item.UnitPrice, Currency.FromCode(item.Currency)));
            
            if (lineItem.IsFailure)
            {
                return Result.Failure<Guid>(lineItem.Error);
            }
            
            lineItems.Add(lineItem.Value);
        }
        
        // Get all discount policies first
        var discountPolicyIds = request.Discounts
            .Select(d => d.DiscountPolicyId)
            .Distinct()
            .ToList();

        var discountPolicies = await _discountPolicyRepository.GetByIdsAsync(
            discountPolicyIds, cancellationToken);

        // Create sales order
        Result<SalesOrder> salesOrderResult;
        if (request.CustomerId.HasValue)
        {
            salesOrderResult = SalesOrder.Create(
                request.CustomerId.Value,
                orderNumber,
                lineItems);
        }
        else
        {
            salesOrderResult = SalesOrder.CreateForWalkInCustomer(
                request.WalkInCustomerName!,
                orderNumber,
                lineItems);
        }
        
        if (salesOrderResult.IsFailure)
        {
            return Result.Failure<Guid>(salesOrderResult.Error);
        }

        var salesOrder = salesOrderResult.Value;

        // Apply discounts if any
        if (request.Discounts?.Any() == true)
        {
            foreach (var discountDto in request.Discounts)
            {
                var policy = discountPolicies.FirstOrDefault(
                    p => p.Id == discountDto.DiscountPolicyId);
                
                if (policy is null || !policy.IsValid(DateTime.UtcNow))
                {
                    continue; // Skip invalid or expired discounts
                }

                Result discountResult;
                if (discountDto.LineItemId.HasValue)
                {
                    // Apply line item discount
                    discountResult = salesOrder.ApplyLineItemDiscount(
                        discountDto.LineItemId.Value,
                        policy);
                }
                else
                {
                    // Apply order level discount
                    discountResult = salesOrder.ApplyOrderLevelDiscount(policy);
                }

                if (discountResult.IsFailure)
                {
                    return Result.Failure<Guid>(discountResult.Error);
                }
            }
        }
        
        _salesOrderRepository.Add(salesOrder);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success(salesOrder.Id);
    }
}