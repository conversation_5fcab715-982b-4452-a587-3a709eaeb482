using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Invoice;
using Tebipaints.Domain.SalesOrder;

namespace Tebipaints.Application.SalesOrders.GetSalesOrder;

internal sealed class GetSalesOrderQueryHandler : IQueryHandler<GetSalesOrderQuery, SalesOrderResponse>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;

    public GetSalesOrderQueryHandler(ISqlConnectionFactory connectionFactory)
    {
        _sqlConnectionFactory = connectionFactory;
    }

    public async Task<Result<SalesOrderResponse>> Handle(GetSalesOrderQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();

        const string sql = """
                           SELECT 
                               so.Id,
                               so.OrderNumber,
                               so.CustomerId,
                               so.WalkInCustomerName,
                               so.Status,
                               so.CreatedOnUtc,
                               so.FulfilledOnUtc,
                               so.InvoiceId,
                               so.SubTotal,
                               so.OrderLevelDiscount,
                               so.Total,
                               -- Line Items
                               li.Id as LineItemId,
                               li.ProductId,
                               li.Sku,
                               li.Description,
                               li.Quantity,
                               li.UnitPrice,
                               li.LineItemDiscount,
                               li.LineTotal,
                               -- Discounts
                               d.Name as DiscountName,
                               d.Type as DiscountType,
                               d.Percentage as DiscountPercentage,
                               d.ValidFrom as DiscountValidFrom,
                               d.ValidTo as DiscountValidTo
                           FROM SalesOrders so
                           LEFT JOIN SalesOrderLineItems li ON so.Id = li.SalesOrderId
                           LEFT JOIN SalesOrderDiscounts sod ON so.Id = sod.SalesOrderId
                           LEFT JOIN Discounts d ON sod.DiscountId = d.Id
                           WHERE so.Id = @Id
                           """;

        var orderDictionary = new Dictionary<Guid, SalesOrderResponse>();

        await connection.QueryAsync<
            SalesOrderResponse,
            SalesOrderLineItemResponse,
            DiscountResponse,
            SalesOrderResponse>(
            sql,
            (order, lineItem, discount) =>
            {
                if (!orderDictionary.TryGetValue(order.Id, out var existingOrder))
                {
                    existingOrder = order with
                    {
                        LineItems = new List<SalesOrderLineItemResponse>(),
                        AppliedDiscounts = new List<DiscountResponse>()
                    };
                    orderDictionary.Add(order.Id, existingOrder);
                }
                
                if (lineItem is not null)
                {
                    existingOrder.LineItems.Add(lineItem);
                }

                if (discount is not null)
                {
                    existingOrder.AppliedDiscounts.Add(discount);
                }
                
                return existingOrder;
            },
            new { request.Id },
            splitOn: "LineItemId,DiscountName");
        
        var salesOrder = orderDictionary.Values.FirstOrDefault();
        
        if (salesOrder is null)
        {
            return Result.Failure<SalesOrderResponse>(
                SalesOrderErrors.NotFound(request.Id));
        }
        
        return Result.Success(salesOrder);
    }
}