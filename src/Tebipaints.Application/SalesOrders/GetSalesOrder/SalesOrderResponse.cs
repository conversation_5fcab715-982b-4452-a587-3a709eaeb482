namespace Tebipaints.Application.SalesOrders.GetSalesOrder;

public sealed record SalesOrderResponse(
    Guid Id,
    string OrderNumber,
    Guid? CustomerId,
    string? WalkInCustomerName,
    string Status,
    DateTime CreatedOnUtc,
    DateTime? FulfilledOnUtc,
    Guid? InvoiceId,
    decimal SubTotal,
    decimal OrderLevelDiscount,
    decimal Total,
    List<SalesOrderLineItemResponse> LineItems,
    List<DiscountResponse> AppliedDiscounts);
    
public sealed record SalesOrderLineItemResponse(
    Guid Id,
    Guid ProductId,
    string Sku,
    string Description,
    int Quantity,
    decimal UnitPrice,
    decimal LineItemDiscount,
    decimal LineTotal);

public sealed record DiscountResponse(
    string Name,
    string Type,
    decimal Percentage,
    DateTime? ValidFrom,
    DateTime? ValidTo);