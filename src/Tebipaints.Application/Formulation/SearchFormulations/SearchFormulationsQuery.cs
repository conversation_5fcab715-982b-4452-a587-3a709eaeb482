using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Application.Formulation.ListFormulations;

namespace Tebipaints.Application.Formulation.SearchFormulations;

public record SearchFormulationsQuery(
    string? SearchTerm = null,
    string? Status = null,
    Guid? MaterialId = null,
    int? MinShelfLife = null,
    int? MaxShelfLife = null,
    DateTime? CreatedAfter = null,
    DateTime? CreatedBefore = null,
    int PageNumber = 1,
    int PageSize = 20) : IQuery<SearchFormulationsResponse>;

public record SearchFormulationsResponse(
    IReadOnlyList<FormulationSummary> Formulations,
    int TotalCount,
    int PageNumber,
    int PageSize,
    int TotalPages);

public record FormulationSummary(
    Guid Id,
    string Name,
    string? Description,
    string Status,
    int ShelfLife,
    TimeSpan EstimatedProductionTime,
    int IngredientCount,
    int VersionCount,
    DateTime? LastModified);
