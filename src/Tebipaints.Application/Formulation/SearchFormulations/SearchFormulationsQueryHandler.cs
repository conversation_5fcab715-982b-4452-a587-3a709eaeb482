using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Application.Formulation.SearchFormulations;

public class SearchFormulationsQueryHandler : IQueryHandler<SearchFormulationsQuery, SearchFormulationsResponse>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;

    public SearchFormulationsQueryHandler(ISqlConnectionFactory sqlConnectionFactory)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
    }

    public async Task<Result<SearchFormulationsResponse>> Handle(
        SearchFormulationsQuery request,
        CancellationToken cancellationToken)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();

        var whereConditions = new List<string>();
        var parameters = new DynamicParameters();

        // Build dynamic WHERE clause based on search criteria
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            whereConditions.Add("(f.name ILIKE @SearchTerm OR f.description ILIKE @SearchTerm)");
            parameters.Add("SearchTerm", $"%{request.SearchTerm}%");
        }

        if (!string.IsNullOrWhiteSpace(request.Status))
        {
            whereConditions.Add("f.status = @Status");
            parameters.Add("Status", request.Status);
        }

        if (request.MaterialId.HasValue)
        {
            whereConditions.Add("EXISTS (SELECT 1 FROM ingredients i WHERE i.formulation_id = f.id AND i.material_id = @MaterialId)");
            parameters.Add("MaterialId", request.MaterialId.Value);
        }

        if (request.MinShelfLife.HasValue)
        {
            whereConditions.Add("f.shelf_life >= @MinShelfLife");
            parameters.Add("MinShelfLife", request.MinShelfLife.Value);
        }

        if (request.MaxShelfLife.HasValue)
        {
            whereConditions.Add("f.shelf_life <= @MaxShelfLife");
            parameters.Add("MaxShelfLife", request.MaxShelfLife.Value);
        }

        if (request.CreatedAfter.HasValue)
        {
            whereConditions.Add("EXISTS (SELECT 1 FROM versions v WHERE v.formulation_id = f.id AND v.time_stamp >= @CreatedAfter)");
            parameters.Add("CreatedAfter", request.CreatedAfter.Value);
        }

        if (request.CreatedBefore.HasValue)
        {
            whereConditions.Add("EXISTS (SELECT 1 FROM versions v WHERE v.formulation_id = f.id AND v.time_stamp <= @CreatedBefore)");
            parameters.Add("CreatedBefore", request.CreatedBefore.Value);
        }

        var whereClause = whereConditions.Any() ? "WHERE " + string.Join(" AND ", whereConditions) : "";

        // Count query for pagination
        var countSql = $"""
            SELECT COUNT(DISTINCT f.id)
            FROM formulations f
            {whereClause}
            """;

        var totalCount = await connection.QuerySingleAsync<int>(countSql, parameters);

        // Calculate pagination
        var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);
        var offset = (request.PageNumber - 1) * request.PageSize;

        parameters.Add("Offset", offset);
        parameters.Add("PageSize", request.PageSize);

        // Main query with pagination
        var sql = $"""
            SELECT 
                f.id as Id,
                f.name as Name,
                f.description as Description,
                f.status as Status,
                f.shelf_life as ShelfLife,
                f.estimated_production_time as EstimatedProductionTime,
                COALESCE(ingredient_counts.ingredient_count, 0) as IngredientCount,
                COALESCE(version_counts.version_count, 0) as VersionCount,
                version_counts.last_modified as LastModified
            FROM formulations f
            LEFT JOIN (
                SELECT 
                    formulation_id, 
                    COUNT(*) as ingredient_count
                FROM ingredients 
                GROUP BY formulation_id
            ) ingredient_counts ON ingredient_counts.formulation_id = f.id
            LEFT JOIN (
                SELECT 
                    formulation_id, 
                    COUNT(*) as version_count,
                    MAX(time_stamp) as last_modified
                FROM versions 
                GROUP BY formulation_id
            ) version_counts ON version_counts.formulation_id = f.id
            {whereClause}
            ORDER BY f.name
            OFFSET @Offset LIMIT @PageSize
            """;

        var formulations = await connection.QueryAsync<FormulationSummary>(sql, parameters);

        var response = new SearchFormulationsResponse(
            formulations.ToList(),
            totalCount,
            request.PageNumber,
            request.PageSize,
            totalPages);

        return Result.Success(response);
    }
}
