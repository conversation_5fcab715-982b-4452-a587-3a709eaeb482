namespace Tebipaints.Application.Formulation.ListFormulations;

public class FormulationResponse
{
    public Guid Id { get; init; }
    public string Name { get; init; } = string.Empty;
    public string? Description { get; init; }
    public int ShelfLife { get; init; }
    public TimeSpan EstimatedProductionTime { get; init; }
    public string Status { get; init; } = string.Empty;
    public FormulationCostResponse? EstimatedCost { get; set; }
    public FormulationEfficiencyMetricsResponse? EfficiencyMetrics { get; set; }

    public List<FormulationIngredientResponse> Ingredients { get; set; } = new();
    public List<VersionResponse> Versions { get; set; } = new();
    public List<string> Instructions { get; init; } = new();
}

public class FormulationIngredientResponse
{
    public Guid IngredientId { get; init; }
    public Guid MaterialId { get; init; }
    public decimal QuantityValue { get; init; }
    public string QuantityUnit { get; init; } = string.Empty;
}

public class VersionResponse
{
    public Guid VersionId { get; init; }
    public DateTime VersionTimestamp { get; init; }
    public bool IsFinalized { get; init; }
    public SnapshotResponse Snapshot { get; set; } = new();
    public ChangeLogResponse? ChangeLog { get; set; }
}

public class SnapshotResponse
{
    public decimal SnapshotMinimumProductionQuantityValue { get; init; }
    public string SnapshotMinimumProductionQuantityUnit { get; init; } = string.Empty;
    public TimeSpan SnapshotEstimatedProductionTime { get; init; }
    public int SnapshotShelfLife { get; init; }
    public string? SnapshotInstructions { get; init; }

    public List<SnapshotIngredientResponse> Ingredients { get; set; } = new();
}

public class SnapshotIngredientResponse
{
    public Guid SnapshotIngredientId { get; init; }
    public Guid RawMaterialId { get; init; }
    public decimal SnapshotIngredientQuantityValue { get; init; }
    public string SnapshotIngredientQuantityUnit { get; init; } = string.Empty;
}

public class ChangeLogResponse
{
    public Guid ChangeLogId { get; init; }
    public List<ChangeLogEntryResponse> Entries { get; set; } = new();
}

public class ChangeLogEntryResponse
{
    public Guid ChangeLogEntryId { get; init; }
    public DateTime ChangeLogEntryTimestamp { get; init; }
    public string ChangeLogEntryDescription { get; init; } = string.Empty;
    public string ChangeLogEntryAuthor { get; init; } = string.Empty;
}

public class FormulationCostResponse
{
    public decimal MaterialCostAmount { get; init; }
    public string MaterialCostCurrency { get; init; } = string.Empty;
    public decimal LaborCostAmount { get; init; }
    public string LaborCostCurrency { get; init; } = string.Empty;
    public decimal OverheadCostAmount { get; init; }
    public string OverheadCostCurrency { get; init; } = string.Empty;
    public decimal TotalCostAmount { get; init; }
    public string TotalCostCurrency { get; init; } = string.Empty;
    public decimal CostPerUnitAmount { get; init; }
    public string CostPerUnitCurrency { get; init; } = string.Empty;
    public DateTime CostCalculatedAt { get; init; }
    public string CostCalculationBasis { get; init; } = string.Empty;
}

public class FormulationEfficiencyMetricsResponse
{
    // Basic Cost Efficiency
    public decimal? CostVariancePercentage { get; init; } // Actual vs Estimated cost variance
    public decimal? CostEfficiencyScore { get; init; } // 0-100 score

    // Basic Production Efficiency
    public decimal? TimeEfficiencyPercentage { get; init; } // Actual vs Estimated time efficiency
    public decimal? YieldEfficiencyPercentage { get; init; } // Actual vs Expected yield
    public decimal? ProductionEfficiencyScore { get; init; } // 0-100 score

    // Basic Quality Metrics
    public decimal? AverageYield { get; init; } // Average actual yield from completed batches
    public decimal? WastePercentage { get; init; } // Average waste/loss percentage

    // Summary
    public int TotalBatchesProduced { get; init; } // Number of completed production batches
    public DateTime? LastProductionDate { get; init; } // Last time this formulation was produced
    public string? PerformanceGrade { get; init; } // A, B, C, D, F based on overall efficiency
}