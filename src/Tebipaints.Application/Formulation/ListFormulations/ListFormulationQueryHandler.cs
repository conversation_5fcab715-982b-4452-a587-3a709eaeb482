using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Application.Formulation.Services;
using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Application.Formulation.ListFormulations;

public class ListFormulationQueryHandler : IQueryHandler<ListFormulationsQuery, IReadOnlyList<FormulationResponse>>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;
    private readonly IFormulationEfficiencyService _efficiencyService;

    public ListFormulationQueryHandler(
        ISqlConnectionFactory sqlConnectionFactory,
        IFormulationEfficiencyService efficiencyService)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
        _efficiencyService = efficiencyService;
    }

    public async Task<Result<IReadOnlyList<FormulationResponse>>> Handle(ListFormulationsQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();

        const string sql = """
                           SELECT
                               f.id                  as "Id",
                               f.name                as "Name",
                               f.description         as "Description",
                               f.shelf_life          as "ShelfLife",
                               f.estimated_production_time as "EstimatedProductionTime",
                               f.status              as "Status",
                               f.material_cost_amount as "MaterialCostAmount",
                               f.material_cost_currency as "MaterialCostCurrency",
                               f.labor_cost_amount as "LaborCostAmount",
                               f.labor_cost_currency as "LaborCostCurrency",
                               f.overhead_cost_amount as "OverheadCostAmount",
                               f.overhead_cost_currency as "OverheadCostCurrency",
                               f.total_cost_amount as "TotalCostAmount",
                               f.total_cost_currency as "TotalCostCurrency",
                               f.cost_per_unit_amount as "CostPerUnitAmount",
                               f.cost_per_unit_currency as "CostPerUnitCurrency",
                               f.cost_calculated_at as "CostCalculatedAt",
                               f.cost_calculation_basis as "CostCalculationBasis",
                               fi.id                 as "IngredientId",
                               fi.material_id        as "MaterialId",
                               fi.quantity_value     as "QuantityValue",
                               fi.quantity_unit      as "QuantityUnit",
                               v.id                  as "VersionId",
                               v.time_stamp          as "VersionTimestamp",
                               v.snapshot_minimum_production_quantity_value as "SnapshotMinimumProductionQuantityValue",
                               v.snapshot_minimum_production_quantity_unit as "SnapshotMinimumProductionQuantityUnit",
                               v.snapshot_estimated_production_time as "SnapshotEstimatedProductionTime",
                               v.snapshot_shelf_life as "SnapshotShelfLife",
                               v.snapshot_instructions as "SnapshotInstructions",
                               v.is_finalized        as "IsFinalized",
                               si.ingredient_id                 as "SnapshotIngredientId",
                               si.raw_material_id    as "RawMaterialId",
                               si.quantity_value     as "SnapshotIngredientQuantityValue",
                               si.quantity_unit      as "SnapshotIngredientQuantityUnit",
                               cl.id                 as "ChangeLogId",
                               cle.change_log_id                as "ChangeLogEntryId",
                               cle.time_stamp        as "ChangeLogEntryTimestamp",
                               cle.description       as "ChangeLogEntryDescription",
                               cle.author            as "ChangeLogEntryAuthor"
                           from formulations  f
                           LEFT JOIN ingredients fi ON fi.formulation_id = f.id
                           LEFT JOIN versions v ON v.formulation_id = f.id
                           LEFT JOIN ingredient_snapshot si ON si.formulation_snapshot_version_id = v.id
                           LEFT JOIN change_logs cl ON cl.version_id = v.id
                           LEFT JOIN change_log_entry cle ON cle.change_log_id = cl.id
                           ORDER BY
                               f.name,
                               f.id,
                               fi.id,
                               v.id,
                               si.id,
                               cl.id,
                               cle.id;
                           """;

        var formulationDictionary = new Dictionary<Guid, FormulationResponse>();
        var versionDictionary = new Dictionary<Guid, VersionResponse>();     // Track versions to add snapshot ingredients/changelogs
        var changeLogDictionary = new Dictionary<Guid, ChangeLogResponse>(); // Track changelogs to add entries

        // Dapper Multi-Mapping (max 7 generic parameters)
        await connection.QueryAsync<
            FormulationResponse,
            FormulationCostResponse?,
            FormulationIngredientResponse?,
            VersionResponse?,
            SnapshotResponse?,
            SnapshotIngredientResponse?,
            ChangeLogResponse?,
            ChangeLogEntryResponse?,
            FormulationResponse>(
            sql,
            (formulation, cost, formIngredient, version, snapshot, snapIngredient, changeLog, changeLogEntry) =>
            {
                // 1. Get or Add Formulation
                if (!formulationDictionary.TryGetValue(formulation.Id, out var currentFormulation))
                {
                    currentFormulation = formulation;
                    currentFormulation.EstimatedCost = cost; // Set the cost
                    currentFormulation.Ingredients = new List<FormulationIngredientResponse>();
                    currentFormulation.Versions = new List<VersionResponse>();
                    formulationDictionary.Add(currentFormulation.Id, currentFormulation);
                    versionDictionary.Clear(); // Clear version tracking for new formulation
                }

                // 2. Add Formulation Ingredient (if present and not duplicate)
                if (formIngredient != null && currentFormulation.Ingredients.All(fi => fi.IngredientId != formIngredient.IngredientId))
                {
                    currentFormulation.Ingredients.Add(formIngredient);
                }

                // 3. Add Version (if present and not duplicate)
                if (version != null && !versionDictionary.ContainsKey(version.VersionId))
                {
                    // Set the snapshot from Dapper mapping
                    if (snapshot != null)
                    {
                        version.Snapshot = snapshot;
                        version.Snapshot.Ingredients = new List<SnapshotIngredientResponse>();
                    }
                    else
                    {
                        version.Snapshot = new SnapshotResponse
                        {
                            Ingredients = new List<SnapshotIngredientResponse>()
                        };
                    }

                    currentFormulation.Versions.Add(version);
                    versionDictionary.Add(version.VersionId, version);
                    changeLogDictionary.Clear(); // Clear changelog tracking for new version
                }

                // Need the current version to add snapshot ingredients or changelogs
                VersionResponse? currentVersion = null;
                if (version != null)
                {
                    versionDictionary.TryGetValue(version.VersionId, out currentVersion);
                }

                // 4. Add Snapshot Ingredient (if present, version exists, and not duplicate)
                if (snapIngredient != null && currentVersion != null && currentVersion.Snapshot.Ingredients.All(si => si.SnapshotIngredientId != snapIngredient.SnapshotIngredientId))
                {
                    currentVersion.Snapshot.Ingredients.Add(snapIngredient);
                }

                // 5. Add ChangeLog (if present, version exists, and not duplicate)
                if (changeLog != null && currentVersion != null && !changeLogDictionary.ContainsKey(changeLog.ChangeLogId))
                {
                    // Ensure ChangeLog object is created if it doesn't exist for the version
                    if (currentVersion.ChangeLog == null)
                    {
                        currentVersion.ChangeLog = changeLog;
                        currentVersion.ChangeLog.Entries = new List<ChangeLogEntryResponse>();
                    }
                    changeLogDictionary.Add(changeLog.ChangeLogId, currentVersion.ChangeLog);
                }

                // Need the current changelog to add entries
                ChangeLogResponse? currentChangeLog = null;
                if (changeLog != null)
                {
                    changeLogDictionary.TryGetValue(changeLog.ChangeLogId, out currentChangeLog);
                }

                // 6. Add ChangeLog Entry (if present, changelog exists, and not duplicate)
                if (changeLogEntry != null && currentChangeLog != null && currentChangeLog.Entries.All(cle => cle.ChangeLogEntryId != changeLogEntry.ChangeLogEntryId))
                {
                    currentChangeLog.Entries.Add(changeLogEntry);
                }

                // Return value is not used here, Dapper handles the aggregation
                return currentFormulation; // Or just null, doesn't matter
            },
            splitOn: "MaterialCostAmount,IngredientId,VersionId,SnapshotMinimumProductionQuantityValue,SnapshotIngredientId,ChangeLogId,ChangeLogEntryId" // Columns marking the start of new objects
        );

        // Calculate efficiency metrics for all formulations
        var formulationIds = formulationDictionary.Keys.ToList();
        var efficiencyMetrics = await _efficiencyService.CalculateBatchEfficiencyMetricsAsync(
            formulationIds, cancellationToken);

        // Add efficiency metrics to formulations
        foreach (var formulation in formulationDictionary.Values)
        {
            if (efficiencyMetrics.TryGetValue(formulation.Id, out var metrics))
            {
                formulation.EfficiencyMetrics = metrics;
            }
        }

        return Result.Success<IReadOnlyList<FormulationResponse>>(formulationDictionary.Values.ToList());
    }
}