using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation;
using Tebipaints.Domain.Formulation.Errors;
using Tebipaints.Domain.Formulation.Services;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Formulation.CalculateFormulationCost;

public class CalculateFormulationCostQueryHandler : IQueryHandler<CalculateFormulationCostQuery, FormulationCost>
{
    private readonly IFormulationRepository _formulationRepository;
    private readonly IFormulationCostCalculationService _costCalculationService;

    public CalculateFormulationCostQueryHandler(
        IFormulationRepository formulationRepository,
        IFormulationCostCalculationService costCalculationService)
    {
        _formulationRepository = formulationRepository;
        _costCalculationService = costCalculationService;
    }

    public async Task<Result<FormulationCost>> Handle(
        CalculateFormulationCostQuery request,
        CancellationToken cancellationToken)
    {
        // Get the formulation
        var formulation = await _formulationRepository.GetByIdAsync(request.FormulationId, cancellationToken);
        if (formulation == null)
        {
            return Result.Failure<FormulationCost>(FormulationErrors.FormulationNotFound(request.FormulationId));
        }

        // Use the domain service to calculate cost
        var currency = Currency.FromCode(request.Currency);
        var unitOfMeasure = UnitOfMeasure.Litre; // Default unit, could be parameterized
        var productionQuantity = new Measurement(request.ProductionQuantity, unitOfMeasure);

        return await _costCalculationService.CalculateCostAsync(
            formulation.Ingredients,
            productionQuantity,
            formulation.EstimatedProductionTime,
            currency,
            cancellationToken);
    }
}
