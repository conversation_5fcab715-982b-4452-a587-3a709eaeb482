using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Application.Formulation.ListFormulations;
using Tebipaints.Application.Formulation.Services;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation.Errors;

namespace Tebipaints.Application.Formulation.GetFormulation;

public class GetFormulationQueryHandler : IQueryHandler<GetFormulationQuery, FormulationResponse>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;
    private readonly IFormulationEfficiencyService _efficiencyService;

    public GetFormulationQueryHandler(
        ISqlConnectionFactory sqlConnectionFactory,
        IFormulationEfficiencyService efficiencyService)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
        _efficiencyService = efficiencyService;
    }

    public async Task<Result<FormulationResponse>> Handle(GetFormulationQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();

        const string sql = """
                           SELECT
                               f.id                  as "Id",
                               f.name                as "Name",
                               f.description         as "Description",
                               f.shelf_life          as "ShelfLife",
                               f.estimated_production_time as "EstimatedProductionTime",
                               f.status              as "Status",
                               f.material_cost_amount as "MaterialCostAmount",
                               f.material_cost_currency as "MaterialCostCurrency",
                               f.labor_cost_amount as "LaborCostAmount",
                               f.labor_cost_currency as "LaborCostCurrency",
                               f.overhead_cost_amount as "OverheadCostAmount",
                               f.overhead_cost_currency as "OverheadCostCurrency",
                               f.total_cost_amount as "TotalCostAmount",
                               f.total_cost_currency as "TotalCostCurrency",
                               f.cost_per_unit_amount as "CostPerUnitAmount",
                               f.cost_per_unit_currency as "CostPerUnitCurrency",
                               f.cost_calculated_at as "CostCalculatedAt",
                               f.cost_calculation_basis as "CostCalculationBasis",
                               fi.id                 as "IngredientId",
                               fi.material_id        as "MaterialId",
                               fi.quantity_value     as "QuantityValue",
                               fi.quantity_unit      as "QuantityUnit",
                               v.id                  as "VersionId",
                               v.time_stamp          as "VersionTimestamp",
                               v.snapshot_minimum_production_quantity_value as "SnapshotMinimumProductionQuantityValue",
                               v.snapshot_minimum_production_quantity_unit as "SnapshotMinimumProductionQuantityUnit",
                               v.snapshot_estimated_production_time as "SnapshotEstimatedProductionTime",
                               v.snapshot_shelf_life as "SnapshotShelfLife",
                               v.snapshot_instructions as "SnapshotInstructions",
                               v.is_finalized        as "IsFinalized",
                               si.ingredient_id                 as "SnapshotIngredientId",
                               si.raw_material_id    as "RawMaterialId",
                               si.quantity_value     as "SnapshotIngredientQuantityValue",
                               si.quantity_unit      as "SnapshotIngredientQuantityUnit",
                               cl.id                 as "ChangeLogId",
                               cle.change_log_id                as "ChangeLogEntryId",
                               cle.time_stamp        as "ChangeLogEntryTimestamp",
                               cle.description       as "ChangeLogEntryDescription",
                               cle.author            as "ChangeLogEntryAuthor"
                           from formulations  f
                           LEFT JOIN ingredients fi ON fi.formulation_id = f.id
                           LEFT JOIN versions v ON v.formulation_id = f.id
                           LEFT JOIN ingredient_snapshot si ON si.formulation_snapshot_version_id = v.id
                           LEFT JOIN change_logs cl ON cl.version_id = v.id
                           LEFT JOIN change_log_entry cle ON cle.change_log_id = cl.id
                           WHERE f.id = @FormulationId
                           ORDER BY
                               v.time_stamp DESC,
                               fi.id,
                               si.id,
                               cl.id,
                               cle.time_stamp;
                           """;

        var formulationDictionary = new Dictionary<Guid, FormulationResponse>();
        var versionDictionary = new Dictionary<Guid, VersionResponse>();
        var changeLogDictionary = new Dictionary<Guid, ChangeLogResponse>();

        // Dapper Multi-Mapping (same logic as ListFormulations but for single formulation)
        await connection.QueryAsync<
            FormulationResponse,
            FormulationCostResponse?,
            FormulationIngredientResponse?,
            VersionResponse?,
            SnapshotIngredientResponse?,
            ChangeLogResponse?,
            ChangeLogEntryResponse?,
            FormulationResponse>(
            sql,
            (formulation, cost, formIngredient, version, snapIngredient, changeLog, changeLogEntry) =>
            {
                // Get or Add Formulation
                if (!formulationDictionary.TryGetValue(formulation.Id, out var currentFormulation))
                {
                    currentFormulation = formulation;
                    currentFormulation.EstimatedCost = cost; // Set the cost
                    currentFormulation.Ingredients = new List<FormulationIngredientResponse>();
                    currentFormulation.Versions = new List<VersionResponse>();
                    formulationDictionary.Add(currentFormulation.Id, currentFormulation);
                    versionDictionary.Clear();
                }

                // Add Formulation Ingredient (if present and not duplicate)
                if (formIngredient != null && currentFormulation.Ingredients.All(i => i.IngredientId != formIngredient.IngredientId))
                {
                    currentFormulation.Ingredients.Add(formIngredient);
                }

                // Add Version (if present and not duplicate)
                if (version != null && currentFormulation.Versions.All(v => v.VersionId != version.VersionId))
                {
                    // Initialize Snapshot within the Version - snapshot data is already in version
                    if (version.Snapshot == null)
                    {
                        version.Snapshot = new SnapshotResponse
                        {
                            Ingredients = new List<SnapshotIngredientResponse>()
                        };
                    }
                    else
                    {
                        version.Snapshot.Ingredients = new List<SnapshotIngredientResponse>();
                    }

                    currentFormulation.Versions.Add(version);
                    versionDictionary.Add(version.VersionId, version);
                }

                // Get current version for adding snapshot ingredients and changelogs
                var currentVersion = version != null ? versionDictionary.GetValueOrDefault(version.VersionId) : null;

                // Add Snapshot Ingredient (if present, version exists, and not duplicate)
                if (snapIngredient != null && currentVersion != null && currentVersion.Snapshot.Ingredients.All(si => si.SnapshotIngredientId != snapIngredient.SnapshotIngredientId))
                {
                    currentVersion.Snapshot.Ingredients.Add(snapIngredient);
                }

                // Add ChangeLog (if present, version exists, and not duplicate)
                if (changeLog != null && currentVersion != null && currentVersion.ChangeLog?.ChangeLogId != changeLog.ChangeLogId)
                {
                    changeLog.Entries = new List<ChangeLogEntryResponse>();
                    currentVersion.ChangeLog = changeLog;
                    changeLogDictionary.Add(changeLog.ChangeLogId, changeLog);
                }

                // Get current changelog for adding entries
                var currentChangeLog = changeLog != null ? changeLogDictionary.GetValueOrDefault(changeLog.ChangeLogId) : null;

                // Add ChangeLog Entry (if present, changelog exists, and not duplicate)
                if (changeLogEntry != null && currentChangeLog != null && currentChangeLog.Entries.All(cle => cle.ChangeLogEntryId != changeLogEntry.ChangeLogEntryId))
                {
                    currentChangeLog.Entries.Add(changeLogEntry);
                }

                return currentFormulation;
            },
            new { FormulationId = request.FormulationId },
            splitOn: "MaterialCostAmount,IngredientId,VersionId,SnapshotIngredientId,ChangeLogId,ChangeLogEntryId"
        );

        var formulation = formulationDictionary.Values.FirstOrDefault();
        if (formulation == null)
        {
            return Result.Failure<FormulationResponse>(FormulationErrors.FormulationNotFound(request.FormulationId));
        }

        // Calculate efficiency metrics for the formulation
        var efficiencyMetrics = await _efficiencyService.CalculateBasicEfficiencyMetricsAsync(
            request.FormulationId, cancellationToken);

        formulation.EfficiencyMetrics = efficiencyMetrics;

        return Result.Success(formulation);
    }
}
