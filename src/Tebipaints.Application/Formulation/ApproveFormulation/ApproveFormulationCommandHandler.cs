using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation;
using Tebipaints.Domain.Formulation.Errors;

namespace Tebipaints.Application.Formulation.ApproveFormulation;

public class ApproveFormulationCommandHandler : ICommandHandler<ApproveFormulationCommand>
{
    private readonly IFormulationRepository _formulationRepository;
    private readonly IUnitOfWork _unitOfWork;

    public ApproveFormulationCommandHandler(
        IFormulationRepository formulationRepository,
        IUnitOfWork unitOfWork)
    {
        _formulationRepository = formulationRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(
        ApproveFormulationCommand command,
        CancellationToken cancellationToken)
    {
        var formulation = await _formulationRepository.GetByIdAsync(
            command.FormulationId, cancellationToken);

        if (formulation is null)
        {
            return Result.Failure(FormulationErrors.FormulationNotFound(command.FormulationId));
        }

        var result = formulation.ApproveFormulation(command.ApprovedBy, command.ApprovalNotes);
        if (result.IsFailure)
        {
            return result;
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
