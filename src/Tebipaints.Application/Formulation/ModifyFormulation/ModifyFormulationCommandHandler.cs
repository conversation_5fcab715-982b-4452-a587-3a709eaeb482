using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation;
using Tebipaints.Domain.Formulation.Errors;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Formulation.ModifyFormulation;

public class ModifyFormulationCommandHandler : ICommandHandler<ModifyFormulationCommand>
{
    private readonly IFormulationRepository _formulationRepository;
    private readonly IUnitOfWork _unitOfWork;

    public ModifyFormulationCommandHandler(
        IFormulationRepository formulationRepository,
        IUnitOfWork unitOfWork)
    {
        _formulationRepository = formulationRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(
        ModifyFormulationCommand command,
        CancellationToken cancellationToken)
    {
        var formulation = await _formulationRepository.GetByIdAsync(
            command.FormulationId, cancellationToken);

        if (formulation is null)
        {
            return Result.Failure(FormulationErrors.FormulationNotFound(command.FormulationId));
        }

        var ingredients = command.Ingredients
            .Select(i => Ingredient.Create(
                i.MaterialId,
                i.Quantity,
                i.Units).Value)
            .ToList();

        var unitOfMeasure = UnitOfMeasureConverter.FromString(command.Units);
        var result = formulation.ModifyFormulation(
            command.Author,
            new Measurement((decimal)command.MinimumProductionQuantity, unitOfMeasure.Value),
            command.ProductionTimeInHours,
            command.ShelfLife,
            ingredients,
            command.Instructions);

        if (result.IsFailure)
        {
            return result;
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}