using FluentValidation;

namespace Tebipaints.Application.Formulation.ModifyFormulation;

public class ModifyFormulationCommandValidator : AbstractValidator<ModifyFormulationCommand>
{
    public ModifyFormulationCommandValidator()
    {
        RuleFor(command => command.FormulationId).NotEmpty();
        RuleFor(command => command.Author).NotEmpty();
        RuleFor(command => command.MinimumProductionQuantity).GreaterThan(0);
        RuleFor(command => command.ProductionTimeInHours).GreaterThan(0);
        RuleFor(command => command.ShelfLife).GreaterThan(0);
        RuleFor(command => command.Ingredients).NotEmpty();
    }
}