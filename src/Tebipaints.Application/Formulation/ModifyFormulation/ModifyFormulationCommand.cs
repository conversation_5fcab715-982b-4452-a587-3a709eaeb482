using Tebipaints.Application.Abstractions.Messaging;

namespace Tebipaints.Application.Formulation.ModifyFormulation;

public sealed record ModifyFormulationCommand(
    Guid FormulationId,
    string Author,
    double MinimumProductionQuantity,
    string Units,
    int ProductionTimeInHours,
    int ShelfLife,
    List<string>? Instructions,
    List<IngredientDto> Ingredients) : ICommand;
    
public sealed record IngredientDto(
    Guid MaterialId,
    double Quantity,
    string Units);