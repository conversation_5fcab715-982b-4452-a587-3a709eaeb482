using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation;
using Tebipaints.Domain.Formulation.Errors;

namespace Tebipaints.Application.Formulation.RejectFormulation;

public class RejectFormulationCommandHandler : ICommandHandler<RejectFormulationCommand>
{
    private readonly IFormulationRepository _formulationRepository;
    private readonly IUnitOfWork _unitOfWork;

    public RejectFormulationCommandHandler(
        IFormulationRepository formulationRepository,
        IUnitOfWork unitOfWork)
    {
        _formulationRepository = formulationRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(
        RejectFormulationCommand command,
        CancellationToken cancellationToken)
    {
        var formulation = await _formulationRepository.GetByIdAsync(
            command.FormulationId, cancellationToken);

        if (formulation is null)
        {
            return Result.Failure(FormulationErrors.FormulationNotFound(command.FormulationId));
        }

        var result = formulation.RejectFormulation(command.RejectedBy, command.RejectionReason);
        if (result.IsFailure)
        {
            return result;
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
