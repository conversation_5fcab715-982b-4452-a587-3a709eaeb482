using Tebipaints.Application.Formulation.ListFormulations;

namespace Tebipaints.Application.Formulation.Services;

/// <summary>
/// Service for calculating basic formulation efficiency metrics using existing production data
/// </summary>
public interface IFormulationEfficiencyService
{
    /// <summary>
    /// Calculate basic efficiency metrics for a formulation using completed production batches
    /// </summary>
    /// <param name="formulationId">The formulation ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Basic efficiency metrics or null if no production data available</returns>
    Task<FormulationEfficiencyMetricsResponse?> CalculateBasicEfficiencyMetricsAsync(
        Guid formulationId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate efficiency metrics for multiple formulations in batch
    /// </summary>
    /// <param name="formulationIds">List of formulation IDs</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary of formulation ID to efficiency metrics</returns>
    Task<Dictionary<Guid, FormulationEfficiencyMetricsResponse?>> CalculateBatchEfficiencyMetricsAsync(
        IEnumerable<Guid> formulationIds,
        CancellationToken cancellationToken = default);
}
