using FluentValidation;

namespace Tebipaints.Application.Formulation.CreateFormulation;

internal class CreateFormulationCommandValidator : AbstractValidator<CreateFormulationCommand>
{
    public CreateFormulationCommandValidator()
    {
        RuleFor(f => f.Name).NotNull().NotEmpty();
        RuleFor(f => f.MinimumProductionQuantity).GreaterThan(0);
        RuleFor(f => f.ProductionTimeInHours).GreaterThan(0);
        RuleFor(f => f.ShelfLife).GreaterThan(0);
        RuleFor(f => f.Ingredients).NotNull();
    }
}