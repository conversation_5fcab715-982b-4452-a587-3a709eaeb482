using Microsoft.Extensions.Logging;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation;
using Tebipaints.Domain.Formulation.Services;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Formulation.CreateFormulation;

internal sealed class CreateFormulationCommandHandler : ICommandHandler<CreateFormulationCommand, Domain.Formulation.Formulation>
{
    private readonly IFormulationRepository _formulationRepository;
    private readonly IFormulationCostCalculationService _costCalculationService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CreateFormulationCommandHandler> _logger;

    public CreateFormulationCommandHandler(
        IFormulationRepository formulationRepository,
        IFormulationCostCalculationService costCalculationService,
        IUnitOfWork unitOfWork, ILogger<CreateFormulationCommandHandler> logger)
    {
        _formulationRepository = formulationRepository;
        _costCalculationService = costCalculationService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Result<Domain.Formulation.Formulation>> Handle(CreateFormulationCommand request, CancellationToken cancellationToken)
    {
        var unitOfMeasure = UnitOfMeasureConverter.FromString(request.Units);
        var measurementValue = (decimal)request.MinimumProductionQuantity;
        var formulation = Domain.Formulation.Formulation.Create(
            request.Name,
            request.Description,
            new Measurement(measurementValue, unitOfMeasure.Value),
            request.ProductionTimeInHours,
            request.ShelfLife,
            request.Ingredients.ConvertAll(ingredient => Ingredient.Create(
                ingredient.MaterialId,
                ingredient.Quantity,
                ingredient.Units).Value),
            request.Instructions);

        if (formulation.IsFailure)
        {
            return Result.Failure<Domain.Formulation.Formulation>(formulation.Error);
        }

        // Calculate initial cost for the formulation
        var currency = Currency.FromCode("GHS"); // Could be configurable
        var latestVersion = formulation.Value.Versions.OrderByDescending(v => v.TimeStamp).First();
        var productionQuantity = latestVersion.Snapshot.MinimumProductionQuantity;

        var costResult = await _costCalculationService.CalculateCostAsync(
            formulation.Value.Ingredients,
            productionQuantity,
            formulation.Value.EstimatedProductionTime,
            currency,
            cancellationToken);

        // Update the formulation with calculated cost (if successful)
        if (costResult.IsSuccess)
        {
            var updateCostResult = formulation.Value.UpdateEstimatedCost(costResult.Value);
            if (updateCostResult.IsFailure)
            {
                _logger.LogWarning("Failed to update cost for formulation {FormulationId}: {Error}",
                    formulation.Value.Id,
                    updateCostResult.Error);
            }
        }

        _formulationRepository.Add(formulation.Value);

        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success(formulation.Value);
    }
}