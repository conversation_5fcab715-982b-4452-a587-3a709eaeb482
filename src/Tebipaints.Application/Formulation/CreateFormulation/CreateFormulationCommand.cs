using Tebipaints.Application.Abstractions.Messaging;

namespace Tebipaints.Application.Formulation.CreateFormulation;

public record CreateFormulationCommand(
    string Name,
    string? Description,
    double MinimumProductionQuantity,
    string Units,
    int ProductionTimeInHours,
    int ShelfLife,
    List<string>? Instructions,
    List<IngredientCommand> Ingredients) : ICommand<Domain.Formulation.Formulation>;
    
    
public record IngredientCommand(Guid MaterialId, double Quantity, string Units);