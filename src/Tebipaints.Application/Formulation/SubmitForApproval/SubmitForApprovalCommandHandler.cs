using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation;
using Tebipaints.Domain.Formulation.Errors;

namespace Tebipaints.Application.Formulation.SubmitForApproval;

public class SubmitForApprovalCommandHandler : ICommandHandler<SubmitForApprovalCommand>
{
    private readonly IFormulationRepository _formulationRepository;
    private readonly IUnitOfWork _unitOfWork;

    public SubmitForApprovalCommandHandler(
        IFormulationRepository formulationRepository,
        IUnitOfWork unitOfWork)
    {
        _formulationRepository = formulationRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(
        SubmitForApprovalCommand command,
        CancellationToken cancellationToken)
    {
        var formulation = await _formulationRepository.GetByIdAsync(
            command.FormulationId, cancellationToken);

        if (formulation is null)
        {
            return Result.Failure(FormulationErrors.FormulationNotFound(command.FormulationId));
        }

        var result = formulation.SubmitForApproval(command.SubmittedBy);
        if (result.IsFailure)
        {
            return result;
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
