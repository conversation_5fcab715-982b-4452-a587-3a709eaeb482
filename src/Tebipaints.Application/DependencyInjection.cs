using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using Tebipaints.Application.Abstractions.Behaviors;
using Tebipaints.Domain.Invoice.Services;
using Tebipaints.Domain.Production.Services;

namespace Tebipaints.Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        services.AddMediatR(config =>
        {
            config.RegisterServicesFromAssembly(typeof(DependencyInjection).Assembly);
            config.AddOpenBehavior(typeof(LoggingBehavior<,>));
            config.AddOpenBehavior(typeof(ValidationBehavior<,>));
        });
        
        services.AddValidatorsFromAssembly(typeof(DependencyInjection).Assembly);

        services.AddTransient<ProductionInventoryService>();
        services.AddTransient<BatchSchedulingService>();
        services.AddTransient(provider => new InvoiceTotalsCalculationService(0.15m));
        services.AddTransient<LineItemCalculationService>();

        return services;
    }
}