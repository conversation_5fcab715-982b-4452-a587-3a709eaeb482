namespace Tebipaints.Application.Procurement.PurchaseInvoices.ListPurchaseInvoices;

public class PurchaseInvoiceResponse
{
    public Guid Id { get; set; }
    public string SupplierInvoiceNumber { get; set; } = string.Empty;
    public Guid PurchaseOrderId { get; set; }
    public string PurchaseOrderNumber { get; set; } = string.Empty;
    public Guid SupplierId { get; set; }
    public DateTime InvoiceDate { get; set; }
    public DateTime DueDate { get; set; }
    public decimal TotalAmount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public decimal CalculatedTotal { get; set; }
    public decimal AmountVariance { get; set; }
    public decimal AmountVariancePercentage { get; set; }
    public bool HasMatchingDiscrepancies { get; set; }
    public bool IsWithinToleranceLimit { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime? ApprovedDate { get; set; }
    public string? ApprovedBy { get; set; }
    public DateTime? RejectedDate { get; set; }
    public string? RejectedBy { get; set; }
    public string? RejectionReason { get; set; }
    public DateTime? LastMatchingDate { get; set; }
    public bool? LastMatchingResult { get; set; }
    public decimal? LastMatchingVariancePercentage { get; set; }
    public string? MatchingNotes { get; set; }
    public string? Notes { get; set; }
    public int ItemCount { get; set; }
    public List<PurchaseInvoiceItemResponse> Items { get; set; } = new();
}

public class PurchaseInvoiceItemResponse
{
    public Guid MaterialId { get; set; }
    public string MaterialName { get; set; } = string.Empty;
    public decimal InvoicedQuantityValue { get; set; }
    public string InvoicedQuantityUnit { get; set; } = string.Empty;
    public decimal UnitPriceAmount { get; set; }
    public string UnitPriceCurrency { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
    public string? Notes { get; set; }
}
