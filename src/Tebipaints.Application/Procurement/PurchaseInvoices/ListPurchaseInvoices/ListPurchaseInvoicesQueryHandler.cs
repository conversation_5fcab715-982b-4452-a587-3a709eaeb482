using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Application.Procurement.PurchaseInvoices.ListPurchaseInvoices;

public class ListPurchaseInvoicesQueryHandler : IQueryHandler<ListPurchaseInvoicesQuery, IReadOnlyList<PurchaseInvoiceResponse>>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;

    public ListPurchaseInvoicesQueryHandler(ISqlConnectionFactory sqlConnectionFactory)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
    }

    public async Task<Result<IReadOnlyList<PurchaseInvoiceResponse>>> Handle(ListPurchaseInvoicesQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();

        var whereConditions = new List<string>();
        var parameters = new DynamicParameters();

        // Build WHERE clause
        if (request.PurchaseOrderId.HasValue)
        {
            whereConditions.Add("pi.purchase_order_id = @PurchaseOrderId");
            parameters.Add("PurchaseOrderId", request.PurchaseOrderId.Value);
        }

        if (request.SupplierId.HasValue)
        {
            whereConditions.Add("pi.supplier_id = @SupplierId");
            parameters.Add("SupplierId", request.SupplierId.Value);
        }

        if (!string.IsNullOrWhiteSpace(request.Status))
        {
            whereConditions.Add("pi.status = @Status");
            parameters.Add("Status", request.Status);
        }

        if (request.FromDate.HasValue)
        {
            whereConditions.Add("pi.invoice_date >= @FromDate");
            parameters.Add("FromDate", request.FromDate.Value);
        }

        if (request.ToDate.HasValue)
        {
            whereConditions.Add("pi.invoice_date <= @ToDate");
            parameters.Add("ToDate", request.ToDate.Value);
        }

        if (request.DueDateFrom.HasValue)
        {
            whereConditions.Add("pi.due_date >= @DueDateFrom");
            parameters.Add("DueDateFrom", request.DueDateFrom.Value);
        }

        if (request.DueDateTo.HasValue)
        {
            whereConditions.Add("pi.due_date <= @DueDateTo");
            parameters.Add("DueDateTo", request.DueDateTo.Value);
        }

        var whereClause = whereConditions.Any() ? "WHERE " + string.Join(" AND ", whereConditions) : "";

        // Add pagination
        var offset = (request.PageNumber - 1) * request.PageSize;
        parameters.Add("Offset", offset);
        parameters.Add("PageSize", request.PageSize);

        const string sql = """
            WITH invoice_totals AS (
                SELECT 
                    purchase_invoice_id,
                    COUNT(*) as item_count,
                    SUM(unit_price_amount * invoiced_quantity_value) as calculated_total
                FROM purchase_invoice_items
                GROUP BY purchase_invoice_id
            )
            SELECT 
                pi.id,
                pi.supplier_invoice_number as SupplierInvoiceNumber,
                pi.purchase_order_id as PurchaseOrderId,
                pi.purchase_order_number as PurchaseOrderNumber,
                pi.supplier_id as SupplierId,
                pi.invoice_date as InvoiceDate,
                pi.due_date as DueDate,
                pi.total_amount as TotalAmount,
                pi.total_currency as Currency,
                COALESCE(it.calculated_total, 0) as CalculatedTotal,
                (pi.total_amount - COALESCE(it.calculated_total, 0)) as AmountVariance,
                CASE 
                    WHEN COALESCE(it.calculated_total, 0) = 0 THEN 0
                    ELSE ROUND(((pi.total_amount - COALESCE(it.calculated_total, 0)) / COALESCE(it.calculated_total, 0)) * 100, 2)
                END as AmountVariancePercentage,
                (ABS(pi.total_amount - COALESCE(it.calculated_total, 0)) > 0.01) as HasMatchingDiscrepancies,
                CASE 
                    WHEN COALESCE(it.calculated_total, 0) = 0 THEN true
                    ELSE ABS(((pi.total_amount - COALESCE(it.calculated_total, 0)) / COALESCE(it.calculated_total, 0)) * 100) <= 5.0
                END as IsWithinToleranceLimit,
                pi.status as Status,
                pi.approved_date as ApprovedDate,
                pi.approved_by as ApprovedBy,
                pi.rejected_date as RejectedDate,
                pi.rejected_by as RejectedBy,
                pi.rejection_reason as RejectionReason,
                pi.last_matching_date as LastMatchingDate,
                pi.last_matching_result as LastMatchingResult,
                pi.last_matching_variance_percentage as LastMatchingVariancePercentage,
                pi.matching_notes as MatchingNotes,
                pi.notes as Notes,
                COALESCE(it.item_count, 0) as ItemCount,
                -- Item details
                pii.material_id as MaterialId,
                pii.material_name as MaterialName,
                pii.invoiced_quantity_value as InvoicedQuantityValue,
                pii.invoiced_quantity_unit as InvoicedQuantityUnit,
                pii.unit_price_amount as UnitPriceAmount,
                pii.unit_price_currency as UnitPriceCurrency,
                (pii.unit_price_amount * pii.invoiced_quantity_value) as TotalAmount,
                pii.notes as ItemNotes
            FROM purchase_invoices pi
            LEFT JOIN invoice_totals it ON pi.id = it.purchase_invoice_id
            LEFT JOIN purchase_invoice_items pii ON pi.id = pii.purchase_invoice_id
            {whereClause}
            ORDER BY pi.invoice_date DESC, pi.supplier_invoice_number DESC
            OFFSET @Offset ROWS
            FETCH NEXT @PageSize ROWS ONLY
            """;

        var invoiceDictionary = new Dictionary<Guid, PurchaseInvoiceResponse>();

        var invoices = await connection.QueryAsync<PurchaseInvoiceResponse, PurchaseInvoiceItemResponse, PurchaseInvoiceResponse>(
            sql,
            (invoice, item) =>
            {
                if (!invoiceDictionary.TryGetValue(invoice.Id, out var existingInvoice))
                {
                    existingInvoice = invoice;
                    existingInvoice.Items = new List<PurchaseInvoiceItemResponse>();
                    invoiceDictionary.Add(invoice.Id, existingInvoice);
                }

                if (item != null && item.MaterialId != Guid.Empty)
                {
                    existingInvoice.Items.Add(item);
                }

                return existingInvoice;
            },
            parameters,
            splitOn: "MaterialId");

        return Result.Success<IReadOnlyList<PurchaseInvoiceResponse>>(invoiceDictionary.Values.ToList());
    }
}
