using Tebipaints.Application.Abstractions.Messaging;

namespace Tebipaints.Application.Procurement.PurchaseInvoices.ListPurchaseInvoices;

public class ListPurchaseInvoicesQuery : IQuery<IReadOnlyList<PurchaseInvoiceResponse>>
{
    public Guid? PurchaseOrderId { get; set; }
    public Guid? SupplierId { get; set; }
    public string? Status { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public DateTime? DueDateFrom { get; set; }
    public DateTime? DueDateTo { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 50;
}
