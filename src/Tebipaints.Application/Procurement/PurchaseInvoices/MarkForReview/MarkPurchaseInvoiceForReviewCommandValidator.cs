using FluentValidation;

namespace Tebipaints.Application.Procurement.PurchaseInvoices.MarkForReview;

public class MarkPurchaseInvoiceForReviewCommandValidator : AbstractValidator<MarkPurchaseInvoiceForReviewCommand>
{
    public MarkPurchaseInvoiceForReviewCommandValidator()
    {
        RuleFor(x => x.PurchaseInvoiceId)
            .NotEmpty()
            .WithMessage("Purchase Invoice ID is required.");

        RuleFor(x => x.Reason)
            .NotEmpty()
            .WithMessage("Reason is required.")
            .MaximumLength(1000)
            .WithMessage("Reason cannot exceed 1000 characters.");
    }
}
