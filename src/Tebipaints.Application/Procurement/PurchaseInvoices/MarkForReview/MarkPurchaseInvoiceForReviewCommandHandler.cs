using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Errors;

namespace Tebipaints.Application.Procurement.PurchaseInvoices.MarkForReview;

public class MarkPurchaseInvoiceForReviewCommandHandler : ICommandHandler<MarkPurchaseInvoiceForReviewCommand>
{
    private readonly IPurchaseInvoiceRepository _purchaseInvoiceRepository;
    private readonly IUnitOfWork _unitOfWork;

    public MarkPurchaseInvoiceForReviewCommandHandler(
        IPurchaseInvoiceRepository purchaseInvoiceRepository,
        IUnitOfWork unitOfWork)
    {
        _purchaseInvoiceRepository = purchaseInvoiceRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(MarkPurchaseInvoiceForReviewCommand request, CancellationToken cancellationToken)
    {
        // 1. Get purchase invoice
        var purchaseInvoice = await _purchaseInvoiceRepository.GetByIdAsync(request.PurchaseInvoiceId, cancellationToken);
        if (purchaseInvoice is null)
        {
            return Result.Failure(ProcurementErrors.PurchaseInvoiceNotFound(request.PurchaseInvoiceId));
        }

        // 2. Mark for review
        var markResult = purchaseInvoice.MarkForReview(request.Reason);
        if (markResult.IsFailure)
        {
            return Result.Failure(markResult.Error);
        }

        // 3. Save changes
        _purchaseInvoiceRepository.Update(purchaseInvoice);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
