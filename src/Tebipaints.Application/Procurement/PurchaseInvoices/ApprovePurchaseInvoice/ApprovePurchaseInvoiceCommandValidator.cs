using FluentValidation;

namespace Tebipaints.Application.Procurement.PurchaseInvoices.ApprovePurchaseInvoice;

public class ApprovePurchaseInvoiceCommandValidator : AbstractValidator<ApprovePurchaseInvoiceCommand>
{
    public ApprovePurchaseInvoiceCommandValidator()
    {
        RuleFor(x => x.PurchaseInvoiceId)
            .NotEmpty()
            .WithMessage("Purchase Invoice ID is required.");

        RuleFor(x => x.ApprovedBy)
            .NotEmpty()
            .WithMessage("Approved By is required.")
            .MaximumLength(100)
            .WithMessage("Approved By cannot exceed 100 characters.");
    }
}
