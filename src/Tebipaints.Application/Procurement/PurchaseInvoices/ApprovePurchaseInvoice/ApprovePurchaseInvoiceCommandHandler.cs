using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Errors;

namespace Tebipaints.Application.Procurement.PurchaseInvoices.ApprovePurchaseInvoice;

public class ApprovePurchaseInvoiceCommandHandler : ICommandHandler<ApprovePurchaseInvoiceCommand>
{
    private readonly IPurchaseInvoiceRepository _purchaseInvoiceRepository;
    private readonly IUnitOfWork _unitOfWork;

    public ApprovePurchaseInvoiceCommandHandler(
        IPurchaseInvoiceRepository purchaseInvoiceRepository,
        IUnitOfWork unitOfWork)
    {
        _purchaseInvoiceRepository = purchaseInvoiceRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(ApprovePurchaseInvoiceCommand request, CancellationToken cancellationToken)
    {
        // 1. Get purchase invoice
        var purchaseInvoice = await _purchaseInvoiceRepository.GetByIdAsync(request.PurchaseInvoiceId, cancellationToken);
        if (purchaseInvoice is null)
        {
            return Result.Failure(ProcurementErrors.PurchaseInvoiceNotFound(request.PurchaseInvoiceId));
        }

        // 2. Check if invoice requires matching review
        if (purchaseInvoice.Status == Domain.Procurement.Enums.PurchaseInvoiceStatus.RequiresReview)
        {
            // Ensure recent matching results exist and are within acceptable limits
            if (purchaseInvoice.LastMatchingDate == null ||
                purchaseInvoice.LastMatchingResult != true)
            {
                return Result.Failure(ProcurementErrors.InvoiceRequiresMatchingReview());
            }

            // Check if matching is recent (within last 24 hours for safety)
            if (purchaseInvoice.LastMatchingDate < DateTime.UtcNow.AddHours(-24))
            {
                return Result.Failure(ProcurementErrors.InvoiceRequiresRecentMatching());
            }
        }

        // 3. Approve purchase invoice
        var approveResult = purchaseInvoice.Approve(request.ApprovedBy);
        if (approveResult.IsFailure)
        {
            return Result.Failure(approveResult.Error);
        }

        // 4. Save changes
        _purchaseInvoiceRepository.Update(purchaseInvoice);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
