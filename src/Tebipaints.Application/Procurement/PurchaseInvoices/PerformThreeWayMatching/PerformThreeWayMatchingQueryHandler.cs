using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Errors;

namespace Tebipaints.Application.Procurement.PurchaseInvoices.PerformThreeWayMatching;

public class PerformThreeWayMatchingQueryHandler : IQueryHandler<PerformThreeWayMatchingQuery, ThreeWayMatchingResponse>
{
    private readonly IPurchaseInvoiceRepository _purchaseInvoiceRepository;
    private readonly IPurchaseOrderRepository _purchaseOrderRepository;
    private readonly IGoodsReceiptRepository _goodsReceiptRepository;

    public PerformThreeWayMatchingQueryHandler(
        IPurchaseInvoiceRepository purchaseInvoiceRepository,
        IPurchaseOrderRepository purchaseOrderRepository,
        IGoodsReceiptRepository goodsReceiptRepository)
    {
        _purchaseInvoiceRepository = purchaseInvoiceRepository;
        _purchaseOrderRepository = purchaseOrderRepository;
        _goodsReceiptRepository = goodsReceiptRepository;
    }

    public async Task<Result<ThreeWayMatchingResponse>> Handle(PerformThreeWayMatchingQuery request, CancellationToken cancellationToken)
    {
        // 1. Get purchase invoice
        var purchaseInvoice = await _purchaseInvoiceRepository.GetByIdAsync(request.PurchaseInvoiceId, cancellationToken);
        if (purchaseInvoice is null)
        {
            return Result.Failure<ThreeWayMatchingResponse>(ProcurementErrors.PurchaseInvoiceNotFound(request.PurchaseInvoiceId));
        }

        // 2. Get purchase order
        var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(purchaseInvoice.PurchaseOrderId, cancellationToken);
        if (purchaseOrder is null)
        {
            return Result.Failure<ThreeWayMatchingResponse>(ProcurementErrors.PurchaseOrderNotFound(purchaseInvoice.PurchaseOrderId));
        }

        // 3. Get goods receipts for this purchase order
        var goodsReceipts = await _goodsReceiptRepository.GetByPurchaseOrderIdAsync(purchaseInvoice.PurchaseOrderId, cancellationToken);

        // 4. Perform three-way matching
        var matchingResult = purchaseInvoice.PerformThreeWayMatching(purchaseOrder, goodsReceipts);
        if (matchingResult.IsFailure)
        {
            return Result.Failure<ThreeWayMatchingResponse>(matchingResult.Error);
        }

        // 5. Build response
        var itemResults = matchingResult.Value.Select(result => new InvoiceMatchingResultResponse
        {
            MaterialId = result.MaterialId,
            MaterialName = GetMaterialName(result.MaterialId, purchaseOrder),
            IsMatched = result.IsMatched,
            DiscrepancyDescription = result.DiscrepancyDescription,
            DiscrepancyType = result.DiscrepancyType?.ToString(),
            OrderedQuantity = result.OrderedQuantity,
            ReceivedQuantity = result.ReceivedQuantity,
            InvoicedQuantity = result.InvoicedQuantity,
            OrderedUnitPrice = result.OrderedUnitPrice,
            InvoicedUnitPrice = result.InvoicedUnitPrice,
            QuantityVariance = result.QuantityVariance,
            QuantityVariancePercentage = result.QuantityVariancePercentage,
            PriceVariance = result.PriceVariance,
            PriceVariancePercentage = result.PriceVariancePercentage,
            IsQuantityWithinTolerance = result.IsQuantityWithinTolerance,
            IsPriceWithinTolerance = result.IsPriceWithinTolerance
        }).ToList();

        var isMatched = itemResults.All(r => r.IsMatched);
        var hasDiscrepancies = itemResults.Any(r => !r.IsMatched);
        var overallVariancePercentage = itemResults.Any() ? 
            itemResults.Average(r => Math.Max(Math.Abs(r.QuantityVariancePercentage), Math.Abs(r.PriceVariancePercentage))) : 0;
        var isWithinTolerance = overallVariancePercentage <= 5.0m;

        var matchingSummary = BuildMatchingSummary(isMatched, hasDiscrepancies, itemResults.Count, itemResults.Count(r => r.IsMatched));

        var response = new ThreeWayMatchingResponse
        {
            PurchaseInvoiceId = purchaseInvoice.Id,
            SupplierInvoiceNumber = purchaseInvoice.SupplierInvoiceNumber,
            PurchaseOrderId = purchaseOrder.Id,
            PurchaseOrderNumber = purchaseOrder.OrderNumber.ToString(),
            IsMatched = isMatched,
            HasDiscrepancies = hasDiscrepancies,
            OverallVariancePercentage = overallVariancePercentage,
            IsWithinTolerance = isWithinTolerance,
            MatchingSummary = matchingSummary,
            ItemResults = itemResults
        };

        return Result.Success(response);
    }

    private static string GetMaterialName(Guid materialId, PurchaseOrder purchaseOrder)
    {
        var item = purchaseOrder.Items.FirstOrDefault(i => i.MaterialId == materialId);
        return item?.MaterialName ?? "Unknown Material";
    }

    private static string BuildMatchingSummary(bool isMatched, bool hasDiscrepancies, int totalItems, int matchedItems)
    {
        if (isMatched)
        {
            return $"All {totalItems} items matched successfully within tolerance.";
        }

        if (hasDiscrepancies)
        {
            var unmatchedItems = totalItems - matchedItems;
            return $"{matchedItems} of {totalItems} items matched. {unmatchedItems} items have discrepancies requiring review.";
        }

        return "Matching completed with no issues.";
    }
}
