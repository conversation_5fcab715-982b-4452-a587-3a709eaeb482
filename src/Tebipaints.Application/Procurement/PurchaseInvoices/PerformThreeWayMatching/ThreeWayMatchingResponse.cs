namespace Tebipaints.Application.Procurement.PurchaseInvoices.PerformThreeWayMatching;

public class ThreeWayMatchingResponse
{
    public Guid PurchaseInvoiceId { get; set; }
    public string SupplierInvoiceNumber { get; set; } = string.Empty;
    public Guid PurchaseOrderId { get; set; }
    public string PurchaseOrderNumber { get; set; } = string.Empty;
    public bool IsMatched { get; set; }
    public bool HasDiscrepancies { get; set; }
    public decimal OverallVariancePercentage { get; set; }
    public bool IsWithinTolerance { get; set; }
    public string MatchingSummary { get; set; } = string.Empty;
    public List<InvoiceMatchingResultResponse> ItemResults { get; set; } = new();
}

public class InvoiceMatchingResultResponse
{
    public Guid MaterialId { get; set; }
    public string MaterialName { get; set; } = string.Empty;
    public bool IsMatched { get; set; }
    public string? DiscrepancyDescription { get; set; }
    public string? DiscrepancyType { get; set; }
    public decimal OrderedQuantity { get; set; }
    public decimal ReceivedQuantity { get; set; }
    public decimal InvoicedQuantity { get; set; }
    public decimal OrderedUnitPrice { get; set; }
    public decimal InvoicedUnitPrice { get; set; }
    public decimal QuantityVariance { get; set; }
    public decimal QuantityVariancePercentage { get; set; }
    public decimal PriceVariance { get; set; }
    public decimal PriceVariancePercentage { get; set; }
    public bool IsQuantityWithinTolerance { get; set; }
    public bool IsPriceWithinTolerance { get; set; }
}
