using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Errors;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Procurement.PurchaseInvoices.CreatePurchaseInvoice;

public class CreatePurchaseInvoiceCommandHandler : ICommandHandler<CreatePurchaseInvoiceCommand, Guid>
{
    private readonly IPurchaseInvoiceRepository _purchaseInvoiceRepository;
    private readonly IPurchaseOrderRepository _purchaseOrderRepository;
    private readonly IGoodsReceiptRepository _goodsReceiptRepository;
    private readonly IUnitOfWork _unitOfWork;

    public CreatePurchaseInvoiceCommandHandler(
        IPurchaseInvoiceRepository purchaseInvoiceRepository,
        IPurchaseOrderRepository purchaseOrderRepository,
        IGoodsReceiptRepository goodsReceiptRepository,
        IUnitOfWork unitOfWork)
    {
        _purchaseInvoiceRepository = purchaseInvoiceRepository;
        _purchaseOrderRepository = purchaseOrderRepository;
        _goodsReceiptRepository = goodsReceiptRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<Guid>> Handle(CreatePurchaseInvoiceCommand request, CancellationToken cancellationToken)
    {
        // 1. Check if invoice already exists for this supplier
        var existingInvoice = await _purchaseInvoiceRepository.GetBySupplierInvoiceNumberAsync(
            request.SupplierInvoiceNumber, 
            request.SupplierId, 
            cancellationToken);

        if (existingInvoice is not null)
        {
            return Result.Failure<Guid>(ProcurementErrors.DuplicateSupplierInvoiceNumber(request.SupplierInvoiceNumber));
        }

        // 2. Get and validate purchase order
        var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(request.PurchaseOrderId, cancellationToken);
        if (purchaseOrder is null)
        {
            return Result.Failure<Guid>(ProcurementErrors.PurchaseOrderNotFound(request.PurchaseOrderId));
        }

        if (purchaseOrder.SupplierId != request.SupplierId)
        {
            return Result.Failure<Guid>(ProcurementErrors.SupplierMismatch());
        }

        // 3. Create purchase invoice items
        var purchaseInvoiceItems = new List<PurchaseInvoiceItem>();
        foreach (var itemRequest in request.Items)
        {
            // Validate material exists in purchase order
            var poItem = purchaseOrder.Items.FirstOrDefault(i => i.MaterialId == itemRequest.MaterialId);
            if (poItem is null)
            {
                return Result.Failure<Guid>(ProcurementErrors.MaterialNotInPurchaseOrder(itemRequest.MaterialId));
            }

            // Convert units
            var invoicedQuantityResult = UnitOfMeasureConverter.FromString(itemRequest.InvoicedQuantityUnit);
            if (invoicedQuantityResult.IsFailure)
            {
                return Result.Failure<Guid>(invoicedQuantityResult.Error);
            }

            var invoicedQuantity = new Measurement(itemRequest.InvoicedQuantityValue, invoicedQuantityResult.Value);

            // Convert currency
            var currency = Currency.FromCode(itemRequest.UnitPriceCurrency);
            var unitPrice = new Money(itemRequest.UnitPriceAmount, currency);

            // Create purchase invoice item
            var purchaseInvoiceItemResult = PurchaseInvoiceItem.Create(
                itemRequest.MaterialId,
                itemRequest.MaterialName,
                invoicedQuantity,
                unitPrice,
                itemRequest.Notes);

            if (purchaseInvoiceItemResult.IsFailure)
            {
                return Result.Failure<Guid>(purchaseInvoiceItemResult.Error);
            }

            purchaseInvoiceItems.Add(purchaseInvoiceItemResult.Value);
        }

        // 4. Create total amount
        var totalCurrency = Currency.FromCode(request.TotalCurrency);
        var totalAmount = new Money(request.TotalAmount, totalCurrency);

        // 5. Create purchase invoice
        var purchaseInvoiceResult = PurchaseInvoice.Create(
            request.SupplierInvoiceNumber,
            request.PurchaseOrderId,
            purchaseOrder.OrderNumber.ToString(),
            request.SupplierId,
            request.InvoiceDate,
            request.DueDate,
            totalAmount,
            purchaseInvoiceItems,
            request.Notes);

        if (purchaseInvoiceResult.IsFailure)
        {
            return Result.Failure<Guid>(purchaseInvoiceResult.Error);
        }

        // 6. Perform automatic 3-way matching
        var goodsReceipts = await _goodsReceiptRepository.GetByPurchaseOrderIdAsync(request.PurchaseOrderId, cancellationToken);
        var matchingResult = purchaseInvoiceResult.Value.ProcessAutomaticMatching(purchaseOrder, goodsReceipts);

        if (matchingResult.IsFailure)
        {
            // Log warning but don't fail invoice creation - just keep as draft
            // In a real system, you might want to log this for monitoring
        }

        // 7. Save purchase invoice
        _purchaseInvoiceRepository.Add(purchaseInvoiceResult.Value);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success(purchaseInvoiceResult.Value.Id);
    }
}
