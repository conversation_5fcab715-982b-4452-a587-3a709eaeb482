using Tebipaints.Application.Abstractions.Messaging;

namespace Tebipaints.Application.Procurement.PurchaseInvoices.CreatePurchaseInvoice;

public class CreatePurchaseInvoiceCommand : ICommand<Guid>
{
    public string SupplierInvoiceNumber { get; set; } = string.Empty;
    public Guid PurchaseOrderId { get; set; }
    public Guid SupplierId { get; set; }
    public DateTime InvoiceDate { get; set; }
    public DateTime DueDate { get; set; }
    public decimal TotalAmount { get; set; }
    public string TotalCurrency { get; set; } = string.Empty;
    public string? Notes { get; set; }
    public List<CreatePurchaseInvoiceItemCommand> Items { get; set; } = new();
}

public class CreatePurchaseInvoiceItemCommand
{
    public Guid MaterialId { get; set; }
    public string MaterialName { get; set; } = string.Empty;
    public decimal InvoicedQuantityValue { get; set; }
    public string InvoicedQuantityUnit { get; set; } = string.Empty;
    public decimal UnitPriceAmount { get; set; }
    public string UnitPriceCurrency { get; set; } = string.Empty;
    public string? Notes { get; set; }
}
