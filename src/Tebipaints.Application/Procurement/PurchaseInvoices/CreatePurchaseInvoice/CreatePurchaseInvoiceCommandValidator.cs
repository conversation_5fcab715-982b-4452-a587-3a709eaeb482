using FluentValidation;

namespace Tebipaints.Application.Procurement.PurchaseInvoices.CreatePurchaseInvoice;

public class CreatePurchaseInvoiceCommandValidator : AbstractValidator<CreatePurchaseInvoiceCommand>
{
    public CreatePurchaseInvoiceCommandValidator()
    {
        RuleFor(x => x.SupplierInvoiceNumber)
            .NotEmpty()
            .WithMessage("Supplier Invoice Number is required.")
            .MaximumLength(100)
            .WithMessage("Supplier Invoice Number cannot exceed 100 characters.");

        RuleFor(x => x.PurchaseOrderId)
            .NotEmpty()
            .WithMessage("Purchase Order ID is required.");

        RuleFor(x => x.SupplierId)
            .NotEmpty()
            .WithMessage("Supplier ID is required.");

        RuleFor(x => x.InvoiceDate)
            .NotEmpty()
            .WithMessage("Invoice Date is required.")
            .LessThanOrEqualTo(DateTime.Today)
            .WithMessage("Invoice Date cannot be in the future.");

        RuleFor(x => x.DueDate)
            .NotEmpty()
            .WithMessage("Due Date is required.")
            .GreaterThan(x => x.InvoiceDate)
            .WithMessage("Due Date must be after Invoice Date.");

        RuleFor(x => x.TotalAmount)
            .GreaterThan(0)
            .WithMessage("Total Amount must be greater than zero.");

        RuleFor(x => x.TotalCurrency)
            .NotEmpty()
            .WithMessage("Total Currency is required.")
            .Length(3)
            .WithMessage("Currency must be a 3-letter code.");

        RuleFor(x => x.Notes)
            .MaximumLength(1000)
            .WithMessage("Notes cannot exceed 1000 characters.");

        RuleFor(x => x.Items)
            .NotEmpty()
            .WithMessage("At least one item is required.");

        RuleForEach(x => x.Items)
            .SetValidator(new CreatePurchaseInvoiceItemCommandValidator());
    }
}

public class CreatePurchaseInvoiceItemCommandValidator : AbstractValidator<CreatePurchaseInvoiceItemCommand>
{
    public CreatePurchaseInvoiceItemCommandValidator()
    {
        RuleFor(x => x.MaterialId)
            .NotEmpty()
            .WithMessage("Material ID is required.");

        RuleFor(x => x.MaterialName)
            .NotEmpty()
            .WithMessage("Material Name is required.")
            .MaximumLength(200)
            .WithMessage("Material Name cannot exceed 200 characters.");

        RuleFor(x => x.InvoicedQuantityValue)
            .GreaterThan(0)
            .WithMessage("Invoiced Quantity must be greater than zero.");

        RuleFor(x => x.InvoicedQuantityUnit)
            .NotEmpty()
            .WithMessage("Invoiced Quantity Unit is required.");

        RuleFor(x => x.UnitPriceAmount)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Unit Price cannot be negative.");

        RuleFor(x => x.UnitPriceCurrency)
            .NotEmpty()
            .WithMessage("Unit Price Currency is required.")
            .Length(3)
            .WithMessage("Currency must be a 3-letter code.");

        RuleFor(x => x.Notes)
            .MaximumLength(500)
            .WithMessage("Notes cannot exceed 500 characters.");
    }
}
