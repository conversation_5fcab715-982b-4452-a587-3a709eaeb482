using Tebipaints.Application.Abstractions.Messaging;

namespace Tebipaints.Application.Procurement.GoodsReceipts.CreateGoodsReceipt;

public class CreateGoodsReceiptCommand : ICommand<Guid>
{
    public Guid PurchaseOrderId { get; set; }
    public string ReceivedBy { get; set; } = string.Empty;
    public string? DeliveryNote { get; set; }
    public string? SupplierReference { get; set; }
    public DateTime? ReceivedDate { get; set; }
    public List<CreateGoodsReceiptItemCommand> Items { get; set; } = new();
}

public class CreateGoodsReceiptItemCommand
{
    public Guid MaterialId { get; set; }
    public string MaterialName { get; set; } = string.Empty;
    public decimal OrderedQuantityValue { get; set; }
    public string OrderedQuantityUnit { get; set; } = string.Empty;
    public decimal ReceivedQuantityValue { get; set; }
    public string ReceivedQuantityUnit { get; set; } = string.Empty;
    public decimal UnitPriceAmount { get; set; }
    public string UnitPriceCurrency { get; set; } = string.Empty;
    public string? Notes { get; set; }
    public string QualityStatus { get; set; } = "Pending";
}
