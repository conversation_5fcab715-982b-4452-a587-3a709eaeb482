using FluentValidation;

namespace Tebipaints.Application.Procurement.GoodsReceipts.CreateGoodsReceipt;

public class CreateGoodsReceiptCommandValidator : AbstractValidator<CreateGoodsReceiptCommand>
{
    public CreateGoodsReceiptCommandValidator()
    {
        RuleFor(x => x.PurchaseOrderId)
            .NotEmpty()
            .WithMessage("Purchase Order ID is required.");

        RuleFor(x => x.ReceivedBy)
            .NotEmpty()
            .WithMessage("Received By is required.")
            .MaximumLength(100)
            .WithMessage("Received By cannot exceed 100 characters.");

        RuleFor(x => x.DeliveryNote)
            .MaximumLength(200)
            .WithMessage("Delivery Note cannot exceed 200 characters.");

        RuleFor(x => x.SupplierReference)
            .MaximumLength(100)
            .WithMessage("Supplier Reference cannot exceed 100 characters.");

        RuleFor(x => x.Items)
            .NotEmpty()
            .WithMessage("At least one item is required.");

        RuleForEach(x => x.Items)
            .SetValidator(new CreateGoodsReceiptItemCommandValidator());
    }
}

public class CreateGoodsReceiptItemCommandValidator : AbstractValidator<CreateGoodsReceiptItemCommand>
{
    public CreateGoodsReceiptItemCommandValidator()
    {
        RuleFor(x => x.MaterialId)
            .NotEmpty()
            .WithMessage("Material ID is required.");

        RuleFor(x => x.MaterialName)
            .NotEmpty()
            .WithMessage("Material Name is required.")
            .MaximumLength(200)
            .WithMessage("Material Name cannot exceed 200 characters.");

        RuleFor(x => x.OrderedQuantityValue)
            .GreaterThan(0)
            .WithMessage("Ordered Quantity must be greater than zero.");

        RuleFor(x => x.OrderedQuantityUnit)
            .NotEmpty()
            .WithMessage("Ordered Quantity Unit is required.");

        RuleFor(x => x.ReceivedQuantityValue)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Received Quantity cannot be negative.");

        RuleFor(x => x.ReceivedQuantityUnit)
            .NotEmpty()
            .WithMessage("Received Quantity Unit is required.");

        RuleFor(x => x.UnitPriceAmount)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Unit Price cannot be negative.");

        RuleFor(x => x.UnitPriceCurrency)
            .NotEmpty()
            .WithMessage("Unit Price Currency is required.");

        RuleFor(x => x.Notes)
            .MaximumLength(500)
            .WithMessage("Notes cannot exceed 500 characters.");

        RuleFor(x => x.QualityStatus)
            .NotEmpty()
            .WithMessage("Quality Status is required.")
            .Must(BeValidQualityStatus)
            .WithMessage("Quality Status must be one of: Pending, Approved, Rejected, RequiresInspection.");
    }

    private static bool BeValidQualityStatus(string status)
    {
        var validStatuses = new[] { "Pending", "Approved", "Rejected", "RequiresInspection" };
        return validStatuses.Contains(status);
    }
}
