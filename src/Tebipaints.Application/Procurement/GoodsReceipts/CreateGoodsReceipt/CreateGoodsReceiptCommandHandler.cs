using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Errors;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Procurement.GoodsReceipts.CreateGoodsReceipt;

public class CreateGoodsReceiptCommandHandler : ICommandHandler<CreateGoodsReceiptCommand, Guid>
{
    private readonly IGoodsReceiptRepository _goodsReceiptRepository;
    private readonly IPurchaseOrderRepository _purchaseOrderRepository;
    private readonly IDocumentNumberGenerator _documentNumberGenerator;
    private readonly IUnitOfWork _unitOfWork;

    public CreateGoodsReceiptCommandHandler(
        IGoodsReceiptRepository goodsReceiptRepository,
        IPurchaseOrderRepository purchaseOrderRepository,
        IDocumentNumberGenerator documentNumberGenerator,
        IUnitOfWork unitOfWork)
    {
        _goodsReceiptRepository = goodsReceiptRepository;
        _purchaseOrderRepository = purchaseOrderRepository;
        _documentNumberGenerator = documentNumberGenerator;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<Guid>> Handle(CreateGoodsReceiptCommand request, CancellationToken cancellationToken)
    {
        // 1. Get and validate purchase order
        var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(request.PurchaseOrderId, cancellationToken);
        if (purchaseOrder is null)
        {
            return Result.Failure<Guid>(ProcurementErrors.PurchaseOrderNotFound(request.PurchaseOrderId));
        }

        if (purchaseOrder.Status != Domain.Procurement.Enums.PurchaseOrderStatus.Approved)
        {
            return Result.Failure<Guid>(ProcurementErrors.CanOnlyReceiveApprovedOrder());
        }

        // 2. Generate receipt number
        var receiptNumber = await _documentNumberGenerator.GetNextGoodsReceiptNumberAsync(cancellationToken);

        // 3. Create goods receipt items
        var goodsReceiptItems = new List<GoodsReceiptItem>();
        foreach (var itemRequest in request.Items)
        {
            // Validate material exists in purchase order
            var poItem = purchaseOrder.Items.FirstOrDefault(i => i.MaterialId == itemRequest.MaterialId);
            if (poItem is null)
            {
                return Result.Failure<Guid>(ProcurementErrors.MaterialNotInPurchaseOrder(itemRequest.MaterialId));
            }

            // Convert units
            var orderedQuantityResult = UnitOfMeasureConverter.FromString(itemRequest.OrderedQuantityUnit);
            if (orderedQuantityResult.IsFailure)
            {
                return Result.Failure<Guid>(orderedQuantityResult.Error);
            }

            var receivedQuantityResult = UnitOfMeasureConverter.FromString(itemRequest.ReceivedQuantityUnit);
            if (receivedQuantityResult.IsFailure)
            {
                return Result.Failure<Guid>(receivedQuantityResult.Error);
            }

            var orderedQuantity = new Measurement(itemRequest.OrderedQuantityValue, orderedQuantityResult.Value);
            var receivedQuantity = new Measurement(itemRequest.ReceivedQuantityValue, receivedQuantityResult.Value);

            // Convert currency
            var currency = Currency.FromCode(itemRequest.UnitPriceCurrency);
            var unitPrice = new Money(itemRequest.UnitPriceAmount, currency);

            // Parse quality status
            if (!Enum.TryParse<QualityStatus>(itemRequest.QualityStatus, out var qualityStatus))
            {
                qualityStatus = QualityStatus.Pending;
            }

            // Create goods receipt item
            var goodsReceiptItemResult = GoodsReceiptItem.Create(
                itemRequest.MaterialId,
                itemRequest.MaterialName,
                orderedQuantity,
                receivedQuantity,
                unitPrice,
                itemRequest.Notes,
                qualityStatus);

            if (goodsReceiptItemResult.IsFailure)
            {
                return Result.Failure<Guid>(goodsReceiptItemResult.Error);
            }

            goodsReceiptItems.Add(goodsReceiptItemResult.Value);
        }
        
        // 3a. Ensure we're using the right DateTime Kind
        DateTime? receivedDate = null;
        if (request.ReceivedDate is not null)
        {
            receivedDate = DateTime.SpecifyKind((DateTime)request.ReceivedDate, DateTimeKind.Utc);
        }
        

        // 4. Create goods receipt
        var goodsReceiptResult = GoodsReceipt.Create(
            receiptNumber,
            request.PurchaseOrderId,
            purchaseOrder.OrderNumber.ToString(),
            purchaseOrder.SupplierId,
            request.ReceivedBy,
            goodsReceiptItems,
            request.DeliveryNote,
            request.SupplierReference,
            receivedDate);

        if (goodsReceiptResult.IsFailure)
        {
            return Result.Failure<Guid>(goodsReceiptResult.Error);
        }

        // 5. Save goods receipt
        _goodsReceiptRepository.Add(goodsReceiptResult.Value);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success(goodsReceiptResult.Value.Id);
    }
}
