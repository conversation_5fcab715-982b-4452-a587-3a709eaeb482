using Tebipaints.Application.Abstractions.Messaging;

namespace Tebipaints.Application.Procurement.GoodsReceipts.ListGoodsReceipts;

public class ListGoodsReceiptsQuery : IQuery<IReadOnlyList<GoodsReceiptResponse>>
{
    public Guid? PurchaseOrderId { get; set; }
    public Guid? SupplierId { get; set; }
    public string? Status { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 50;
}
