namespace Tebipaints.Application.Procurement.GoodsReceipts.ListGoodsReceipts;

public class GoodsReceiptResponse
{
    public Guid Id { get; set; }
    public string ReceiptNumber { get; set; } = string.Empty;
    public Guid PurchaseOrderId { get; set; }
    public string PurchaseOrderNumber { get; set; } = string.Empty;
    public Guid SupplierId { get; set; }
    public DateTime ReceivedDate { get; set; }
    public string ReceivedBy { get; set; } = string.Empty;
    public string? DeliveryNote { get; set; }
    public string? SupplierReference { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal TotalValue { get; set; }
    public string Currency { get; set; } = string.Empty;
    public int ItemCount { get; set; }
    public List<GoodsReceiptItemResponse> Items { get; set; } = new();
}

public class GoodsReceiptItemResponse
{
    public Guid MaterialId { get; set; }
    public string MaterialName { get; set; } = string.Empty;
    public decimal OrderedQuantityValue { get; set; }
    public string OrderedQuantityUnit { get; set; } = string.Empty;
    public decimal ReceivedQuantityValue { get; set; }
    public string ReceivedQuantityUnit { get; set; } = string.Empty;
    public decimal UnitPriceAmount { get; set; }
    public string UnitPriceCurrency { get; set; } = string.Empty;
    public decimal TotalValue { get; set; }
    public decimal VariancePercentage { get; set; }
    public bool IsWithinTolerance { get; set; }
    public bool IsOverReceived { get; set; }
    public bool IsUnderReceived { get; set; }
    public bool IsFullyReceived { get; set; }
    public string QualityStatus { get; set; } = string.Empty;
    public string? Notes { get; set; }
}
