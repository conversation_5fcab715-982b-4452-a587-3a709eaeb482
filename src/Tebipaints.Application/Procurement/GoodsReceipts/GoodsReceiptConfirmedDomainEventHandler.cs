using MediatR;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Inventory;
using Tebipaints.Domain.Procurement.Events;

namespace Tebipaints.Application.Procurement.GoodsReceipts;

public class GoodsReceiptConfirmedDomainEventHandler : INotificationHandler<GoodsReceiptConfirmedDomainEvent>
{
    private readonly IMaterialInventoryRepository _inventoryRepository;
    private readonly IUnitOfWork _unitOfWork;

    public GoodsReceiptConfirmedDomainEventHandler(
        IMaterialInventoryRepository inventoryRepository,
        IUnitOfWork unitOfWork)
    {
        _inventoryRepository = inventoryRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task Handle(GoodsReceiptConfirmedDomainEvent notification, CancellationToken cancellationToken)
    {
        // Process each received item
        foreach (var item in notification.Items)
        {
            // Find or create material inventory
            var inventory = await _inventoryRepository
                .GetByIdAsync(item.MaterialId, cancellationToken);

            if (inventory is null)
            {
                // Create new inventory with default settings
                var createResult = MaterialInventory.Create(
                    item.MaterialId,
                    InventoryLocation.Factory, // Default location
                    minimumStockLevel: 0,
                    reorderPoint: 0);

                if (createResult.IsFailure)
                {
                    // Log error and continue with next item
                    continue;
                }

                inventory = createResult.Value;
                _inventoryRepository.Add(inventory, cancellationToken);
            }

            // Add stock to inventory with goods receipt reference
            var reference = $"Goods Receipt: {notification.ReceiptNumber} (PO: {notification.PurchaseOrderNumber})";
            var result = inventory.AddStock(
                (double)item.ReceivedQuantity.Value,
                reference);

            if (result.IsFailure)
            {
                // Log error and continue with next item
                continue;
            }
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);
    }
}
