using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Application.Abstractions.Reporting;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement.Errors;

namespace Tebipaints.Application.Procurement.GoodsReceipts.GenerateGoodsReceiptPdf;

public class GenerateGoodReceiptPdfQueryHandler : IQueryHandler<GenerateGoodsReceiptPdfQuery, byte[]>
{
    private readonly IPdfGenerator _pdfGenerator;
    private readonly ISqlConnectionFactory _sqlConnectionFactory;

    public GenerateGoodReceiptPdfQueryHandler(IPdfGenerator pdfGenerator, ISqlConnectionFactory sqlConnectionFactory)
    {
        _pdfGenerator = pdfGenerator;
        _sqlConnectionFactory = sqlConnectionFactory;
    }

    public async Task<Result<byte[]>> Handle(GenerateGoodsReceiptPdfQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var connection = _sqlConnectionFactory.CreateConnection();

            const string sql = @"
                        select
                            gr.received_date as ReceivedDate,
                            gr.receipt_number as ReceiptNumber,
                            gr.purchase_order_number as PurchaseOrderNumber,
                            gr.delivery_note as DeliveryNoteNumber,
                            s.name as SupplierName,
                            s.contact_info_address as SupplierAddress,
                            s.contact_info_email as SupplierEmail,
                            s.contact_info_phone as SupplierPhone,
                            gri.id as GoodsReceiptItemId,
                            gri.material_name as ItemName,
                            gri.received_quantity_unit as ItemReceivedQuantityUnit,
                            gri.received_quantity_value as ItemReceivedQuantityValue,
                            gri.ordered_quantity_value as ItemOrderedQuantityValue,
                            gri.unit_price_amount as ItemUnitPrice,
                            gri.unit_price_currency as ItemUnitPriceCurrency,
                            gri.notes as ItemNotes
                        from goods_receipts gr 
                        left join goods_receipt_items gri on gr.id = gri.goods_receipt_id
                        left join suppliers s on gr.supplier_id = s.id
                        where gr.id = @GoodsReceiptId
                        ";

            var rows = await connection.QueryAsync<GoodsReceiptRawRow>(sql, new { request.GoodsReceiptId });

            if (!rows.Any())
                return Result.Failure<byte[]>(ProcurementErrors.GoodsReceiptNotFound(request.GoodsReceiptId));

            var first = rows.First();
            
            // Log the retrieved data for debugging
            Console.WriteLine($"Retrieved {rows.Count()} rows for goods receipt {request.GoodsReceiptId}");
            Console.WriteLine($"First row - ReceiptNumber: {first.ReceiptNumber}, SupplierName: {first.SupplierName}");

            var dto = new GoodsReceiptDto
            {
                ReceivedDate = first.ReceivedDate,
                ReceiptNumber = first.ReceiptNumber ?? string.Empty,
                PurchaseOrderNumber = first.PurchaseOrderNumber ?? string.Empty,
                DeliveryNoteNumber = first.DeliveryNoteNumber ?? string.Empty,
                SupplierName = first.SupplierName ?? string.Empty,
                SupplierAddress = first.SupplierAddress ?? string.Empty,
                SupplierEmail = first.SupplierEmail ?? string.Empty,
                SupplierPhone = first.SupplierPhone ?? string.Empty,
                Items = new List<GoodsReceiptItemDto>()
            };

            // Filter out rows where GoodsReceiptItemId is default (empty) - these are from the LEFT JOIN when no items exist
            var validItemRows = rows.Where(r => r.GoodsReceiptItemId != Guid.Empty).ToList();
            
            Console.WriteLine($"Valid item rows: {validItemRows.Count}");

            foreach (var row in validItemRows)
            {
                if (dto.Items.All(i => i.GoodsReceiptItemId != row.GoodsReceiptItemId))
                {
                    dto.Items.Add(new GoodsReceiptItemDto
                    {
                        GoodsReceiptItemId = row.GoodsReceiptItemId,
                        ItemName = row.ItemName ?? string.Empty,
                        ItemReceivedQuantityUnit = row.ItemReceivedQuantityUnit ?? string.Empty,
                        ItemReceivedQuantityValue = row.ItemReceivedQuantityValue,
                        ItemOrderedQuantityValue = row.ItemOrderedQuantityValue,
                        ItemUnitPrice = row.ItemUnitPrice,
                        ItemUnitPriceCurrency = row.ItemUnitPriceCurrency ?? string.Empty,
                        ItemNotes = row.ItemNotes ?? string.Empty
                    });
                }
            }
            
            Console.WriteLine($"Final DTO - Items count: {dto.Items.Count}, ReceiptNumber: {dto.ReceiptNumber}");

            var pdfBytes = await _pdfGenerator.GenerateGoodsReceiptPdf(dto);
            
            return Result.Success(pdfBytes);
        }
        catch (Exception ex)
        {
            // Log the exception for debugging
            throw new InvalidOperationException($"Failed to generate goods receipt PDF for ID {request.GoodsReceiptId}", ex);
        }
    }

    private class GoodsReceiptRawRow
    {
	    public DateTime ReceivedDate { get; set; }
	    public string ReceiptNumber { get; set; }
	    public string PurchaseOrderNumber { get; set; }
	    public string DeliveryNoteNumber { get; set; }
	    public Guid GoodsReceiptItemId { get; set; }
	    public string ItemName { get; set; }
	    public string ItemReceivedQuantityUnit { get; set; }
	    public int? ItemReceivedQuantityValue { get; set; }
	    public int? ItemOrderedQuantityValue { get; set; }
	    public decimal? ItemUnitPrice { get; set; }
	    public string ItemUnitPriceCurrency { get; set; }
	    public string ItemNotes { get; set; }
	    public string SupplierName { get; set; }
	    public string SupplierAddress { get; set; }
	    public string SupplierEmail { get; set; }
	    public string SupplierPhone { get; set; }
    }
}