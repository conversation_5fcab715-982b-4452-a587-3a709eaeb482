namespace Tebipaints.Application.Procurement.GoodsReceipts.GenerateGoodsReceiptPdf;

public class GoodsReceiptDto
{
    public GoodsReceiptDto()
    {
        Items = new List<GoodsReceiptItemDto>();
    }
    
    public DateTime ReceivedDate { get; set; }
    public string ReceiptNumber { get; set; } = string.Empty;
    public string PurchaseOrderNumber { get; set; } = string.Empty;
    public string DeliveryNoteNumber { get; set; } = string.Empty;
    public string SupplierName { get; set; } = string.Empty;
    public string SupplierAddress { get; set; } = string.Empty;
    public string SupplierEmail { get; set; } = string.Empty;
    public string SupplierPhone { get; set; } = string.Empty;
    public List<GoodsReceiptItemDto> Items { get; set; }
}

public class GoodsReceiptItemDto
{
    public Guid GoodsReceiptItemId { get; set; }
    public string ItemName { get; set; } = string.Empty;
    public string ItemReceivedQuantityUnit { get; set; } = string.Empty;
    public int? ItemReceivedQuantityValue { get; set; }
    public int? ItemOrderedQuantityValue { get; set; }
    public decimal? ItemUnitPrice { get; set; }
    public string ItemUnitPriceCurrency { get; set; } = string.Empty;
    public string ItemNotes { get; set; } = string.Empty;
}