using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Errors;

namespace Tebipaints.Application.Procurement.GoodsReceipts.ConfirmGoodsReceipt;

public class ConfirmGoodsReceiptCommandHandler : ICommandHandler<ConfirmGoodsReceiptCommand>
{
    private readonly IGoodsReceiptRepository _goodsReceiptRepository;
    private readonly IPurchaseOrderRepository _purchaseOrderRepository;
    private readonly IUnitOfWork _unitOfWork;

    public ConfirmGoodsReceiptCommandHandler(
        IGoodsReceiptRepository goodsReceiptRepository,
        IPurchaseOrderRepository purchaseOrderRepository,
        IUnitOfWork unitOfWork)
    {
        _goodsReceiptRepository = goodsReceiptRepository;
        _purchaseOrderRepository = purchaseOrderRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(ConfirmGoodsReceiptCommand request, CancellationToken cancellationToken)
    {
        // 1. Get goods receipt
        var goodsReceipt = await _goodsReceiptRepository.GetByIdAsync(request.GoodsReceiptId, cancellationToken);
        if (goodsReceipt is null)
        {
            return Result.Failure(ProcurementErrors.GoodsReceiptNotFound(request.GoodsReceiptId));
        }

        // 2. Confirm goods receipt
        var confirmResult = goodsReceipt.Confirm();
        if (confirmResult.IsFailure)
        {
            return Result.Failure(confirmResult.Error);
        }

        // 3. Update purchase order received quantities
        var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(goodsReceipt.PurchaseOrderId, cancellationToken);
        if (purchaseOrder is not null)
        {
            foreach (var item in goodsReceipt.Items)
            {
                var updateResult = purchaseOrder.UpdateReceivedQuantity(item.MaterialId, item.ReceivedQuantity);
                if (updateResult.IsFailure)
                {
                    // Log warning but continue - this shouldn't fail the confirmation
                    continue;
                }
            }

            _purchaseOrderRepository.Update(purchaseOrder);
        }

        // 4. Save changes
        _goodsReceiptRepository.Update(goodsReceipt);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
