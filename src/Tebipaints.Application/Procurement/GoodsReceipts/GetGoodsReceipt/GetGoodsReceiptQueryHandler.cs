using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Application.Procurement.GoodsReceipts.ListGoodsReceipts;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement.Errors;

namespace Tebipaints.Application.Procurement.GoodsReceipts.GetGoodsReceipt;

public class GetGoodsReceiptQueryHandler : IQueryHandler<GetGoodsReceiptQuery, GoodsReceiptResponse>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;

    public GetGoodsReceiptQueryHandler(ISqlConnectionFactory sqlConnectionFactory)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
    }

    public async Task<Result<GoodsReceiptResponse>> Handle(GetGoodsReceiptQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();

        const string sql = """
            WITH receipt_totals AS (
                SELECT 
                    goods_receipt_id,
                    COUNT(*) as item_count,
                    SUM(unit_price_amount * received_quantity_value) as total_value,
                    STRING_AGG(DISTINCT unit_price_currency, ',') as currency
                FROM goods_receipt_items
                WHERE goods_receipt_id = @GoodsReceiptId
                GROUP BY goods_receipt_id
            )
            SELECT 
                gr.id,
                gr.receipt_number as ReceiptNumber,
                gr.purchase_order_id as PurchaseOrderId,
                gr.purchase_order_number as PurchaseOrderNumber,
                gr.supplier_id as SupplierId,
                gr.received_date as ReceivedDate,
                gr.received_by as ReceivedBy,
                gr.delivery_note as DeliveryNote,
                gr.supplier_reference as SupplierReference,
                gr.status as Status,
                COALESCE(rt.total_value, 0) as TotalValue,
                COALESCE(rt.currency, 'GHS') as Currency,
                COALESCE(rt.item_count, 0) as ItemCount,
                -- Item details
                gri.material_id as MaterialId,
                gri.material_name as MaterialName,
                gri.ordered_quantity_value as OrderedQuantityValue,
                gri.ordered_quantity_unit as OrderedQuantityUnit,
                gri.received_quantity_value as ReceivedQuantityValue,
                gri.received_quantity_unit as ReceivedQuantityUnit,
                gri.unit_price_amount as UnitPriceAmount,
                gri.unit_price_currency as UnitPriceCurrency,
                (gri.unit_price_amount * gri.received_quantity_value) as TotalValue,
                CASE 
                    WHEN gri.ordered_quantity_value = 0 THEN 0
                    ELSE ROUND(((gri.received_quantity_value - gri.ordered_quantity_value) / gri.ordered_quantity_value) * 100, 2)
                END as VariancePercentage,
                CASE 
                    WHEN gri.ordered_quantity_value = 0 THEN true
                    ELSE ABS(((gri.received_quantity_value - gri.ordered_quantity_value) / gri.ordered_quantity_value) * 100) <= 5.0
                END as IsWithinTolerance,
                (gri.received_quantity_value > gri.ordered_quantity_value) as IsOverReceived,
                (gri.received_quantity_value < gri.ordered_quantity_value) as IsUnderReceived,
                (gri.received_quantity_value >= gri.ordered_quantity_value OR 
                 (gri.received_quantity_value >= gri.ordered_quantity_value * 0.95 AND 
                  ABS(((gri.received_quantity_value - gri.ordered_quantity_value) / gri.ordered_quantity_value) * 100) <= 5.0)) as IsFullyReceived,
                gri.quality_status as QualityStatus,
                gri.notes as Notes
            FROM goods_receipts gr
            LEFT JOIN receipt_totals rt ON gr.id = rt.goods_receipt_id
            LEFT JOIN goods_receipt_items gri ON gr.id = gri.goods_receipt_id
            WHERE gr.id = @GoodsReceiptId
            ORDER BY gri.material_name
            """;

        var receiptDictionary = new Dictionary<Guid, GoodsReceiptResponse>();

        var receipts = await connection.QueryAsync<GoodsReceiptResponse, GoodsReceiptItemResponse, GoodsReceiptResponse>(
            sql,
            (receipt, item) =>
            {
                if (!receiptDictionary.TryGetValue(receipt.Id, out var existingReceipt))
                {
                    existingReceipt = receipt;
                    existingReceipt.Items = new List<GoodsReceiptItemResponse>();
                    receiptDictionary.Add(receipt.Id, existingReceipt);
                }

                if (item != null && item.MaterialId != Guid.Empty)
                {
                    existingReceipt.Items.Add(item);
                }

                return existingReceipt;
            },
            new { GoodsReceiptId = request.GoodsReceiptId },
            splitOn: "MaterialId");

        var receipt = receiptDictionary.Values.FirstOrDefault();
        if (receipt == null)
        {
            return Result.Failure<GoodsReceiptResponse>(ProcurementErrors.GoodsReceiptNotFound(request.GoodsReceiptId));
        }

        return Result.Success(receipt);
    }
}
