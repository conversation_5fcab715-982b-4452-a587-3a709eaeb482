using Tebipaints.Application.Abstractions.Messaging;

namespace Tebipaints.Application.Procurement.Suppliers.AddSupplierContract;

public sealed record AddSupplierContractCommand(
    Guid SupplierId,
    DateTime StartDate,
    DateTime EndDate,
    List<AddContractTermCommand> Terms,
    List<AddContractedMaterialCommand> Materials) : ICommand<Guid>;
    
    
public sealed record AddContractTermCommand(
    string Description,
    string Type,
    List<string> Values,
    DateTime ExpirationDate);

public sealed record AddContractedMaterialCommand(
    Guid MaterialId,
    string MaterialName,
    decimal UnitPrice,
    decimal MinimumOrder,
    string MinimumOrderUnit,
    decimal? MaximumOrder,
    string? MaximumOrderUnit,
    int LeadTimeDays);