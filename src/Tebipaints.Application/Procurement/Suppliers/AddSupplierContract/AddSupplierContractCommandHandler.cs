using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Material;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Enums;
using Tebipaints.Domain.Procurement.Errors;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Procurement.Suppliers.AddSupplierContract;

public class AddSupplierContractCommandHandler : ICommandHandler<AddSupplierContractCommand, Guid>
{
    private readonly ISupplierRepository _supplierRepository;
    private readonly IMaterialRepository _materialRepository;
    private readonly IDocumentNumberGenerator _documentNumberGenerator;
    private readonly IUnitOfWork _unitOfWork;

    public AddSupplierContractCommandHandler(
        ISupplierRepository supplierRepository,
        IMaterialRepository materialRepository,
        IUnitOfWork unitOfWork,
        IDocumentNumberGenerator documentNumberGenerator)
    {
        _supplierRepository = supplierRepository;
        _materialRepository = materialRepository;
        _unitOfWork = unitOfWork;
        _documentNumberGenerator = documentNumberGenerator;
    }

    public async Task<Result<Guid>> Handle(AddSupplierContractCommand request, CancellationToken cancellationToken)
    {
        // 1. Get the supplier
        var supplier = await _supplierRepository.GetByIdAsync(
            request.SupplierId,
            cancellationToken);

        if (supplier is null)
        {
            return Result.Failure<Guid>(ProcurementErrors.SupplierNotFound(request.SupplierId));
        }

        // 2. Create contract terms
        var terms = new List<ContractTerm>();
        foreach (var termCommand in request.Terms)
        {
            if (!Enum.TryParse<TermType>(termCommand.Type, true, out var termType))
            {
                return Result.Failure<Guid>(ProcurementErrors.InvalidTermType(termCommand.Type));
            }
            
            // Convert string values to TermValue enum values
            var termValues = termCommand.Values
                .Select(v => Enum.TryParse<TermValue>(v, true, out var termValue) 
                    ? termValue 
                    : throw new ArgumentException($"Invalid term value: {v}"))
                .ToList();

            var expirationDate = DateTime.SpecifyKind(termCommand.ExpirationDate, DateTimeKind.Utc);

            var term = ContractTerm.Create(
                termCommand.Description,
                termType,
                termValues,
                expirationDate);

            terms.Add(term);
        }

        // 3. Create and add contract first
        var startDate = DateTime.SpecifyKind(request.StartDate, DateTimeKind.Utc);
        var endDate = DateTime.SpecifyKind(request.EndDate, DateTimeKind.Utc);

        var contractNumber = await _documentNumberGenerator.GetNextContractNumberAsync(cancellationToken);
        var contractResult = Contract.Create(
            contractNumber,
            startDate,
            endDate,
            terms);

        if (contractResult.IsFailure)
        {
            return Result.Failure<Guid>(contractResult.Error);
        }

        var result = supplier.AddContract(contractResult.Value);
        if (result.IsFailure)
        {
            return Result.Failure<Guid>(result.Error);
        }

        // 4. Save the contract first
        _supplierRepository.UpdateSupplier(supplier);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // 5. Now add materials
        foreach (var materialCommand in request.Materials)
        {
            // Verify material exists
            var material = await _materialRepository.GetByIdAsync(materialCommand.MaterialId, cancellationToken);

            if (material is null)
            {
                return Result.Failure<Guid>(
                    ProcurementErrors.MaterialNotFound(materialCommand.MaterialId));
            }

            // convert minimum order unit
            var minOrderUnitResult = UnitOfMeasureConverter.FromString(materialCommand.MinimumOrderUnit);
            if (minOrderUnitResult.IsFailure)
            {
                return Result.Failure<Guid>(minOrderUnitResult.Error);
            }

            var minimumOrder = new Measurement(
                materialCommand.MinimumOrder,
                minOrderUnitResult.Value);

            // convert maximum order unit
            Measurement? maximumOrder = null;
            if (materialCommand.MaximumOrder.HasValue)
            {
                var maxOrderUnitResult = UnitOfMeasureConverter.FromString(materialCommand.MaximumOrderUnit!);

                if (maxOrderUnitResult.IsFailure)
                {
                    return Result.Failure<Guid>(maxOrderUnitResult.Error);
                }

                maximumOrder = new Measurement(
                    materialCommand.MaximumOrder.Value,
                    maxOrderUnitResult.Value);
            }

            var materialResult = ContractedMaterial.Create(
                materialCommand.MaterialId,
                materialCommand.MaterialName,
                Money.FromDecimal(materialCommand.UnitPrice, Currency.Usd),
                minimumOrder,
                maximumOrder,
                materialCommand.LeadTimeDays);

            if (materialResult.IsFailure)
            {
                return Result.Failure<Guid>(materialResult.Error);
            }

            contractResult.Value.AddMaterial(materialResult.Value);
        }

        // 6. Save again with materials
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success(contractResult.Value.Id);
    }
}