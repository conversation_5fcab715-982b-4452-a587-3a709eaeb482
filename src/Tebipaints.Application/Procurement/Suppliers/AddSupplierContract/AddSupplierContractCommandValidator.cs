using FluentValidation;

namespace Tebipaints.Application.Procurement.Suppliers.AddSupplierContract;

public class AddSupplierContractCommandValidator : AbstractValidator<AddSupplierContractCommand>
{
    public AddSupplierContractCommandValidator()
    {
        RuleFor(v => v.SupplierId).NotEmpty();
        RuleFor(v => v.StartDate).NotEmpty();
        RuleFor(v => v.EndDate).NotEmpty();
        RuleFor(v => v.Terms).NotEmpty();
        RuleFor(v => v.Materials).NotEmpty();
    }
}