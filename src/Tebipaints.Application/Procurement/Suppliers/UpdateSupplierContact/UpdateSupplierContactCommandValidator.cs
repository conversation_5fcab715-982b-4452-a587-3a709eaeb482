using FluentValidation;

namespace Tebipaints.Application.Procurement.Suppliers.UpdateSupplierContact;

public class UpdateSupplierContactCommandValidator : AbstractValidator<UpdateSupplierContactCommand>
{
    public UpdateSupplierContactCommandValidator()
    {
        RuleFor(s => s.SupplierId).NotEmpty();
        RuleFor(s => s.ContactPerson).NotEmpty();
        RuleFor(s => s.Email).NotEmpty().EmailAddress();
        RuleFor(s => s.Phone).NotEmpty();
    }
}