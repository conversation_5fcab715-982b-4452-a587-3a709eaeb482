using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Errors;

namespace Tebipaints.Application.Procurement.Suppliers.UpdateSupplierContact;

public class UpdateSupplierContactCommandHandler : ICommandHandler<UpdateSupplierContactCommand>
{
    private readonly ISupplierRepository _supplierRepository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateSupplierContactCommandHandler(ISupplierRepository supplierRepository, IUnitOfWork unitOfWork)
    {
        _supplierRepository = supplierRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(
        UpdateSupplierContactCommand request,
        CancellationToken cancellationToken)
    {
        var supplier = await _supplierRepository.GetByIdAsync(
            request.SupplierId,
            cancellationToken);

        if (supplier is null)
        {
            return Result.Failure(ProcurementErrors.SupplierNotFound(request.SupplierId));
        }

        var contactInfoResult = ContactInfo.Create(
            request.ContactPerson,
            request.Email,
            request.Phone,
            request.Address);

        if (contactInfoResult.IsFailure)
        {
            return Result.Failure(contactInfoResult.Error);
        }

        var result = supplier.UpdateContactInfo(contactInfoResult.Value);
        if (result.IsFailure)
        {
            return Result.Failure(result.Error);
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}