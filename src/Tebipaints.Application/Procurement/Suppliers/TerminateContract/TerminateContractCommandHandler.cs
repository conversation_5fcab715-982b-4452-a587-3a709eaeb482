using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Errors;

namespace Tebipaints.Application.Procurement.Suppliers.TerminateContract;

public class TerminateContractCommandHandler : ICommandHandler<TerminateContractCommand>
{
    private readonly ISupplierRepository _supplierRepository;
    private readonly IUnitOfWork _unitOfWork;

    public TerminateContractCommandHandler(ISupplierRepository supplierRepository, IUnitOfWork unitOfWork)
    {
        _supplierRepository = supplierRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(
        TerminateContractCommand request,
        CancellationToken cancellationToken)
    {
        var supplier = await _supplierRepository
            .GetByContractIdAsync(request.ContractId, cancellationToken);

        if (supplier is null)
        {
            return Result.Failure(ProcurementErrors.ContractNotFound(request.ContractId));
        }

        var contract = supplier.Contracts
            .First(c => c.Id == request.ContractId);

        var result = contract.Terminate(request.Reason);
        if (result.IsFailure)
        {
            return Result.Failure(result.Error);
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        return Result.Success();
    }
}