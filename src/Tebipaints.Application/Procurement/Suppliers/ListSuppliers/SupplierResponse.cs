namespace Tebipaints.Application.Procurement.Suppliers.ListSuppliers;

public sealed class SupplierResponse
{
    public Guid Id { get; init; }
    public string Code { get; init; }
    public string Name { get; init; }
    public string Status { get; init; }
    public ContactInfoResponse ContactInfo { get; set; }
    public List<ContractResponse> Contracts { get; set; }
    public ContractResponse? ActiveContract { get; init; }
}

public sealed class ContactInfoResponse
{
    public string ContactPerson { get; init; }
    public string Email { get; init; }
    public string Phone { get; init; }
    public string Address { get; init; }
}

public sealed class ContractResponse
{
    public Guid Id { get; init; }
    public string ContractNumber { get; init; }
    public DateTime StartDate { get; init; }
    public DateTime EndDate { get; init; }
    public string Status { get; init; }
    public bool IsActive { get; init; }
    public List<ContractTermResponse> Terms { get; set; }
    public List<ContractedMaterialResponse> Materials { get; set; } = new();
}

public sealed class ContractTermResponse
{
    public string Description { get; init; }
    public string Type { get; init; }
    public DateTime ExpirationDate { get; init; }
}

public sealed class ContractedMaterialResponse
{
    public Guid MaterialId { get; init; }
    public string MaterialName { get; init; }
    public decimal Price { get; init; }
    public string Currency { get; init; }
    public decimal MinimumOrder { get; init; }
    public decimal MaximumOrder { get; init; }
    public string Unit { get; init; }
    public int LeadTime { get; init; }
}