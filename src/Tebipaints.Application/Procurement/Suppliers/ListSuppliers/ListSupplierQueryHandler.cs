using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Application.Procurement.Suppliers.ListSuppliers;

public sealed class ListSupplierQueryHandler : IQueryHandler<ListSuppliersQuery, IReadOnlyList<SupplierResponse>>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;

    public ListSupplierQueryHandler(ISqlConnectionFactory sqlConnectionFactory)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
    }

    public async Task<Result<IReadOnlyList<SupplierResponse>>> Handle(ListSuppliersQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();
        
        const string sql = 
            """
            SELECT
                s.id,
                s.code,
                s.name,
                s.status,
                s.contact_info_contact_person as <PERSON><PERSON><PERSON>,
                s.contact_info_address as Address,
                s.contact_info_email as Email,
                s.contact_info_phone as Phone,
                c.id,
                c.contract_number_value as ContractNumber,
                c.start_date as StartDate,
                c.end_date as EndDate,
                c.status,
                ct.description,
                ct.type,
                ct.expiration_date as ExpirationDate,
                cm.material_id as MaterialId,
                cm.material_name as MaterialName,
                cm.unit_price_amount as Price,
                cm.unit_price_currency as Currency,
                cm.minimum_order_value as MinimumOrder,
                cm.maximum_order_value as MaximumOrder,
                cm.minimum_order_unit as Unit,
                cm.lead_time_days as LeadTime
            FROM suppliers s
            left join contracts c on c.supplier_id = s.id
            left join contract_terms ct on ct.contract_id = c.id
            left join contract_materials cm on cm.contract_id = c.id
            left join materials m on m.id = cm.material_id
            where s.status != 'Discontinued'
            order by s.name;
            """;

        var supplierDict = new Dictionary<Guid, SupplierResponse>();
        
        await connection.QueryAsync<
            SupplierResponse,
            ContactInfoResponse,
            ContractResponse,
            ContractTermResponse,
            ContractedMaterialResponse,
            SupplierResponse>(
            sql,
            (supplier, contactInfo, contract, term, material) =>
            {
                if (!supplierDict.TryGetValue(supplier.Id, out var existingSupplier))
                {
                    existingSupplier = supplier;
                    existingSupplier.ContactInfo = contactInfo;
                    existingSupplier.Contracts = new List<ContractResponse>();
                    supplierDict.Add(supplier.Id, existingSupplier);
                }

                if (contract is not null && contract.Id != Guid.Empty)
                {
                    var existingContract = existingSupplier.Contracts
                        .FirstOrDefault(c => c.Id == contract.Id);

                    if (existingContract == null)
                    {
                        contract.Terms = new List<ContractTermResponse>();
                        contract.Materials = new List<ContractedMaterialResponse>();
                        existingSupplier.Contracts.Add(contract);
                        existingContract = contract;
                    }

                    if (term?.Description != null && term?.Type != null)
                    {
                        if (!existingContract.Terms.Any(t => 
                                t.Description == term.Description && 
                                t.Type == term.Type))
                        {
                            existingContract.Terms.Add(term);
                        }
                    }

                    if (material is not null && material.MaterialId != Guid.Empty)
                    {
                        if (existingContract.Materials.All(m => m.MaterialId != material.MaterialId))
                        {
                            existingContract.Materials.Add(material);
                        }
                    }
                }

                
                return existingSupplier;
            },
            splitOn: "ContactPerson,Id,Description,MaterialId");
        
        return Result.Success<IReadOnlyList<SupplierResponse>>(supplierDict.Values.ToList());
    }
}