using System.Data;
using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Application.Procurement.Suppliers.ListSuppliers;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement.Errors;

namespace Tebipaints.Application.Procurement.Suppliers.GetSupplier;

public class GetSupplierQueryHandler : IQueryHandler<GetSupplierQuery, SupplierResponse>
{
    private readonly ISqlConnectionFactory _sqlConnection;

    public GetSupplierQueryHandler(ISqlConnectionFactory sqlConnection)
    {
        _sqlConnection = sqlConnection;
    }

    public async Task<Result<SupplierResponse>> Handle(GetSupplierQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnection.CreateConnection();

        // Get supplier data
        const string supplierSql = """
                                       SELECT 
                                           id as SupplierId,
                                           code as Code,
                                           name as Name,
                                           status as Status,
                                           contact_info_contact_person as Contact<PERSON><PERSON>,
                                           contact_info_address as Address,
                                           contact_info_email as Email,
                                           contact_info_phone as Phone
                                       FROM suppliers 
                                       WHERE id = @id;
                                   """;

        var supplier =
            await connection.QuerySingleOrDefaultAsync<SupplierDto>(supplierSql, new { id = request.SupplierId });

        if (supplier == null)
            return Result.Failure<SupplierResponse>(ProcurementErrors.SupplierNotFound(request.SupplierId));

        // Get contracts with terms and materials
        const string contractsSql = """
                                        SELECT 
                                            c.id as ContractId,
                                            c.contract_number_value as ContractNumber,
                                            c.start_date as StartDate,
                                            c.end_date as EndDate,
                                            c.status as ContractStatus,
                                            
                                            ct.description as TermDescription,
                                            ct.type as TermType,
                                            ct.expiration_date as TermExpirationDate,
                                            
                                            cm.material_id as MaterialId,
                                            cm.material_name as MaterialName,
                                            cm.unit_price_amount as Price,
                                            cm.unit_price_currency as Currency,
                                            cm.minimum_order_value as MinimumOrder,
                                            cm.maximum_order_value as MaximumOrder,
                                            cm.minimum_order_unit as Unit,
                                            cm.lead_time_days as LeadTime
                                        FROM contracts c
                                        LEFT JOIN contract_terms ct ON ct.contract_id = c.id
                                        LEFT JOIN contract_materials cm ON cm.contract_id = c.id
                                        WHERE c.supplier_id = @id;
                                    """;

        var contractResults = await connection
            .QueryAsync<ContractDto, ContractTermDto, ContractedMaterialDto, (ContractDto contract, ContractTermDto?
                term, ContractedMaterialDto? material)>(
                contractsSql,
                (contract, term, material) => (contract, term, material),
                new { id = request.SupplierId },
                splitOn: "TermDescription,MaterialId"
            );

        // Build contracts
        var contracts = contractResults
            .GroupBy(r => r.contract.ContractId)
            .Select(contractGroup =>
            {
                var contractData = contractGroup.First().contract;

                var terms = contractGroup
                    .Where(r => r.term != null && !string.IsNullOrEmpty(r.term.TermDescription))
                    .GroupBy(r => new { r.term!.TermDescription, r.term.TermType, r.term.TermExpirationDate })
                    .Select(termGroup => new ContractTermResponse
                    {
                        Description = termGroup.Key.TermDescription!,
                        Type = termGroup.Key.TermType ?? string.Empty,
                        ExpirationDate = termGroup.Key.TermExpirationDate ?? DateTime.MinValue
                    })
                    .ToList();

                var materials = contractGroup
                    .Where(r => r.material?.MaterialId != null)
                    .GroupBy(r => r.material!.MaterialId)
                    .Select(materialGroup =>
                    {
                        var materialData = materialGroup.First().material!;
                        return new ContractedMaterialResponse
                        {
                            MaterialId = materialData.MaterialId!.Value,
                            MaterialName = materialData.MaterialName ?? string.Empty,
                            Price = materialData.Price ?? 0m,
                            Currency = materialData.Currency ?? string.Empty,
                            MinimumOrder = materialData.MinimumOrder ?? 0m,
                            MaximumOrder = materialData.MaximumOrder ?? 0m,
                            Unit = materialData.Unit ?? string.Empty,
                            LeadTime = materialData.LeadTime ?? 0
                        };
                    })
                    .ToList();

                return new ContractResponse
                {
                    Id = contractData.ContractId!.Value,
                    ContractNumber = contractData.ContractNumber ?? string.Empty,
                    StartDate = contractData.StartDate ?? DateTime.MinValue,
                    EndDate = contractData.EndDate ?? DateTime.MinValue,
                    Status = contractData.ContractStatus ?? string.Empty,
                    IsActive = contractData.IsActive ?? false,
                    Terms = terms,
                    Materials = materials
                };
            })
            .ToList();

        // Build final response
        var response = new SupplierResponse
        {
            Id = supplier.SupplierId,
            Code = supplier.Code,
            Name = supplier.Name,
            Status = supplier.Status,
            ContactInfo = new ContactInfoResponse
            {
                ContactPerson = supplier.ContactPerson ?? string.Empty,
                Email = supplier.Email ?? string.Empty,
                Phone = supplier.Phone ?? string.Empty,
                Address = supplier.Address ?? string.Empty
            },
            Contracts = contracts,
            ActiveContract = contracts.FirstOrDefault(c => c.IsActive)
        };

        return Result.Success(response);
    }


    // 1. Create DTOs for database mapping
    private class SupplierDto
    {
        public Guid SupplierId { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string? ContactPerson { get; set; }
        public string? Address { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
    }

    private class ContractDto
    {
        public Guid? ContractId { get; set; }
        public string? ContractNumber { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? ContractStatus { get; set; }
        public bool? IsActive { get; set; }
    }

    private class ContractTermDto
    {
        public string? TermDescription { get; set; }
        public string? TermType { get; set; }
        public DateTime? TermExpirationDate { get; set; }
    }

    private class ContractedMaterialDto
    {
        public Guid? MaterialId { get; set; }
        public string? MaterialName { get; set; }
        public decimal? Price { get; set; }
        public string? Currency { get; set; }
        public decimal? MinimumOrder { get; set; }
        public decimal? MaximumOrder { get; set; }
        public string? Unit { get; set; }
        public int? LeadTime { get; set; }
    }

// 2. Combined DTO for the query result
    private class SupplierQueryResult
    {
        public SupplierDto Supplier { get; set; } = new();
        public ContractDto? Contract { get; set; }
        public ContractTermDto? Term { get; set; }
        public ContractedMaterialDto? Material { get; set; }
    }
}