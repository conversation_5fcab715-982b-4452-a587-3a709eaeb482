using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Errors;

namespace Tebipaints.Application.Procurement.Suppliers.CreateSupplier;

public class CreateSupplierCommandHandler : ICommandHandler<CreateSupplierCommand, Guid>
{
    private readonly ISupplierRepository _supplierRepository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateSupplierCommandHandler(ISupplierRepository supplierRepository, IUnitOfWork unitOfWork)
    {
        _supplierRepository = supplierRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<Guid>> Handle(CreateSupplierCommand request, CancellationToken cancellationToken)
    {
        // Check if supplier code already exists
        if (await _supplierRepository.ExistsAsync(request.Code, cancellationToken))
        {
            return Result.Failure<Guid>(ProcurementErrors.SupplierCodeAlreadyExists(request.Code));
        }

        // Create contact info
        var contactInfoResult = ContactInfo.Create(
            request.ContactPerson,
            request.Email,
            request.Phone,
            request.Address);

        if (contactInfoResult.IsFailure)
        {
            return Result.Failure<Guid>(contactInfoResult.Error);
        }

        // Create supplier
        var supplierResult = Supplier.Create(
            request.Code,
            request.Name,
            contactInfoResult.Value);

        if (supplierResult.IsFailure)
        {
            return Result.Failure<Guid>(supplierResult.Error);
        }

        var supplier = supplierResult.Value;
        
        _supplierRepository.Add(supplier);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success(supplier.Id);
    }
}