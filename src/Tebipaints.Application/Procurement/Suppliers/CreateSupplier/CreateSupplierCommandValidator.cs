using FluentValidation;

namespace Tebipaints.Application.Procurement.Suppliers.CreateSupplier;

public class CreateSupplierCommandValidator : AbstractValidator<CreateSupplierCommand>
{
    public CreateSupplierCommandValidator()
    {
        RuleFor(supplier => supplier.Name).NotEmpty();
        RuleFor(supplier => supplier.Address).NotEmpty();
        RuleFor(supplier => supplier.Code).NotEmpty();
        RuleFor(supplier => supplier.ContactPerson).NotEmpty();
        RuleFor(supplier => supplier.Email).NotEmpty().EmailAddress();
        RuleFor(supplier => supplier.Phone).NotEmpty();
    }
}