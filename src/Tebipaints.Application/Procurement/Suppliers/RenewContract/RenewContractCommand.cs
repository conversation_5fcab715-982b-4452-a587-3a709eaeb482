using Tebipaints.Application.Abstractions.Messaging;

namespace Tebipaints.Application.Procurement.Suppliers.RenewContract;

public record RenewContractCommand(
    Guid ContractId,
    DateTime NewStartDate,
    DateTime NewEndDate,
    List<UpdateContractTermCommand>? UpdatedTerms = null,
    List<UpdateContractedMaterialCommand>? UpdatedMaterials = null) : ICommand<Guid>;
    
public sealed record UpdateContractTermCommand(
    string Description,
    string Type,
    List<string> Values,
    DateTime? ExpirationDate);

public sealed record UpdateContractedMaterialCommand(
    Guid MaterialId,
    decimal NewUnitPrice,
    decimal? NewMinimumOrder,
    decimal? NewMaximumOrder,
    int? NewLeadTimeDays);