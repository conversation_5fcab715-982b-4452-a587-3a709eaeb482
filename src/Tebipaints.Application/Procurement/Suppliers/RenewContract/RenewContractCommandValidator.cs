using FluentValidation;
using Tebipaints.Application.Procurement.Suppliers.TerminateContract;

namespace Tebipaints.Application.Procurement.Suppliers.RenewContract;

public class RenewContractCommandValidator : AbstractValidator<RenewContractCommand>
{
    public RenewContractCommandValidator()
    {
        RuleFor(c => c.ContractId).NotEmpty();
        RuleFor(c => c.NewStartDate).NotEmpty();
        RuleFor(c => c.NewEndDate).NotEmpty();
        RuleFor(c => c.UpdatedTerms).NotEmpty();
        RuleFor(c => c.UpdatedMaterials).NotEmpty();
    }
}