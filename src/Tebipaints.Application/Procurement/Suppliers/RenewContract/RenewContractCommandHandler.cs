using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Enums;
using Tebipaints.Domain.Procurement.Errors;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Procurement.Suppliers.RenewContract;

public class RenewContractCommandHandler : ICommandHandler<RenewContractCommand, Guid>
{
    private readonly ISupplierRepository _supplierRepository;
    private readonly IDocumentNumberGenerator _documentNumberGenerator;
    private readonly IUnitOfWork _unitOfWork;

    public RenewContractCommandHandler(ISupplierRepository supplierRepository, IUnitOfWork unitOfWork, IDocumentNumberGenerator documentNumberGenerator)
    {
        _supplierRepository = supplierRepository;
        _unitOfWork = unitOfWork;
        _documentNumberGenerator = documentNumberGenerator;
    }

    public async Task<Result<Guid>> Handle(RenewContractCommand request, CancellationToken cancellationToken)
    {
        // 1. Find supplier and contract
        var supplier = await _supplierRepository
            .GetByContractIdAsync(request.ContractId, cancellationToken);

        if (supplier is null)
        {
            return Result.Failure<Guid>(ProcurementErrors.ContractNotFound(request.ContractId));
        }

        var existingContract = supplier.Contracts
            .First(c => c.Id == request.ContractId);
        
        // 2. Generate new contract number
        
        // 3. Renew contract
        var contractNumber = await _documentNumberGenerator.GetNextContractNumberAsync(cancellationToken);
        var renewalResult = existingContract.Renew(
            contractNumber,
            request.NewStartDate,
            request.NewEndDate);

        if (renewalResult.IsFailure)
        {
            return Result.Failure<Guid>(renewalResult.Error);
        }

        var newContract = renewalResult.Value;
        
        // 4. Update terms if provided
        if (request.UpdatedTerms is not null)
        {
            foreach (var termUpdate in request.UpdatedTerms)
            {
                if (!Enum.TryParse<TermType>(termUpdate.Type, true, out var termType))
                {
                    return Result.Failure<Guid>(ProcurementErrors.InvalidTermType(termUpdate.Type));
                }
                
                // Convert string values to TermValue enum values
                var termValues = termUpdate.Values
                    .Select(v => Enum.TryParse<TermValue>(v, true, out var termValue) 
                        ? termValue 
                        : throw new ArgumentException($"Invalid term value: {v}"))
                    .ToList();

                var term = ContractTerm.Create(
                    termUpdate.Description,
                    termType,
                    termValues,
                    termUpdate.ExpirationDate);

                newContract.UpdateTerm(term);
            }
        }
        
        // 5. Update materials if provided
        if (request.UpdatedMaterials is not null)
        {
            foreach (var materialUpdate in request.UpdatedMaterials)
            {
                var existingMaterial = newContract.GetMaterial(materialUpdate.MaterialId);
                if (existingMaterial is null) continue;

                // Create new unit price if provided
                Money? newUnitPrice = materialUpdate.NewUnitPrice > 0 
                    ? Money.FromDecimal(materialUpdate.NewUnitPrice, Currency.Usd)
                    : null;

                // Create new minimum order if provided
                Measurement? newMinimumOrder = materialUpdate.NewMinimumOrder.HasValue
                    ? existingMaterial.MinimumOrder.WithValue(materialUpdate.NewMinimumOrder.Value)
                    : null;

                // Create new maximum order if provided
                Measurement? newMaximumOrder = materialUpdate.NewMaximumOrder.HasValue
                    ? existingMaterial.MaximumOrder?.WithValue(materialUpdate.NewMaximumOrder.Value)
                    : null;

                var updatedMaterialResult = existingMaterial.WithUpdates(
                    newUnitPrice,
                    newMinimumOrder,
                    newMaximumOrder,
                    materialUpdate.NewLeadTimeDays);

                if (updatedMaterialResult.IsFailure)
                {
                    return Result.Failure<Guid>(updatedMaterialResult.Error);
                }

                var result = newContract.UpdateMaterial(updatedMaterialResult.Value);
                if (result.IsFailure)
                {
                    return Result.Failure<Guid>(result.Error);
                }
            }
        }
        
        // 6. Add new contract to supplier
        var addResult = supplier.AddContract(newContract);
        if (addResult.IsFailure)
        {
            return Result.Failure<Guid>(addResult.Error);
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        return Result.Success(newContract.Id);
    }
}