using System.Data;
using System.Data;
using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Application.Procurement.PurchaseOrders.ListPurchaseOrders;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement.Errors;
using Tebipaints.Application.Procurement.PurchaseOrders.Shared;

namespace Tebipaints.Application.Procurement.PurchaseOrders.GetPurchaseOrder;

public class GetPurchaseOrderQueryHandler : IQueryHandler<GetPurchaseOrderQuery, PurchaseOrderResponse>
{
    private readonly ISqlConnectionFactory _sqlConnection;

    public GetPurchaseOrderQueryHandler(ISqlConnectionFactory sqlConnection)
    {
        _sqlConnection = sqlConnection;
    }

    public async Task<Result<PurchaseOrderResponse>> Handle(GetPurchaseOrderQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnection.CreateConnection();

        const string sql = """
            WITH order_totals AS (
            SELECT 
                purchase_order_id,
                CAST(COALESCE(SUM(CAST(quantity_value AS numeric(19,4)) * CAST(unit_price_amount AS numeric(19,4))), 0) AS numeric(19,4)) as total_amount,
                CAST(
                    CASE 
                        WHEN SUM(CAST(quantity_value AS numeric(19,4))) = 0 THEN 0
                        ELSE ROUND((SUM(CAST(received_quantity_value AS numeric(19,4))) * 100.0) / SUM(CAST(quantity_value AS numeric(19,4))), 2)
                    END 
                AS numeric(19,4)) as fulfillment_percentage
            FROM purchase_order_items
            GROUP BY purchase_order_id
        )
        SELECT 
            po.id as Id,
            po.order_number_value as PurchaseOrderNumber,
            po.supplier_id as SupplierId,
            po.order_date as OrderDate,
            po.requested_by as RequestBy,
            po.approved_date as ApprovedDate,
            po.approved_by as ApprovedBy,
            po.status as Status,
            po.discount_percentage as DiscountPercentage,
            po.freight_charge_amount as FreightChargeAmount,
            COALESCE(po.freight_charge_currency, '') as FreightChargeCurrency,
            po.promise_date as PromiseDate,
            COALESCE(po.order_currency, '') as OrderCurrency,
            po.tax_rate as TaxRate,
            s.name as SupplierName,
            CAST(COALESCE(ot.total_amount, 0) AS numeric(19,4)) as TotalAmount,
            CAST(COALESCE(ot.fulfillment_percentage, 0) AS numeric(19,4)) as FulfillmentPercentage,
            poi.material_id as MaterialId,
            poi.material_name as MaterialName,
            CAST(COALESCE(poi.quantity_value, 0) AS numeric(19,4)) as QuantityValue,
            COALESCE(poi.quantity_unit, '') as QuantityUnit,
            CAST(COALESCE(poi.unit_price_amount, 0) AS numeric(19,4)) as UnitPriceAmount,
            COALESCE(poi.unit_price_currency, '') as UnitPriceCurrency,
            COALESCE(poi.notes, '') as Notes,
            CAST(COALESCE(poi.received_quantity_value, 0) AS numeric(19,4)) as ReceivedQuantityValue,
            COALESCE(poi.received_quantity_unit, '') as ReceivedQuantityUnit,
            CASE 
                WHEN COALESCE(poi.quantity_value, 0) = 0 THEN 0
                ELSE ROUND((COALESCE(poi.received_quantity_value, 0) * 100.0) / poi.quantity_value, 2)
            END as ItemFulfillmentPercentage
        FROM purchase_orders po
        LEFT JOIN order_totals ot ON ot.purchase_order_id = po.id
        LEFT JOIN purchase_order_items poi ON poi.purchase_order_id = po.id
        LEFT JOIN suppliers s ON s.id = po.supplier_id
        WHERE po.id = @PurchaseOrderId;
        """;

        var purchaseOrderDictionary = new Dictionary<Guid, PurchaseOrderResponse>();

        await connection.QueryAsync<PurchaseOrderResponse, PurchaseOrderItemResponse, PurchaseOrderResponse>(
            sql,
            (order, item) =>
            {

                if (!purchaseOrderDictionary.TryGetValue(order.Id, out var purchaseOrder))
                {
                    purchaseOrder = order;
                    purchaseOrder.Items = new List<PurchaseOrderItemResponse>();
                    purchaseOrderDictionary.Add(order.Id, purchaseOrder);
                }

                if (item?.MaterialId != null && item.MaterialId != Guid.Empty)
                {
                    purchaseOrder.Items.Add(item);
                }

                return purchaseOrder;
            },
            new { PurchaseOrderId = request.PurchaseOrderId },
            splitOn: "MaterialId");

        var purchaseOrder = purchaseOrderDictionary.Values.FirstOrDefault();
        if (purchaseOrder == null)
        {
            return Result.Failure<PurchaseOrderResponse>(ProcurementErrors.PurchaseOrderNotFound());
        }

        // Load related goods receipts and purchase invoices
        await LoadGoodsReceipts(connection, purchaseOrder, request.PurchaseOrderId);
        await LoadPurchaseInvoices(connection, purchaseOrder, request.PurchaseOrderId);
        LoadSummaryStatistics(purchaseOrder);

        // Load recent activities (limit to 10 most recent)
        purchaseOrder.RecentActivities = await PurchaseOrderActivityLoader.LoadActivitiesAsync(
            connection, request.PurchaseOrderId, limit: 10);

        return Result.Success(purchaseOrder);
    }

    private async Task LoadGoodsReceipts(IDbConnection connection, PurchaseOrderResponse purchaseOrder, Guid purchaseOrderId)
    {
        const string goodsReceiptsSql = """
            SELECT
                gr.id as Id,
                gr.receipt_number as ReceiptNumber,
                gr.received_date as ReceivedDate,
                gr.received_by as ReceivedBy,
                gr.status as Status,
                gr.delivery_note as DeliveryNote,
                COUNT(gri.material_id) as ItemCount,
                COALESCE(SUM(gri.unit_price_amount * gri.received_quantity_value), 0) as TotalValue,
                COALESCE(STRING_AGG(DISTINCT gri.unit_price_currency, ','), 'GHS') as Currency
            FROM goods_receipts gr
            LEFT JOIN goods_receipt_items gri ON gr.id = gri.goods_receipt_id
            WHERE gr.purchase_order_id = @PurchaseOrderId
            GROUP BY gr.id, gr.receipt_number, gr.received_date, gr.received_by, gr.status, gr.delivery_note
            ORDER BY gr.received_date DESC
            """;

        var goodsReceipts = await connection.QueryAsync<GoodsReceiptSummaryResponse>(
            goodsReceiptsSql,
            new { PurchaseOrderId = purchaseOrderId });

        purchaseOrder.GoodsReceipts = goodsReceipts.ToList();
    }

    private async Task LoadPurchaseInvoices(IDbConnection connection, PurchaseOrderResponse purchaseOrder, Guid purchaseOrderId)
    {
        const string purchaseInvoicesSql = """
            SELECT
                pi.id as Id,
                pi.supplier_invoice_number as SupplierInvoiceNumber,
                pi.invoice_date as InvoiceDate,
                pi.due_date as DueDate,
                pi.status as Status,
                pi.total_amount as TotalAmount,
                pi.total_currency as Currency,
                pi.last_matching_result as LastMatchingResult,
                pi.last_matching_variance_percentage as LastMatchingVariancePercentage,
                ABS(pi.total_amount - COALESCE(calc_total.calculated_total, 0)) > 0.01 as HasMatchingDiscrepancies,
                COALESCE(calc_total.item_count, 0) as ItemCount
            FROM purchase_invoices pi
            LEFT JOIN (
                SELECT
                    purchase_invoice_id,
                    COUNT(*) as item_count,
                    SUM(unit_price_amount * invoiced_quantity_value) as calculated_total
                FROM purchase_invoice_items
                GROUP BY purchase_invoice_id
            ) calc_total ON pi.id = calc_total.purchase_invoice_id
            WHERE pi.purchase_order_id = @PurchaseOrderId
            ORDER BY pi.invoice_date DESC
            """;

        var purchaseInvoices = await connection.QueryAsync<PurchaseInvoiceSummaryResponse>(
            purchaseInvoicesSql,
            new { PurchaseOrderId = purchaseOrderId });

        purchaseOrder.PurchaseInvoices = purchaseInvoices.ToList();
    }

    private static void LoadSummaryStatistics(PurchaseOrderResponse purchaseOrder)
    {
        var summary = new PurchaseOrderSummaryResponse
        {
            TotalGoodsReceipts = purchaseOrder.GoodsReceipts.Count,
            ConfirmedGoodsReceipts = purchaseOrder.GoodsReceipts.Count(gr => gr.Status == "Confirmed"),
            TotalPurchaseInvoices = purchaseOrder.PurchaseInvoices.Count,
            ApprovedPurchaseInvoices = purchaseOrder.PurchaseInvoices.Count(pi => pi.Status == "Approved"),
            InvoicesRequiringReview = purchaseOrder.PurchaseInvoices.Count(pi => pi.Status == "RequiresReview"),
            TotalInvoicedAmount = purchaseOrder.PurchaseInvoices.Sum(pi => pi.TotalAmount),
            TotalReceivedValue = purchaseOrder.GoodsReceipts.Sum(gr => gr.TotalValue),
            IsFullyReceived = purchaseOrder.FulfillmentPercentage >= 95.0m,
            IsFullyInvoiced = purchaseOrder.PurchaseInvoices.Any(),
            HasPendingInvoices = purchaseOrder.PurchaseInvoices.Any(pi =>
                pi.Status is "Draft" or "PendingApproval" or "RequiresReview")
        };

        purchaseOrder.Summary = summary;
    }
}