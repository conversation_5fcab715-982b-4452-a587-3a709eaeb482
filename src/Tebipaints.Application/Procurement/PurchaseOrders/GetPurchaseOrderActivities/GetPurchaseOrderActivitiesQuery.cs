using Tebipaints.Application.Abstractions.Messaging;

namespace Tebipaints.Application.Procurement.PurchaseOrders.GetPurchaseOrderActivities;

public class GetPurchaseOrderActivitiesQuery : IQuery<PurchaseOrderActivitiesResponse>
{
    public Guid PurchaseOrderId { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public List<string>? ActivityTypes { get; set; }
    public bool IncludeSystemActivities { get; set; } = true;
    public int? Limit { get; set; }
}
