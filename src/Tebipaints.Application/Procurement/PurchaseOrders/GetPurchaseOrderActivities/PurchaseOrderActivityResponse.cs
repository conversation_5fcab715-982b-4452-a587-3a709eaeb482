namespace Tebipaints.Application.Procurement.PurchaseOrders.GetPurchaseOrderActivities;

public class PurchaseOrderActivitiesResponse
{
    public Guid PurchaseOrderId { get; set; }
    public string PurchaseOrderNumber { get; set; } = string.Empty;
    public List<ActivityTimelineItem> Activities { get; set; } = new();
    public ActivitySummary Summary { get; set; } = new();
}

public class ActivityTimelineItem
{
    public Guid Id { get; set; }
    public DateTime Timestamp { get; set; }
    public string ActivityType { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? PerformedBy { get; set; }
    public string Status { get; set; } = string.Empty; // Success, Warning, Error, Info
    public Dictionary<string, object> Metadata { get; set; } = new();
    public List<ActivityDetail> Details { get; set; } = new();
}

public class ActivityDetail
{
    public string Label { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public string? Unit { get; set; }
    public string? PreviousValue { get; set; }
    public bool IsChanged { get; set; }
}

public class ActivitySummary
{
    public int TotalActivities { get; set; }
    public DateTime FirstActivity { get; set; }
    public DateTime LastActivity { get; set; }
    public int DaysInProgress { get; set; }
    public Dictionary<string, int> ActivityCounts { get; set; } = new();
    public List<string> KeyMilestones { get; set; } = new();
    public string CurrentPhase { get; set; } = string.Empty;
    public decimal CompletionPercentage { get; set; }
}

// Activity Types Constants
public static class ActivityTypes
{
    public const string ORDER_CREATED = "ORDER_CREATED";
    public const string ORDER_APPROVED = "ORDER_APPROVED";
    public const string ORDER_CANCELLED = "ORDER_CANCELLED";
    public const string ORDER_CLOSED = "ORDER_CLOSED";
    public const string ITEM_ADDED = "ITEM_ADDED";
    public const string ITEM_REMOVED = "ITEM_REMOVED";
    public const string GOODS_RECEIPT_CREATED = "GOODS_RECEIPT_CREATED";
    public const string GOODS_RECEIPT_CONFIRMED = "GOODS_RECEIPT_CONFIRMED";
    public const string GOODS_RECEIPT_CANCELLED = "GOODS_RECEIPT_CANCELLED";
    public const string MATERIAL_RECEIVED = "MATERIAL_RECEIVED";
    public const string INVOICE_CREATED = "INVOICE_CREATED";
    public const string INVOICE_MATCHED = "INVOICE_MATCHED";
    public const string INVOICE_APPROVED = "INVOICE_APPROVED";
    public const string INVOICE_REJECTED = "INVOICE_REJECTED";
    public const string INVOICE_REQUIRES_REVIEW = "INVOICE_REQUIRES_REVIEW";
    public const string SUPPLIER_COMMUNICATION = "SUPPLIER_COMMUNICATION";
    public const string SYSTEM_NOTIFICATION = "SYSTEM_NOTIFICATION";
    public const string STATUS_CHANGE = "STATUS_CHANGE";
    public const string QUALITY_INSPECTION = "QUALITY_INSPECTION";
    public const string PAYMENT_PROCESSED = "PAYMENT_PROCESSED";
}

// Activity Status Constants
public static class ActivityStatus
{
    public const string SUCCESS = "success";
    public const string WARNING = "warning";
    public const string ERROR = "error";
    public const string INFO = "info";
    public const string PENDING = "pending";
}


