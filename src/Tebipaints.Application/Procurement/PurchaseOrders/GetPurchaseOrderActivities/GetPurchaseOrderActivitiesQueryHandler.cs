using System.Data;
using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement.Errors;
using Tebipaints.Application.Procurement.PurchaseOrders.Shared;

namespace Tebipaints.Application.Procurement.PurchaseOrders.GetPurchaseOrderActivities;

public class GetPurchaseOrderActivitiesQueryHandler : IQueryHandler<GetPurchaseOrderActivitiesQuery, PurchaseOrderActivitiesResponse>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;

    public GetPurchaseOrderActivitiesQueryHandler(ISqlConnectionFactory sqlConnectionFactory)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
    }

    public async Task<Result<PurchaseOrderActivitiesResponse>> Handle(GetPurchaseOrderActivitiesQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();

        // First verify the purchase order exists
        var purchaseOrderExists = await connection.QueryFirstOrDefaultAsync<bool>(
            "SELECT CAST(CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END AS BIT) FROM purchase_orders WHERE id = @PurchaseOrderId",
            new { request.PurchaseOrderId });

        if (!purchaseOrderExists)
        {
            return Result.Failure<PurchaseOrderActivitiesResponse>(
                ProcurementErrors.PurchaseOrderNotFound(request.PurchaseOrderId));
        }

        var response = new PurchaseOrderActivitiesResponse
        {
            PurchaseOrderId = request.PurchaseOrderId
        };

        // Load activities using shared helper
        var activities = await PurchaseOrderActivityLoader.LoadActivitiesAsync(
            connection, request.PurchaseOrderId, request.Limit);

        // Add system activities if requested
        if (request.IncludeSystemActivities)
        {
            var systemActivities = await LoadSystemActivities(connection, request);
            activities.AddRange(systemActivities);

            // Re-sort and apply limit after adding system activities
            activities = activities.OrderByDescending(a => a.Timestamp).ToList();
            if (request.Limit.HasValue)
            {
                activities = activities.Take(request.Limit.Value).ToList();
            }
        }

        // Apply date filtering if specified
        if (request.FromDate.HasValue)
        {
            activities = activities.Where(a => a.Timestamp >= request.FromDate.Value).ToList();
        }
        if (request.ToDate.HasValue)
        {
            activities = activities.Where(a => a.Timestamp <= request.ToDate.Value).ToList();
        }

        // Apply activity type filtering if specified
        if (request.ActivityTypes?.Any() == true)
        {
            activities = activities.Where(a => request.ActivityTypes.Contains(a.ActivityType)).ToList();
        }

        response.Activities = activities;
        response.Summary = GenerateActivitySummary(activities, request.PurchaseOrderId);

        // Get PO number for response
        var poNumber = await connection.QueryFirstOrDefaultAsync<string>(
            "SELECT order_number_value FROM purchase_orders WHERE id = @PurchaseOrderId",
            new { request.PurchaseOrderId });
        response.PurchaseOrderNumber = poNumber ?? "";

        return Result.Success(response);
    }



    private async Task<List<ActivityTimelineItem>> LoadSystemActivities(IDbConnection connection, GetPurchaseOrderActivitiesQuery request)
    {
        // This would load from an event store if you have one
        // For now, we'll create some derived system activities
        const string sql = """
            SELECT 
                NEWID() as Id,
                GETDATE() as Timestamp,
                'SYSTEM_NOTIFICATION' as ActivityType,
                'System Activity' as Title,
                'System activities would be loaded from event store' as Description,
                'System' as PerformedBy,
                'info' as Status
            WHERE 1 = 0  -- Placeholder - no actual system activities yet
            """;

        var activities = await connection.QueryAsync<ActivityTimelineItem>(sql, new { request.PurchaseOrderId });
        return activities.ToList();
    }

    private static ActivitySummary GenerateActivitySummary(List<ActivityTimelineItem> activities, Guid purchaseOrderId)
    {
        if (!activities.Any())
        {
            return new ActivitySummary();
        }

        var firstActivity = activities.MinBy(a => a.Timestamp)?.Timestamp ?? DateTime.Now;
        var lastActivity = activities.MaxBy(a => a.Timestamp)?.Timestamp ?? DateTime.Now;
        var daysInProgress = (int)(DateTime.Now - firstActivity).TotalDays;

        var activityCounts = activities
            .GroupBy(a => a.ActivityType)
            .ToDictionary(g => g.Key, g => g.Count());

        var keyMilestones = new List<string>();
        if (activityCounts.ContainsKey(ActivityTypes.ORDER_CREATED)) keyMilestones.Add("Order Created");
        if (activityCounts.ContainsKey(ActivityTypes.ORDER_APPROVED)) keyMilestones.Add("Order Approved");
        if (activityCounts.ContainsKey(ActivityTypes.GOODS_RECEIPT_CONFIRMED)) keyMilestones.Add("Materials Received");
        if (activityCounts.ContainsKey(ActivityTypes.INVOICE_APPROVED)) keyMilestones.Add("Invoice Approved");

        // Determine current phase and completion percentage
        var currentPhase = "Draft";
        var completionPercentage = 0m;

        if (activityCounts.ContainsKey(ActivityTypes.INVOICE_APPROVED))
        {
            currentPhase = "Completed";
            completionPercentage = 100m;
        }
        else if (activityCounts.ContainsKey(ActivityTypes.INVOICE_CREATED))
        {
            currentPhase = "Invoicing";
            completionPercentage = 80m;
        }
        else if (activityCounts.ContainsKey(ActivityTypes.GOODS_RECEIPT_CONFIRMED))
        {
            currentPhase = "Delivered";
            completionPercentage = 60m;
        }
        else if (activityCounts.ContainsKey(ActivityTypes.ORDER_APPROVED))
        {
            currentPhase = "In Progress";
            completionPercentage = 40m;
        }
        else if (activityCounts.ContainsKey(ActivityTypes.ORDER_CREATED))
        {
            currentPhase = "Created";
            completionPercentage = 20m;
        }

        return new ActivitySummary
        {
            TotalActivities = activities.Count,
            FirstActivity = firstActivity,
            LastActivity = lastActivity,
            DaysInProgress = daysInProgress,
            ActivityCounts = activityCounts,
            KeyMilestones = keyMilestones,
            CurrentPhase = currentPhase,
            CompletionPercentage = completionPercentage
        };
    }
}
