using FluentValidation;

namespace Tebipaints.Application.Procurement.PurchaseOrders.AddPurchaseOrderItem;

public class AddPurchaseOrderItemCommandValidator : AbstractValidator<AddPurchaseOrderItemCommand>
{
    public AddPurchaseOrderItemCommandValidator()
    {
        RuleFor(po => po.PurchaseOrderId).NotEmpty();
        RuleFor(po => po.MaterialId).NotEmpty();
        RuleFor(po => po.MaterialName).NotEmpty();
        RuleFor(po => po.Quantity).GreaterThan(0);
        RuleFor(po => po.Unit).NotEmpty();
        RuleFor(po => po.UnitPrice).GreaterThan(0);
    }
}