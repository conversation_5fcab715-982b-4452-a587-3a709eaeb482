using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Errors;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Procurement.PurchaseOrders.AddPurchaseOrderItem;

public class AddPurchaseOrderItemCommandHandler : ICommandHandler<AddPurchaseOrderItemCommand, Guid>
{
    private readonly IPurchaseOrderRepository _purchaseOrderRepository;
    private readonly IUnitOfWork _unitOfWork;

    public AddPurchaseOrderItemCommandHandler(IPurchaseOrderRepository purchaseOrderRepository, IUnitOfWork unitOfWork)
    {
        _purchaseOrderRepository = purchaseOrderRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<Guid>> Handle(AddPurchaseOrderItemCommand request, CancellationToken cancellationToken)
    {
        // 1. verify existence of purchase order
        var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(request.PurchaseOrderId, cancellationToken);
        if (purchaseOrder == null)
        {
            return Result.Failure<Guid>(ProcurementErrors.PurchaseOrderNotFound());
        }
        
        // 2. Create order item
        var unitOfMeasure = UnitOfMeasureConverter.FromString(request.Unit);
        if (unitOfMeasure.IsFailure)
        {
            return Result.Failure<Guid>(unitOfMeasure.Error);
        }
        
        var measurement = new Measurement(request.Quantity, unitOfMeasure.Value);
        var item = PurchaseOrderItem.Create(
            request.MaterialId,
            request.MaterialName,
            measurement,
            new Money(request.UnitPrice, Currency.Usd));

        // 3. Add order items
        purchaseOrder.AddItem(item.Value);
        _purchaseOrderRepository.Update(purchaseOrder);

        // 4. Update storage
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success(purchaseOrder.Id);
    }
}