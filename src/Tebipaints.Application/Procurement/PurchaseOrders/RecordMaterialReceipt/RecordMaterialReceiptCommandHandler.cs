using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Errors;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Procurement.PurchaseOrders.RecordMaterialReceipt;

public class RecordMaterialReceiptCommandHandler : ICommandHandler<RecordMaterialReceiptCommand, Guid>
{
    private readonly IPurchaseOrderRepository _repository;
    private readonly IUnitOfWork _unitOfWork;

    public RecordMaterialReceiptCommandHandler(IPurchaseOrderRepository repository, IUnitOfWork unitOfWork)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<Guid>> Handle(RecordMaterialReceiptCommand request, CancellationToken cancellationToken)
    {
        //. Fetch Purchase Order
        var purchaseOrder = await _repository.GetByIdAsync(request.PurchaseOrderId, cancellationToken);
        if (purchaseOrder == null)
        {
            return Result.Failure<Guid>(ProcurementErrors.PurchaseOrderNotFound());
        }
        
        // update received quantity
        var unitOfMeasure = UnitOfMeasureConverter.FromString(request.Unit);
        if (unitOfMeasure.IsFailure)
        {
            return Result.Failure<Guid>(unitOfMeasure.Error);
        }
        
        var measurement = new Measurement(request.ReceivedQuantity, unitOfMeasure.Value);
        purchaseOrder.UpdateReceivedQuantity(request.MaterialId, measurement);
        
        _repository.Update(purchaseOrder);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        return Result.Success(purchaseOrder.Id);
    }
}