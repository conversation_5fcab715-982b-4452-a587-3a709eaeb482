using MediatR;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Inventory;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Events;

namespace Tebipaints.Application.Procurement.PurchaseOrders.RecordMaterialReceipt;

public class PurchaseOrderItemReceivedDomainEventHandler : INotificationHandler<PurchaseOrderItemReceivedDomainEvent>
{
    private readonly IMaterialInventoryRepository _inventoryRepository;
    private readonly IPurchaseOrderRepository _purchaseOrderRepository;
    private readonly IUnitOfWork _unitOfWork;

    public PurchaseOrderItemReceivedDomainEventHandler(IMaterialInventoryRepository inventoryRepository,
        IPurchaseOrderRepository purchaseOrderRepository,
        IUnitOfWork unitOfWork)
    {
        _inventoryRepository = inventoryRepository;
        _purchaseOrderRepository = purchaseOrderRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task Handle(PurchaseOrderItemReceivedDomainEvent notification, CancellationToken cancellationToken)
    {
        // 1. Get the purchase order to access full item details
        var purchaseOrder = await _purchaseOrderRepository.GetByIdAsync(notification.PurchaseOrderId, cancellationToken);
        
        if (purchaseOrder is null)
        {
            return;
        }
        
        // 2. Find or create material inventory
        var inventory = await _inventoryRepository
            .GetByIdAsync(notification.ItemId, cancellationToken);

        if (inventory is null)
        {
            // Create new inventory with default settings
            // Note: You might want to get these values from configuration or material settings
            var createResult = MaterialInventory.Create(
                notification.ItemId,
                InventoryLocation.Factory, // Default location
                minimumStockLevel: 0,
                reorderPoint: 0);

            if (createResult.IsFailure)
            {
                // Log error
                throw new ApplicationException($"{createResult.Error.Code} : {createResult.Error.Name}");
            }

            inventory = createResult.Value;
            _inventoryRepository.Add(inventory, cancellationToken);
        }

        // 3. Add stock to inventory with PO reference
        var reference = $"PO Receipt: {notification.OrderNumber}";
        var result = inventory.AddStock(
            (double)notification.ReceivedQuantity.Value,
            reference);

        if (result.IsFailure)
        {
            // Log error
            throw new ApplicationException($"{result.Error.Code} : {result.Error.Name}");
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);
    }
}