using FluentValidation;

namespace Tebipaints.Application.Procurement.PurchaseOrders.RecordMaterialReceipt;

public class RecordMaterialReceiptCommandValidator : AbstractValidator<RecordMaterialReceiptCommand>
{
    public RecordMaterialReceiptCommandValidator()
    {
        RuleFor(po => po.PurchaseOrderId).NotEmpty();
        RuleFor(po => po.MaterialId).NotEmpty();
        RuleFor(po => po.ReceivedQuantity).GreaterThan(0);
        RuleFor(po => po.Unit).NotEmpty();
    }
}