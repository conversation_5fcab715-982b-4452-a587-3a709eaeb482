using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Errors;

namespace Tebipaints.Application.Procurement.PurchaseOrders.ClosePurchaseOrder;

public class ClosePurchaseOrderCommandHandler : ICommandHandler<ClosePurchaseOrderCommand, Guid>
{
    private readonly IPurchaseOrderRepository _repository;
    private readonly IUnitOfWork _unitOfWork;

    public ClosePurchaseOrderCommandHandler(IPurchaseOrderRepository repository, IUnitOfWork unitOfWork)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<Guid>> Handle(ClosePurchaseOrderCommand request, CancellationToken cancellationToken)
    {
        // 1. verify existence of purchase order
        var order = await _repository.GetByIdAsync(request.PurchaseOrderId, cancellationToken);
        if (order == null)
        {
            return Result.Failure<Guid>(ProcurementErrors.PurchaseOrderNotFound());
        }

        var closeResult = order.Close();
        if (closeResult.IsFailure)
        {
            return Result.Failure<Guid>(closeResult.Error);
        }
        
        _repository.Update(order);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        return Result.Success(order.Id);
    }
}