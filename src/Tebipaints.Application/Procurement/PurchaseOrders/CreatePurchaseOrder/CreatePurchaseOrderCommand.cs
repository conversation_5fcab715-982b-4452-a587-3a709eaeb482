using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Procurement;

namespace Tebipaints.Application.Procurement.PurchaseOrders.CreatePurchaseOrder;

public record CreatePurchaseOrderCommand(
    Guid SupplierId,
    string RequestBy,
    DateTime PromiseDate,
    decimal DiscountPercent,
    decimal FreightChargeAmount,
    decimal TaxRate,
    string PurchaseOrderCurrency,
    List<CreatePurchaseOrderItemCommand> Items) : ICommand<PurchaseOrder>;
    
public record CreatePurchaseOrderItemCommand(
    Guid MaterialId, 
    string MaterialName,
    decimal Quantity,
    string Unit,
    decimal UnitPrice,
    string? Notes);