using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Procurement.PurchaseOrders.CreatePurchaseOrder;

public class CreatePurchaseOrderCommandHandler : ICommandHandler<CreatePurchaseOrderCommand, PurchaseOrder>
{
    private readonly IDocumentNumberGenerator _documentNumberGenerator;
    private readonly IPurchaseOrderRepository _purchaseOrderRepository;
    private readonly IUnitOfWork _unitOfWork;

    public CreatePurchaseOrderCommandHandler(
        IPurchaseOrderRepository purchaseOrderRepository,
        IUnitOfWork unitOfWork, IDocumentNumberGenerator documentNumberGenerator)
    {
        _purchaseOrderRepository = purchaseOrderRepository;
        _unitOfWork = unitOfWork;
        _documentNumberGenerator = documentNumberGenerator;
    }

    public async Task<Result<PurchaseOrder>> Handle(CreatePurchaseOrderCommand request, CancellationToken cancellationToken)
    {
        // 1 fetch next sequence for order number
        var purchaseOrderNumber = await _documentNumberGenerator.GetNextPurchaseOrderNumberAsync(cancellationToken);
        
        // 2 create order items
        var orderItems = new List<PurchaseOrderItem>();

        foreach (var item in request.Items)
        {
            // Get the appropriate UnitOfMeasure based on the unit string
            var unitOfMeasure = UnitOfMeasureConverter.FromString(item.Unit);
            if (unitOfMeasure.IsFailure)
            {
                return Result.Failure<PurchaseOrder>(unitOfMeasure.Error);
            }
            
            var measurement = new Measurement(item.Quantity, unitOfMeasure.Value);

            var orderItemResult = PurchaseOrderItem.Create(
                item.MaterialId,
                item.MaterialName,
                measurement,
                Money.FromDecimal(item.UnitPrice, Currency.FromCode(request.PurchaseOrderCurrency)));

            if (orderItemResult.IsFailure)
            {
                return Result.Failure<PurchaseOrder>(orderItemResult.Error);
            }
            
            orderItems.Add(orderItemResult.Value);
        }
        
        var poResult = PurchaseOrder.Create(
            purchaseOrderNumber,
            request.SupplierId,
            request.RequestBy,
            request.PromiseDate,
            request.DiscountPercent,
            new Money(request.FreightChargeAmount, Currency.FromCode(request.PurchaseOrderCurrency)),
            Currency.FromCode(request.PurchaseOrderCurrency),
            request.TaxRate,
            orderItems);

        if (poResult.IsFailure)
        {
            return Result.Failure<PurchaseOrder>(poResult.Error);
        }
        
        _purchaseOrderRepository.Add(poResult.Value);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        return Result.Success(poResult.Value);
    }
}