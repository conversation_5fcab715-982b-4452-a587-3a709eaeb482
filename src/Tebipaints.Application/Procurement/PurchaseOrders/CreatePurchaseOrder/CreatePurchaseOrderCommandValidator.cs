using FluentValidation;

namespace Tebipaints.Application.Procurement.PurchaseOrders.CreatePurchaseOrder;

public class CreatePurchaseOrderCommandValidator : AbstractValidator<CreatePurchaseOrderCommand>
{
    public CreatePurchaseOrderCommandValidator()
    {
        RuleFor(c => c.SupplierId).NotEmpty();
        RuleFor(c => c.RequestBy).NotEmpty();
        RuleFor(c => c.DiscountPercent).NotEmpty();
        RuleFor(c => c.Items).NotEmpty();
    }
}