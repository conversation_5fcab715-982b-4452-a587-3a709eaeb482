using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Application.Procurement.PurchaseOrders.Shared;

namespace Tebipaints.Application.Procurement.PurchaseOrders.ListPurchaseOrders;

public class ListPurchaseOrdersQueryHandler : IQueryHandler<ListPurchaseOrdersQuery, IReadOnlyList<PurchaseOrderResponse>>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;

    public ListPurchaseOrdersQueryHandler(ISqlConnectionFactory sqlConnectionFactory)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
    }

    public async Task<Result<IReadOnlyList<PurchaseOrderResponse>>> Handle(ListPurchaseOrdersQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();
        
        

        const string sql = """
                            WITH order_totals AS (
                               SELECT
                                   purchase_order_id,
                                   CAST(COALESCE(SUM(CAST(quantity_value AS numeric(19,4)) * CAST(unit_price_amount AS numeric(19,4))), 0) AS numeric(19,4)) as total_amount,
                                   CAST(
                                       CASE
                                           WHEN SUM(CAST(quantity_value AS numeric(19,4))) = 0 THEN 0
                                           ELSE ROUND((SUM(CAST(received_quantity_value AS numeric(19,4))) * 100.0) / SUM(CAST(quantity_value AS numeric(19,4))), 2)
                                       END
                                   AS numeric(19,4)) as fulfillment_percentage
                               FROM purchase_order_items
                               GROUP BY purchase_order_id
                           )
                           SELECT
                               po.id,
                               po.order_number_value as PurchaseOrderNumber,
                               po.supplier_id as SupplierId,
                               po.order_date as OrderDate,
                               po.requested_by as RequestBy,
                               po.approved_date as ApprovedDate,
                               po.approved_by as ApprovedBy,
                               po.status as Status,
                               po.discount_percentage as DiscountPercentage,
                               po.freight_charge_amount as FreightChargeAmount,
                               COALESCE(po.freight_charge_currency, '') as FreightChargeCurrency,
                               po.promise_date as PromiseDate,
                               COALESCE(po.order_currency, '') as OrderCurrency,
                               po.tax_rate as TaxRate,
                               s.name as SupplierName,
                               CAST(COALESCE(ot.total_amount, 0) AS numeric(19,4)) as "TotalAmount",
                               CAST(COALESCE(ot.fulfillment_percentage, 0) AS numeric(19,4)) as "FulfillmentPercentage",
                               poi.material_id as MaterialId,
                               poi.material_name as MaterialName,
                               CAST(COALESCE(poi.quantity_value, 0) AS numeric(19,4)) as "QuantityValue",
                               COALESCE(poi.quantity_unit, '') as QuantityUnit,
                               CAST(COALESCE(poi.unit_price_amount, 0) AS numeric(19,4)) as "UnitPriceAmount",
                               COALESCE(poi.unit_price_currency, '') as UnitPriceCurrency,
                               COALESCE(poi.notes, '') as Notes,
                               CAST(COALESCE(poi.received_quantity_value, 0) AS numeric(19,4)) as "ReceivedQuantityValue",
                               COALESCE(poi.received_quantity_unit, '') as ReceivedQuantityUnit,
                               CASE 
                                   WHEN poi.quantity_value = 0 THEN 0
                                   ELSE ROUND((poi.received_quantity_value * 100.0) / poi.quantity_value, 2)
                               END as ItemFulfillmentPercentage
                           FROM purchase_orders po
                           LEFT JOIN order_totals ot ON ot.purchase_order_id = po.id
                           LEFT JOIN purchase_order_items poi ON po.id = poi.purchase_order_id
                           LEFT JOIN suppliers s on po.supplier_id = s.id
                           ORDER BY po.order_date DESC;
                           """;
        
        var purchaseOrderDictionary = new Dictionary<Guid, PurchaseOrderResponse>();
    
        await connection.QueryAsync<PurchaseOrderResponse, PurchaseOrderItemResponse, PurchaseOrderResponse>(
            sql,
            (order, item) =>
            {
                
                if (!purchaseOrderDictionary.TryGetValue(order.Id, out var purchaseOrder))
                {
                    purchaseOrder = order;
                    purchaseOrder.Items = new List<PurchaseOrderItemResponse>();
                    purchaseOrderDictionary.Add(order.Id, purchaseOrder);
                }

                if (item?.MaterialId != null)
                {
                    purchaseOrder.Items.Add(item);
                }

                return purchaseOrder;
            },
            splitOn: "MaterialId");

        var purchaseOrders = purchaseOrderDictionary.Values.ToList();

        // Load recent activities for each purchase order (limit to 3 most recent for list view)
        foreach (var purchaseOrder in purchaseOrders)
        {
            purchaseOrder.RecentActivities = await PurchaseOrderActivityLoader.LoadActivitiesAsync(
                connection, purchaseOrder.Id, limit: 3);
        }

        return Result.Success<IReadOnlyList<PurchaseOrderResponse>>(purchaseOrders);
    }
}