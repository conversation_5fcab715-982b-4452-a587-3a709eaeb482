using Tebipaints.Application.Procurement.PurchaseOrders.GetPurchaseOrderActivities;

namespace Tebipaints.Application.Procurement.PurchaseOrders.ListPurchaseOrders;

public class PurchaseOrderResponse
{
    public Guid Id { get; init; }
    public string PurchaseOrderNumber { get; init; }
    public Guid SupplierId { get; init; }
    public string SupplierName { get; init; }
    public DateTime OrderDate { get; init; }
    public string RequestBy { get; init; }
    public DateTime? ApprovedDate { get; init; }
    public DateTime? PromiseDate { get; init; }
    public string? ApprovedBy { get; init; }
    public string Status { get; init; }
    public decimal TotalAmount { get; init; }
    public decimal DiscountPercentage { get; init; }
    public decimal FreightChargeAmount { get; init; }
    public string FreightChargeCurrency { get; init; }
    public string OrderCurrency { get; init; }
    public decimal TaxRate { get; init; }
    public decimal FulfillmentPercentage { get; init; }
    public List<PurchaseOrderItemResponse> Items { get; set; } = new();

    // Related Documents (populated in GetPurchaseOrder only)
    public List<GoodsReceiptSummaryResponse> GoodsReceipts { get; set; } = new();
    public List<PurchaseInvoiceSummaryResponse> PurchaseInvoices { get; set; } = new();

    // Summary Statistics (populated in GetPurchaseOrder only)
    public PurchaseOrderSummaryResponse Summary { get; set; } = new();

    // Recent Activities (populated in GetPurchaseOrder only)
    public List<ActivityTimelineItem> RecentActivities { get; set; } = new();
}

public class PurchaseOrderItemResponse
{
    public Guid MaterialId { get; init; }
    public string MaterialName { get; init; }
    public decimal QuantityValue { get; init; }
    public string QuantityUnit { get; init; }
    public decimal UnitPriceAmount { get; init; }
    public string UnitPriceCurrency { get; init; }
    public string Notes { get; init; }
    public decimal ReceivedQuantityValue { get; init; }
    public string ReceivedQuantityUnit { get; init; }
    public decimal ItemFulfillmentPercentage { get; init; }
}

public class GoodsReceiptSummaryResponse
{
    public Guid Id { get; init; }
    public string ReceiptNumber { get; init; } = string.Empty;
    public DateTime ReceivedDate { get; init; }
    public string ReceivedBy { get; init; } = string.Empty;
    public string Status { get; init; } = string.Empty;
    public decimal TotalValue { get; init; }
    public string Currency { get; init; } = string.Empty;
    public int ItemCount { get; init; }
    public string? DeliveryNote { get; init; }
}

public class PurchaseInvoiceSummaryResponse
{
    public Guid Id { get; init; }
    public string SupplierInvoiceNumber { get; init; } = string.Empty;
    public DateTime InvoiceDate { get; init; }
    public DateTime DueDate { get; init; }
    public string Status { get; init; } = string.Empty;
    public decimal TotalAmount { get; init; }
    public string Currency { get; init; } = string.Empty;
    public bool? LastMatchingResult { get; init; }
    public decimal? LastMatchingVariancePercentage { get; init; }
    public bool HasMatchingDiscrepancies { get; init; }
    public int ItemCount { get; init; }
}

public class PurchaseOrderSummaryResponse
{
    public int TotalGoodsReceipts { get; init; }
    public int ConfirmedGoodsReceipts { get; init; }
    public int TotalPurchaseInvoices { get; init; }
    public int ApprovedPurchaseInvoices { get; init; }
    public int InvoicesRequiringReview { get; init; }
    public decimal TotalInvoicedAmount { get; init; }
    public decimal TotalReceivedValue { get; init; }
    public bool IsFullyReceived { get; init; }
    public bool IsFullyInvoiced { get; init; }
    public bool HasPendingInvoices { get; init; }
}