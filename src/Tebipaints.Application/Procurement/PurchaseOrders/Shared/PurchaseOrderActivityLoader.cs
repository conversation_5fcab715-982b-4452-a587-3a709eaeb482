using System.Data;
using Dapper;
using Tebipaints.Application.Procurement.PurchaseOrders.GetPurchaseOrderActivities;

namespace Tebipaints.Application.Procurement.PurchaseOrders.Shared;

public static class PurchaseOrderActivityLoader
{
    public static async Task<List<ActivityTimelineItem>> LoadActivitiesAsync(
        IDbConnection connection, 
        Guid purchaseOrderId, 
        int? limit = null)
    {
        var activities = new List<ActivityTimelineItem>();

        // Load activities from multiple sources
        activities.AddRange(await LoadPurchaseOrderActivities(connection, purchaseOrderId));
        activities.AddRange(await LoadGoodsReceiptActivities(connection, purchaseOrderId));
        activities.AddRange(await LoadPurchaseInvoiceActivities(connection, purchaseOrderId));

        // Sort by timestamp descending (most recent first)
        activities = activities.OrderByDescending(a => a.Timestamp).ToList();

        // Apply limit if specified
        if (limit.HasValue)
        {
            activities = activities.Take(limit.Value).ToList();
        }

        return activities;
    }

    private static async Task<List<ActivityTimelineItem>> LoadPurchaseOrderActivities(IDbConnection connection, Guid purchaseOrderId)
    {
        const string sql = """
            SELECT 
                gen_random_uuid() as Id,
                po.order_date as Timestamp,
                'ORDER_CREATED' as ActivityType,
                'Purchase Order Created' as Title,
                CONCAT('Purchase order ', po.order_number_value, ' created for supplier ', s.name) as Description,
                po.requested_by as PerformedBy,
                'success' as Status
            FROM purchase_orders po
            LEFT JOIN suppliers s ON po.supplier_id = s.id
            WHERE po.id = @PurchaseOrderId

            UNION ALL

            SELECT 
                gen_random_uuid() as Id,
                COALESCE(po.approved_date, po.order_date) as Timestamp,
                'ORDER_APPROVED' as ActivityType,
                'Purchase Order Approved' as Title,
                CONCAT('Purchase order approved by ', COALESCE(po.approved_by, 'System')) as Description,
                po.approved_by as PerformedBy,
                'success' as Status
            FROM purchase_orders po
            WHERE po.id = @PurchaseOrderId 
              AND po.status IN ('Approved', 'Closed')
              AND po.approved_date IS NOT NULL

            ORDER BY Timestamp DESC
            """;

        var activities = await connection.QueryAsync<ActivityTimelineItem>(sql, new { PurchaseOrderId = purchaseOrderId });
        return activities.ToList();
    }

    private static async Task<List<ActivityTimelineItem>> LoadGoodsReceiptActivities(IDbConnection connection, Guid purchaseOrderId)
    {
        const string sql = """
            SELECT 
                gen_random_uuid() as Id,
                gr.received_date as Timestamp,
                'GOODS_RECEIPT_CREATED' as ActivityType,
                'Goods Receipt Created' as Title,
                CONCAT('Goods receipt ', gr.receipt_number, ' created by ', gr.received_by) as Description,
                gr.received_by as PerformedBy,
                CASE WHEN gr.status = 'Confirmed' THEN 'success' ELSE 'info' END as Status
            FROM goods_receipts gr
            WHERE gr.purchase_order_id = @PurchaseOrderId

            UNION ALL

            SELECT 
                gen_random_uuid() as Id,
                gr.received_date as Timestamp,
                'GOODS_RECEIPT_CONFIRMED' as ActivityType,
                'Goods Receipt Confirmed' as Title,
                CONCAT('Goods receipt ', gr.receipt_number, ' confirmed - inventory updated') as Description,
                gr.received_by as PerformedBy,
                'success' as Status
            FROM goods_receipts gr
            WHERE gr.purchase_order_id = @PurchaseOrderId 
              AND gr.status = 'Confirmed'

            ORDER BY Timestamp DESC
            """;

        var activities = await connection.QueryAsync<ActivityTimelineItem>(sql, new { PurchaseOrderId = purchaseOrderId });
        return activities.ToList();
    }

    private static async Task<List<ActivityTimelineItem>> LoadPurchaseInvoiceActivities(IDbConnection connection, Guid purchaseOrderId)
    {
        const string sql = """
            SELECT 
                gen_random_uuid() as Id,
                pi.invoice_date as Timestamp,
                'INVOICE_CREATED' as ActivityType,
                'Purchase Invoice Created' as Title,
                CONCAT('Invoice ', pi.supplier_invoice_number, ' received from supplier') as Description,
                'System' as PerformedBy,
                'info' as Status
            FROM purchase_invoices pi
            WHERE pi.purchase_order_id = @PurchaseOrderId

            UNION ALL

            SELECT 
                gen_random_uuid() as Id,
                COALESCE(pi.last_matching_date, pi.invoice_date) as Timestamp,
                'INVOICE_MATCHED' as ActivityType,
                CASE
                    WHEN pi.last_matching_result = true THEN '3-Way Matching Successful'
                    ELSE '3-Way Matching - Discrepancies Found'
                END as Title,
                CASE
                    WHEN pi.last_matching_result = true THEN
                        CONCAT('Invoice automatically matched with ', CAST(COALESCE(pi.last_matching_variance_percentage, 0) AS VARCHAR), '% variance')
                    ELSE
                        CONCAT('Invoice requires review - ', CAST(COALESCE(pi.last_matching_variance_percentage, 0) AS VARCHAR), '% variance detected')
                END as Description,
                'System' as PerformedBy,
                CASE
                    WHEN pi.last_matching_result = true THEN 'success'
                    ELSE 'warning'
                END as Status
            FROM purchase_invoices pi
            WHERE pi.purchase_order_id = @PurchaseOrderId 
              AND pi.last_matching_date IS NOT NULL

            UNION ALL

            SELECT 
                gen_random_uuid() as Id,
                pi.approved_date as Timestamp,
                'INVOICE_APPROVED' as ActivityType,
                'Invoice Approved' as Title,
                CONCAT('Invoice ', pi.supplier_invoice_number, ' approved by ', pi.approved_by) as Description,
                pi.approved_by as PerformedBy,
                'success' as Status
            FROM purchase_invoices pi
            WHERE pi.purchase_order_id = @PurchaseOrderId 
              AND pi.status = 'Approved'
              AND pi.approved_date IS NOT NULL

            ORDER BY Timestamp DESC
            """;

        var activities = await connection.QueryAsync<ActivityTimelineItem>(sql, new { PurchaseOrderId = purchaseOrderId });
        return activities.ToList();
    }
}
