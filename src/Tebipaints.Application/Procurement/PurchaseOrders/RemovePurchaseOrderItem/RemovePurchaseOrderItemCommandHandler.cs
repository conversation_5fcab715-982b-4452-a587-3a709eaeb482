using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Errors;

namespace Tebipaints.Application.Procurement.PurchaseOrders.RemovePurchaseOrderItem;

public class RemovePurchaseOrderItemCommandHandler : ICommandHandler<RemovePurchaseOrderItemCommand, Guid>
{
    private readonly IPurchaseOrderRepository _repository;
    private readonly IUnitOfWork _unitOfWork;

    public RemovePurchaseOrderItemCommandHandler(IUnitOfWork unitOfWork, IPurchaseOrderRepository repository)
    {
        _unitOfWork = unitOfWork;
        _repository = repository;
    }

    public async Task<Result<Guid>> Handle(RemovePurchaseOrderItemCommand request, CancellationToken cancellationToken)
    {
        //. Fetch Purchase Order
        var purchaseOrder = await _repository.GetByIdAsync(request.PurchaseOrderId, cancellationToken);
        if (purchaseOrder == null)
        {
            return Result.Failure<Guid>(ProcurementErrors.PurchaseOrderNotFound());
        }
        
        // 2. Filter for specific item
        var item = purchaseOrder.Items.FirstOrDefault(item => item.MaterialId == request.MaterialId);
        if (item == null)
        {
            return Result.Failure<Guid>(ProcurementErrors.OrderItemNotFound());
        }
        
        // 3. Remove item
        purchaseOrder.RemoveItem(item);
        
        _repository.Update(purchaseOrder);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        return Result.Success(purchaseOrder.Id);
    }
}