namespace Tebipaints.Application.Procurement.PurchaseOrders.GeneratePurchaseOrderPdf;

public class PurchaseOrderDto
{
    public string PurchaseOrderNumber { get; set; }
    public string ShippingMethod { get; set; }
    public DateTime? IssueDate { get; set; }
    public DateTime? PromiseDate { get; set; }
    public string SupplierName { get; set; }
    public string ContactPerson { get; set; }
    public string SupplierAddress { get; set; }
    public string SupplierPhoneNumber { get; set; }
    public string SupplierEmail { get; set; }
    public decimal? DiscountPercent { get; set; }
    public decimal? Freight { get; set; }
    public decimal? TaxPercent { get; set; }
    public List<PaymentTermsDto> PaymentTerms { get; set; }
    public List<PurchaseOrderItemDto> Items { get; set; }
    public decimal? TotalAmount { get; set; }
    public string OrderCurrency  { get; set; }
}

public class PurchaseOrderItemDto
{
    public string Name { get; set; }
    public string Description { get; set; }
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public string Unit { get; set; }
}

public class PaymentTermsDto
{
    public string TermType { get; set; }
    public string TermDescription { get; set; }
    public string TermExpiration { get; set; }
}