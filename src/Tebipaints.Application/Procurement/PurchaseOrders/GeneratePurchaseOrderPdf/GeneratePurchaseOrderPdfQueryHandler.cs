using System.Net;
using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Application.Abstractions.Reporting;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement.Errors;

namespace Tebipaints.Application.Procurement.PurchaseOrders.GeneratePurchaseOrderPdf;

public class GeneratePurchaseOrderPdfQueryHandler : IQueryHandler<GeneratePurchaseOrderPdfQuery, byte[]>
{
    private readonly IPdfGenerator _pdfGenerator;
    private readonly ISqlConnectionFactory _sqlConnectionFactory;

    public GeneratePurchaseOrderPdfQueryHandler(IPdfGenerator pdfGenerator, ISqlConnectionFactory sqlConnectionFactory)
    {
        _pdfGenerator = pdfGenerator;
        _sqlConnectionFactory = sqlConnectionFactory;
    }

    public async Task<Result<byte[]>> Handle(GeneratePurchaseOrderPdfQuery request, CancellationToken cancellationToken)
    {
        var connection = _sqlConnectionFactory.CreateConnection();

        const string sql = @"
        SELECT
            po.order_number_value AS PurchaseOrderNumber,
            po.order_date AS IssueDate,
            po.promise_date AS PromiseDate,
            s.name AS SupplierName,
            s.contact_info_contact_person AS ContactPerson,
            s.contact_info_address AS SupplierAddress,
            s.contact_info_phone AS SupplierPhoneNumber,
            s.contact_info_email AS SupplierEmail,
            po.discount_percentage AS DiscountPercent,
            po.freight_charge_amount  AS FreightAmount,
            po.tax_rate AS TaxRate,
            po.order_currency AS OrderCurrency,
            poi.material_name AS ItemName,
            poi.quantity_value AS ItemQuantity,
            poi.unit_price_amount AS ItemUnitPrice,
            poi.quantity_unit AS ItemUnit,
            ct.type AS TermType,
            ct.description AS TermDescription,
            ct.expiration_date AS TermExpiration
        FROM purchase_orders po
        LEFT JOIN suppliers s ON po.supplier_id = s.id
        LEFT JOIN purchase_order_items poi ON poi.purchase_order_id = po.id
        LEFT JOIN contracts c ON po.supplier_id = c.supplier_id 
        LEFT JOIN contract_terms ct ON ct.contract_id = c.id
        WHERE po.id = @PurchaseOrderId
    ";

    var lookup = new Dictionary<string, PurchaseOrderDto>();

    var rows = await connection.QueryAsync<PurchaseOrderRawRow>(sql, new { PurchaseOrderId = request.PurchaseOrderId });

    if (!rows.Any())
        return Result.Failure<byte[]>(ProcurementErrors.PurchaseOrderNotFound());

    var first = rows.First();

    var dto = new PurchaseOrderDto
    {
        PurchaseOrderNumber = first.PurchaseOrderNumber,
        ShippingMethod = first.ShippingMethod,
        IssueDate = first.IssueDate,
        PromiseDate = first.PromiseDate,
        SupplierName = first.SupplierName,
        ContactPerson = first.ContactPerson,
        SupplierAddress = first.SupplierAddress,
        SupplierPhoneNumber = first.SupplierPhoneNumber,
        SupplierEmail = first.SupplierEmail,
        DiscountPercent = first.DiscountPercent,
        Freight = first.FreightAmount ?? 0,
        TaxPercent = first.TaxRate,
        TotalAmount = first.TotalAmount ?? 0,
        OrderCurrency = first.OrderCurrency,
        Items = new List<PurchaseOrderItemDto>(),
        PaymentTerms = new List<PaymentTermsDto>()
    };

    foreach (var row in rows)
    {
        if (!string.IsNullOrWhiteSpace(row.ItemName) &&
            !dto.Items.Any(i => i.Name == row.ItemName && i.Description == row.ItemDescription))
        {
            dto.Items.Add(new PurchaseOrderItemDto
            {
                Name = row.ItemName,
                Description = row.ItemDescription,
                Quantity = row.ItemQuantity ?? 0,
                UnitPrice = row.ItemUnitPrice ?? 0,
                Unit = row.ItemUnit
            });
        }

        if (!string.IsNullOrWhiteSpace(row.TermType) && dto.PaymentTerms.All(pt => pt.TermType != row.TermType))
        {
            dto.PaymentTerms.Add(new PaymentTermsDto
            {
                TermType = row.TermType,
                TermDescription = row.TermDescription,
                TermExpiration = row.TermExpiration
            });
        }
    }

    if (dto == null)
        return Result.Failure<byte[]>(ProcurementErrors.PurchaseOrderNotFound());

    // Now generate the PDF
    var pdfBytes = await _pdfGenerator.GeneratePurchaseOrderPdf(dto);

    return Result.Success(pdfBytes);
    }
    
    private class PurchaseOrderRawRow
    {
        public string PurchaseOrderNumber { get; set; }
        public string ShippingMethod { get; set; }
        public DateTime? IssueDate { get; set; }
        public DateTime? PromiseDate { get; set; }
        public string SupplierName { get; set; }
        public string ContactPerson { get; set; }
        public string SupplierAddress { get; set; }
        public string SupplierPhoneNumber { get; set; }
        public string SupplierEmail { get; set; }
        public decimal? DiscountPercent { get; set; }
        public decimal? FreightAmount { get; set; }
        public decimal? TaxRate { get; set; }
        public decimal? TotalAmount { get; set; }
        public string OrderCurrency { get; set; }

        public string ItemName { get; set; }
        public string ItemDescription { get; set; }
        public int? ItemQuantity { get; set; }
        public decimal? ItemUnitPrice { get; set; }
        public string ItemUnit { get; set; }

        public string TermExpiration { get; set; }
        public string TermType { get; set; }
        public string TermDescription { get; set; }
    }

}