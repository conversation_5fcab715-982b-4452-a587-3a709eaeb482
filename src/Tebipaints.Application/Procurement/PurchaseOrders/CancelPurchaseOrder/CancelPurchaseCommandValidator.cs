using FluentValidation;

namespace Tebipaints.Application.Procurement.PurchaseOrders.CancelPurchaseOrder;

public class CancelPurchaseCommandValidator : AbstractValidator<CancelPurchaseOrderCommand>
{
    public CancelPurchaseCommandValidator()
    {
        RuleFor(po => po.PurchaseOrderId).NotEmpty();
        RuleFor(po => po.PurchaseOrderId).NotEmpty();
        RuleFor(po => po.Reason).NotEmpty();
    }
}