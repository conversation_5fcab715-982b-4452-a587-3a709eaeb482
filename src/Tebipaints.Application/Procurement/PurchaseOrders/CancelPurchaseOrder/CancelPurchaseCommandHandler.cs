using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Errors;

namespace Tebipaints.Application.Procurement.PurchaseOrders.CancelPurchaseOrder;

public class CancelPurchaseCommandHandler : ICommandHandler<CancelPurchaseOrderCommand>
{
    private readonly IPurchaseOrderRepository _repository;
    private readonly IUnitOfWork _unitOfWork;

    public CancelPurchaseCommandHandler(IPurchaseOrderRepository repository, IUnitOfWork unitOfWork)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(CancelPurchaseOrderCommand request, CancellationToken cancellationToken)
    {
        // 1. verify existence of purchase order
        var order = await _repository.GetByIdAsync(request.PurchaseOrderId, cancellationToken);
        if (order == null)
        {
            return Result.Failure<Guid>(ProcurementErrors.PurchaseOrderNotFound());
        }
        
        order.Cancel(request.Reason);
        
        _repository.Update(order);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        return Result.Success();
    }
}