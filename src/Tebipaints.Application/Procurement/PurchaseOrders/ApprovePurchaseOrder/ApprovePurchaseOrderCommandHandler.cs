using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Errors;

namespace Tebipaints.Application.Procurement.PurchaseOrders.ApprovePurchaseOrder;

public class ApprovePurchaseOrderCommandHandler : ICommandHandler<ApprovePurchaseOrderCommand, Guid>
{
    private readonly IPurchaseOrderRepository _repository;
    private readonly IUnitOfWork _unitOfWork;

    public ApprovePurchaseOrderCommandHandler(IPurchaseOrderRepository repository, IUnitOfWork unitOfWork)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<Guid>> Handle(ApprovePurchaseOrderCommand request, CancellationToken cancellationToken)
    {
        // 1. verify existence of purchase order
        var order = await _repository.GetByIdAsync(request.PurchaseOrderId, cancellationToken);
        if (order == null)
        {
            return Result.Failure<Guid>(ProcurementErrors.PurchaseOrderNotFound());
        }
        
        // 2. Approve order
        order.Approve(request.ApprovedBy);
        
        // 3. update purchase order
        _repository.Update(order);

        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        return Result.Success(order.Id);
    }
}