using System.Data;
using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Application.Procurement.Metrics.GetProcurementMetrics;

public class GetProcurementMetricsQueryHandler : IQueryHandler<GetProcurementMetricsQuery, ProcurementMetricsResponse>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;

    public GetProcurementMetricsQueryHandler(ISqlConnectionFactory sqlConnectionFactory)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
    }

    public async Task<Result<ProcurementMetricsResponse>> Handle(GetProcurementMetricsQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();

        var response = new ProcurementMetricsResponse();

        // Load all metrics in parallel for better performance
        var tasks = new[]
        {
            LoadKPIMetrics(connection, request),
            LoadSupplierDeliveryMetrics(connection, request),
            LoadMonthlyOrderVsDeliveryMetrics(connection, request),
            LoadSupplierVolumeMetrics(connection, request),
            LoadSupplierPerformanceMetrics(connection, request),
            LoadMaterialCategoryMetrics(connection, request),
            LoadInvoiceMatchingMetrics(connection, request)
        };

        var results = await Task.WhenAll(tasks);

        response.KPIs = (ProcurementKpiResponse)results[0];
        response.DeliveryDelaysBySupplier = (List<SupplierDeliveryMetricsResponse>)results[1];
        response.MaterialsOrderedVsDelivered = (List<MonthlyOrderVsDeliveryResponse>)results[2];
        response.PurchaseVolumeBySupplier = (List<SupplierVolumeResponse>)results[3];
        response.SupplierPerformance = (List<SupplierPerformanceResponse>)results[4];
        response.MaterialCategoryBreakdown = (List<MaterialCategoryMetricsResponse>)results[5];
        response.InvoiceMatchingMetrics = (InvoiceMatchingMetricsResponse)results[6];

        return Result.Success(response);
    }

    private async Task<object> LoadKPIMetrics(IDbConnection connection, GetProcurementMetricsQuery request)
    {
        const string sql = """
            WITH date_ranges AS (
                SELECT 
                    COALESCE(@FromDate, '1900-01-01') as from_date,
                    COALESCE(@ToDate, GETDATE()) as to_date,
                    DATEADD(MONTH, -1, COALESCE(@FromDate, DATEADD(MONTH, -1, GETDATE()))) as prev_from_date,
                    DATEADD(MONTH, -1, COALESCE(@ToDate, GETDATE())) as prev_to_date
            ),
            current_period AS (
                SELECT 
                    COUNT(*) as total_orders,
                    COUNT(CASE WHEN po.status IN ('Approved') THEN 1 END) as orders_in_progress,
                    COUNT(CASE WHEN po.status = 'Closed' THEN 1 END) as completed_orders,
                    COALESCE(SUM(CASE WHEN po.status = 'Closed' THEN ot.total_amount END), 0) as total_spend
                FROM purchase_orders po
                CROSS JOIN date_ranges dr
                LEFT JOIN (
                    SELECT 
                        purchase_order_id,
                        SUM(quantity_value * unit_price_amount) as total_amount
                    FROM purchase_order_items
                    GROUP BY purchase_order_id
                ) ot ON po.id = ot.purchase_order_id
                WHERE po.order_date >= dr.from_date AND po.order_date <= dr.to_date
            ),
            previous_period AS (
                SELECT 
                    COUNT(*) as total_orders,
                    COUNT(CASE WHEN po.status IN ('Approved') THEN 1 END) as orders_in_progress,
                    COUNT(CASE WHEN po.status = 'Closed' THEN 1 END) as completed_orders,
                    COALESCE(SUM(CASE WHEN po.status = 'Closed' THEN ot.total_amount END), 0) as total_spend
                FROM purchase_orders po
                CROSS JOIN date_ranges dr
                LEFT JOIN (
                    SELECT 
                        purchase_order_id,
                        SUM(quantity_value * unit_price_amount) as total_amount
                    FROM purchase_order_items
                    GROUP BY purchase_order_id
                ) ot ON po.id = ot.purchase_order_id
                WHERE po.order_date >= dr.prev_from_date AND po.order_date <= dr.prev_to_date
            )
            SELECT 
                cp.total_orders as TotalPurchaseOrders,
                CASE WHEN pp.total_orders = 0 THEN 0 
                     ELSE ROUND(((cp.total_orders - pp.total_orders) * 100.0 / pp.total_orders), 2) 
                END as TotalPurchaseOrdersGrowth,
                
                cp.orders_in_progress as OrdersInProgress,
                CASE WHEN pp.orders_in_progress = 0 THEN 0 
                     ELSE ROUND(((cp.orders_in_progress - pp.orders_in_progress) * 100.0 / pp.orders_in_progress), 2) 
                END as OrdersInProgressGrowth,
                
                cp.completed_orders as CompletedOrders,
                CASE WHEN pp.completed_orders = 0 THEN 0 
                     ELSE ROUND(((cp.completed_orders - pp.completed_orders) * 100.0 / pp.completed_orders), 2) 
                END as CompletedOrdersGrowth,
                
                cp.total_spend as TotalSpend,
                CASE WHEN pp.total_spend = 0 THEN 0 
                     ELSE ROUND(((cp.total_spend - pp.total_spend) * 100.0 / pp.total_spend), 2) 
                END as TotalSpendGrowth,
                
                @Currency as Currency
            FROM current_period cp
            CROSS JOIN previous_period pp
            """;

        var kpis = await connection.QueryFirstOrDefaultAsync<ProcurementKpiResponse>(sql, new
        {
            FromDate = request.FromDate,
            ToDate = request.ToDate,
            Currency = request.Currency ?? "GHS"
        });

        return kpis ?? new ProcurementKpiResponse();
    }

    private async Task<object> LoadSupplierDeliveryMetrics(IDbConnection connection, GetProcurementMetricsQuery request)
    {
        const string sql = """
            WITH supplier_deliveries AS (
                SELECT 
                    s.id as SupplierId,
                    s.name as SupplierName,
                    po.id as PurchaseOrderId,
                    po.promise_date,
                    gr.received_date,
                    CASE 
                        WHEN gr.received_date IS NULL THEN NULL
                        WHEN gr.received_date <= po.promise_date THEN 0
                        ELSE DATEDIFF(day, po.promise_date, gr.received_date)
                    END as delay_days
                FROM suppliers s
                INNER JOIN purchase_orders po ON s.id = po.supplier_id
                LEFT JOIN goods_receipts gr ON po.id = gr.purchase_order_id AND gr.status = 'Confirmed'
                WHERE (@FromDate IS NULL OR po.order_date >= @FromDate)
                  AND (@ToDate IS NULL OR po.order_date <= @ToDate)
                  AND (@SupplierId IS NULL OR s.id = @SupplierId)
            )
            SELECT 
                SupplierId,
                SupplierName,
                AVG(CAST(COALESCE(delay_days, 0) AS FLOAT)) as AverageDeliveryDays,
                COUNT(*) as TotalOrders,
                COUNT(CASE WHEN delay_days > 0 THEN 1 END) as DelayedOrders,
                CASE 
                    WHEN COUNT(*) = 0 THEN 0
                    ELSE ROUND((COUNT(CASE WHEN delay_days <= 0 THEN 1 END) * 100.0 / COUNT(*)), 2)
                END as OnTimeDeliveryPercentage
            FROM supplier_deliveries
            GROUP BY SupplierId, SupplierName
            ORDER BY AverageDeliveryDays DESC
            """;

        var metrics = await connection.QueryAsync<SupplierDeliveryMetricsResponse>(sql, new
        {
            FromDate = request.FromDate,
            ToDate = request.ToDate,
            SupplierId = request.SupplierId
        });

        return metrics.ToList();
    }

    private async Task<object> LoadMonthlyOrderVsDeliveryMetrics(IDbConnection connection, GetProcurementMetricsQuery request)
    {
        const string sql = """
            WITH monthly_data AS (
                SELECT 
                    YEAR(po.order_date) as year,
                    MONTH(po.order_date) as month_num,
                    DATENAME(month, po.order_date) as month_name,
                    SUM(poi.quantity_value) as ordered_quantity,
                    SUM(COALESCE(poi.received_quantity_value, 0)) as delivered_quantity
                FROM purchase_orders po
                INNER JOIN purchase_order_items poi ON po.id = poi.purchase_order_id
                WHERE po.order_date >= DATEADD(month, -6, GETDATE())
                  AND (@FromDate IS NULL OR po.order_date >= @FromDate)
                  AND (@ToDate IS NULL OR po.order_date <= @ToDate)
                GROUP BY YEAR(po.order_date), MONTH(po.order_date), DATENAME(month, po.order_date)
            )
            SELECT 
                LEFT(month_name, 3) as Month,
                month_num as MonthNumber,
                year as Year,
                ordered_quantity as OrderedQuantity,
                delivered_quantity as DeliveredQuantity,
                CASE 
                    WHEN ordered_quantity = 0 THEN 0
                    ELSE ROUND((delivered_quantity * 100.0 / ordered_quantity), 2)
                END as DeliveryMatchPercentage
            FROM monthly_data
            ORDER BY year, month_num
            """;

        var metrics = await connection.QueryAsync<MonthlyOrderVsDeliveryResponse>(sql, new
        {
            FromDate = request.FromDate,
            ToDate = request.ToDate
        });

        return metrics.ToList();
    }

    private async Task<object> LoadSupplierVolumeMetrics(IDbConnection connection, GetProcurementMetricsQuery request)
    {
        const string sql = """
            WITH supplier_volumes AS (
                SELECT 
                    s.id as SupplierId,
                    s.name as SupplierName,
                    SUM(poi.quantity_value * poi.unit_price_amount) as total_volume
                FROM suppliers s
                INNER JOIN purchase_orders po ON s.id = po.supplier_id
                INNER JOIN purchase_order_items poi ON po.id = poi.purchase_order_id
                WHERE po.status IN ('Approved', 'Closed')
                  AND (@FromDate IS NULL OR po.order_date >= @FromDate)
                  AND (@ToDate IS NULL OR po.order_date <= @ToDate)
                  AND (@SupplierId IS NULL OR s.id = @SupplierId)
                GROUP BY s.id, s.name
            ),
            total_volume AS (
                SELECT SUM(total_volume) as grand_total
                FROM supplier_volumes
            )
            SELECT 
                sv.SupplierId,
                sv.SupplierName,
                sv.total_volume as TotalVolume,
                CASE 
                    WHEN tv.grand_total = 0 THEN 0
                    ELSE ROUND((sv.total_volume * 100.0 / tv.grand_total), 2)
                END as VolumePercentage,
                @Currency as Currency,
                '' as Color
            FROM supplier_volumes sv
            CROSS JOIN total_volume tv
            ORDER BY sv.total_volume DESC
            """;

        var metrics = await connection.QueryAsync<SupplierVolumeResponse>(sql, new
        {
            FromDate = request.FromDate,
            ToDate = request.ToDate,
            SupplierId = request.SupplierId,
            Currency = request.Currency ?? "GHS"
        });

        // Add chart colors
        var colors = new[] { "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", "#DDA0DD", "#98D8C8", "#F7DC6F" };
        var result = metrics.Select((m, index) => new SupplierVolumeResponse
        {
            SupplierId = m.SupplierId,
            SupplierName = m.SupplierName,
            TotalVolume = m.TotalVolume,
            VolumePercentage = m.VolumePercentage,
            Currency = m.Currency,
            Color = colors[index % colors.Length]
        }).ToList();

        return result;
    }

    private async Task<object> LoadSupplierPerformanceMetrics(IDbConnection connection, GetProcurementMetricsQuery request)
    {
        const string sql = """
            WITH supplier_performance AS (
                SELECT
                    s.id as SupplierId,
                    s.name as SupplierName,
                    COUNT(DISTINCT po.id) as total_orders,
                    SUM(poi.quantity_value * poi.unit_price_amount) as total_spend,
                    AVG(CASE
                        WHEN gr.received_date IS NULL THEN NULL
                        WHEN gr.received_date <= po.promise_date THEN 0
                        ELSE DATEDIFF(day, po.promise_date, gr.received_date)
                    END) as avg_delivery_days,
                    COUNT(CASE WHEN gr.received_date <= po.promise_date THEN 1 END) * 100.0 / COUNT(DISTINCT po.id) as on_time_rate,
                    AVG(CASE WHEN gri.quality_status = 'Approved' THEN 100.0 ELSE 0 END) as quality_score,
                    COUNT(CASE WHEN pi.last_matching_result = 1 THEN 1 END) * 100.0 / NULLIF(COUNT(DISTINCT pi.id), 0) as invoice_accuracy
                FROM suppliers s
                INNER JOIN purchase_orders po ON s.id = po.supplier_id
                INNER JOIN purchase_order_items poi ON po.id = poi.purchase_order_id
                LEFT JOIN goods_receipts gr ON po.id = gr.purchase_order_id AND gr.status = 'Confirmed'
                LEFT JOIN goods_receipt_items gri ON gr.id = gri.goods_receipt_id
                LEFT JOIN purchase_invoices pi ON po.id = pi.purchase_order_id
                WHERE (@FromDate IS NULL OR po.order_date >= @FromDate)
                  AND (@ToDate IS NULL OR po.order_date <= @ToDate)
                  AND (@SupplierId IS NULL OR s.id = @SupplierId)
                GROUP BY s.id, s.name
            )
            SELECT
                SupplierId,
                SupplierName,
                COALESCE(on_time_rate, 0) as OnTimeDeliveryRate,
                COALESCE(quality_score, 0) as QualityScore,
                COALESCE(invoice_accuracy, 0) as InvoiceAccuracy,
                COALESCE(avg_delivery_days, 0) as AverageDeliveryDays,
                total_orders as TotalOrders,
                total_spend as TotalSpend,
                CASE
                    WHEN on_time_rate >= 95 AND quality_score >= 95 AND invoice_accuracy >= 95 THEN 'A'
                    WHEN on_time_rate >= 85 AND quality_score >= 85 AND invoice_accuracy >= 85 THEN 'B'
                    WHEN on_time_rate >= 70 AND quality_score >= 70 AND invoice_accuracy >= 70 THEN 'C'
                    ELSE 'D'
                END as PerformanceGrade
            FROM supplier_performance
            ORDER BY on_time_rate DESC, quality_score DESC, invoice_accuracy DESC
            """;

        var metrics = await connection.QueryAsync<SupplierPerformanceResponse>(sql, new
        {
            FromDate = request.FromDate,
            ToDate = request.ToDate,
            SupplierId = request.SupplierId
        });

        return metrics.ToList();
    }

    private async Task<object> LoadMaterialCategoryMetrics(IDbConnection connection, GetProcurementMetricsQuery request)
    {
        const string sql = """
            WITH category_spend AS (
                SELECT
                    COALESCE(m.category, 'Uncategorized') as category_name,
                    SUM(poi.quantity_value * poi.unit_price_amount) as total_spend,
                    COUNT(DISTINCT po.id) as order_count,
                    AVG(poi.quantity_value * poi.unit_price_amount) as avg_order_value
                FROM purchase_orders po
                INNER JOIN purchase_order_items poi ON po.id = poi.purchase_order_id
                LEFT JOIN materials m ON poi.material_id = m.id
                WHERE po.status IN ('Approved', 'Closed')
                  AND (@FromDate IS NULL OR po.order_date >= @FromDate)
                  AND (@ToDate IS NULL OR po.order_date <= @ToDate)
                GROUP BY COALESCE(m.category, 'Uncategorized')
            ),
            total_spend AS (
                SELECT SUM(total_spend) as grand_total
                FROM category_spend
            )
            SELECT
                cs.category_name as CategoryName,
                cs.total_spend as TotalSpend,
                CASE
                    WHEN ts.grand_total = 0 THEN 0
                    ELSE ROUND((cs.total_spend * 100.0 / ts.grand_total), 2)
                END as SpendPercentage,
                cs.order_count as OrderCount,
                cs.avg_order_value as AverageOrderValue,
                @Currency as Currency
            FROM category_spend cs
            CROSS JOIN total_spend ts
            ORDER BY cs.total_spend DESC
            """;

        var metrics = await connection.QueryAsync<MaterialCategoryMetricsResponse>(sql, new
        {
            FromDate = request.FromDate,
            ToDate = request.ToDate,
            Currency = request.Currency ?? "GHS"
        });

        return metrics.ToList();
    }

    private async Task<object> LoadInvoiceMatchingMetrics(IDbConnection connection, GetProcurementMetricsQuery request)
    {
        const string sql = """
            SELECT
                COUNT(*) as TotalInvoices,
                COUNT(CASE WHEN last_matching_result = 1 THEN 1 END) as PerfectMatches,
                COUNT(CASE WHEN last_matching_result = 0 THEN 1 END) as InvoicesWithDiscrepancies,
                COUNT(CASE WHEN status = 'RequiresReview' THEN 1 END) as InvoicesRequiringReview,
                CASE
                    WHEN COUNT(*) = 0 THEN 0
                    ELSE ROUND((COUNT(CASE WHEN last_matching_result = 1 THEN 1 END) * 100.0 / COUNT(*)), 2)
                END as MatchingAccuracyPercentage,
                AVG(COALESCE(last_matching_variance_percentage, 0)) as AverageVariancePercentage,
                CASE
                    WHEN COUNT(*) = 0 THEN 0
                    ELSE ROUND((COUNT(CASE WHEN status = 'PendingApproval' THEN 1 END) * 100.0 / COUNT(*)), 2)
                END as AutoApprovalRate
            FROM purchase_invoices pi
            INNER JOIN purchase_orders po ON pi.purchase_order_id = po.id
            WHERE (@FromDate IS NULL OR pi.invoice_date >= @FromDate)
              AND (@ToDate IS NULL OR pi.invoice_date <= @ToDate)
            """;

        var metrics = await connection.QueryFirstOrDefaultAsync<InvoiceMatchingMetricsResponse>(sql, new
        {
            FromDate = request.FromDate,
            ToDate = request.ToDate
        });

        return metrics ?? new InvoiceMatchingMetricsResponse();
    }
}
