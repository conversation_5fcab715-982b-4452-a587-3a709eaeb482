namespace Tebipaints.Application.Procurement.Metrics.GetProcurementMetrics;

public class ProcurementMetricsResponse
{
    // Top-level KPIs (matching your dashboard)
    public ProcurementKpiResponse KPIs { get; set; } = new();
    
    // Detailed breakdowns for charts
    public List<SupplierDeliveryMetricsResponse> DeliveryDelaysBySupplier { get; set; } = new();
    public List<MonthlyOrderVsDeliveryResponse> MaterialsOrderedVsDelivered { get; set; } = new();
    public List<SupplierVolumeResponse> PurchaseVolumeBySupplier { get; set; } = new();
    
    // Additional insights
    public List<SupplierPerformanceResponse> SupplierPerformance { get; set; } = new();
    public List<MaterialCategoryMetricsResponse> MaterialCategoryBreakdown { get; set; } = new();
    public InvoiceMatchingMetricsResponse InvoiceMatchingMetrics { get; set; } = new();
}

public class ProcurementKpiResponse
{
    public int TotalPurchaseOrders { get; set; }
    public decimal TotalPurchaseOrdersGrowth { get; set; } // Percentage change
    
    public int OrdersInProgress { get; set; }
    public decimal OrdersInProgressGrowth { get; set; }
    
    public int CompletedOrders { get; set; }
    public decimal CompletedOrdersGrowth { get; set; }
    
    public decimal TotalSpend { get; set; }
    public decimal TotalSpendGrowth { get; set; }
    public string Currency { get; set; } = "GHS";
    
    // Additional context
    public string TotalOrdersDescription { get; set; } = "All time orders made";
    public string OrdersInProgressDescription { get; set; } = "Actively being fulfilled";
    public string CompletedOrdersDescription { get; set; } = "Delivered & closed";
    public string TotalSpendDescription { get; set; } = "Total value of fulfilled orders";
}

public class SupplierDeliveryMetricsResponse
{
    public Guid SupplierId { get; set; }
    public string SupplierName { get; set; } = string.Empty;
    public decimal AverageDeliveryDays { get; set; }
    public int TotalOrders { get; set; }
    public int DelayedOrders { get; set; }
    public decimal OnTimeDeliveryPercentage { get; set; }
}

public class MonthlyOrderVsDeliveryResponse
{
    public string Month { get; set; } = string.Empty; // "Jan", "Feb", etc.
    public int MonthNumber { get; set; } // 1, 2, etc.
    public decimal OrderedQuantity { get; set; }
    public decimal DeliveredQuantity { get; set; }
    public decimal DeliveryMatchPercentage { get; set; }
    public int Year { get; set; }
}

public class SupplierVolumeResponse
{
    public Guid SupplierId { get; set; }
    public string SupplierName { get; set; } = string.Empty;
    public decimal TotalVolume { get; set; }
    public decimal VolumePercentage { get; set; }
    public string Currency { get; set; } = "GHS";
    public string Color { get; set; } = string.Empty; // For chart colors
}

public class SupplierPerformanceResponse
{
    public Guid SupplierId { get; set; }
    public string SupplierName { get; set; } = string.Empty;
    public decimal OnTimeDeliveryRate { get; set; }
    public decimal QualityScore { get; set; }
    public decimal InvoiceAccuracy { get; set; }
    public decimal AverageDeliveryDays { get; set; }
    public int TotalOrders { get; set; }
    public decimal TotalSpend { get; set; }
    public string PerformanceGrade { get; set; } = string.Empty; // A, B, C, D
}

public class MaterialCategoryMetricsResponse
{
    public string CategoryName { get; set; } = string.Empty;
    public decimal TotalSpend { get; set; }
    public decimal SpendPercentage { get; set; }
    public int OrderCount { get; set; }
    public decimal AverageOrderValue { get; set; }
    public string Currency { get; set; } = "GHS";
}

public class InvoiceMatchingMetricsResponse
{
    public int TotalInvoices { get; set; }
    public int PerfectMatches { get; set; }
    public int InvoicesWithDiscrepancies { get; set; }
    public int InvoicesRequiringReview { get; set; }
    public decimal MatchingAccuracyPercentage { get; set; }
    public decimal AverageVariancePercentage { get; set; }
    public decimal AutoApprovalRate { get; set; }
}
