using FluentValidation;

namespace Tebipaints.Application.Materials.UpdateMaterial;

public class UpdateMaterialCommandValidator : AbstractValidator<UpdateMaterialCommand>
{
    public UpdateMaterialCommandValidator()
    {
        RuleFor(m => m.MaterialId).NotEmpty();
        RuleFor(m => m.Name).NotEmpty();
        RuleFor(m => m.Description).NotEmpty();
        RuleFor(m => m.DefaultUnit).NotEmpty();
    }
}