using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Material;
using Tebipaints.Domain.Material.Errors;

namespace Tebipaints.Application.Materials.UpdateMaterial;

public class UpdateMaterialCommandHandler : ICommandHandler<UpdateMaterialCommand>
{
    private readonly IMaterialRepository _materialRepository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateMaterialCommandHandler(
        IMaterialRepository materialRepository,
        IUnitOfWork unitOfWork)
    {
        _materialRepository = materialRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(
        UpdateMaterialCommand command,
        CancellationToken cancellationToken)
    {
        var material = await _materialRepository
            .GetByIdAsync(command.MaterialId, cancellationToken);

        if (material is null)
        {
            return Result.Failure(MaterialErrors.NotFound(command.MaterialId));
        }

        var result = material.Update(
            command.Name,
            command.Description,
            command.DefaultUnit);

        if (result.IsFailure)
        {
            return result;
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}