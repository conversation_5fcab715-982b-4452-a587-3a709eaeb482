using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation;
using Tebipaints.Domain.Material;
using Tebipaints.Domain.Material.Errors;

namespace Tebipaints.Application.Materials.DiscontinueMaterial;

public class DiscontinueMaterialCommandHandler : ICommandHandler<DiscontinueMaterialCommand>
{
    private readonly IMaterialRepository _materialRepository;
    private readonly IFormulationRepository _formulationRepository;
    private readonly IUnitOfWork _unitOfWork;

    public DiscontinueMaterialCommandHandler(
        IMaterialRepository materialRepository,
        IFormulationRepository formulationRepository,
        IUnitOfWork unitOfWork)
    {
        _materialRepository = materialRepository;
        _formulationRepository = formulationRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(
        DiscontinueMaterialCommand command,
        CancellationToken cancellationToken)
    {
        var material = await _materialRepository
            .GetByIdAsync(command.MaterialId, cancellationToken);

        if (material is null)
        {
            return Result.Failure(MaterialErrors.NotFound(command.MaterialId));
        }

        // Check if material is used in any active formulations
        var isUsedInFormulations = await _formulationRepository
            .IsUsedInActiveFormulationsAsync(command.MaterialId, cancellationToken);

        if (isUsedInFormulations)
        {
            return Result.Failure(MaterialErrors.UsedInActiveFormulations(command.MaterialId));
        }

        var result = material.Discontinue(command.Reason);

        if (result.IsFailure)
        {
            return result;
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}