using FluentValidation;

namespace Tebipaints.Application.Materials.CreateMaterial;

public class CreateMaterialCommandValidator : AbstractValidator<CreateMaterialCommand>
{
    public CreateMaterialCommandValidator()
    {
        RuleFor(c => c.Name).NotEmpty();
        RuleFor(c => c.Description).NotEmpty();
        RuleFor(c => c.Type).NotEmpty();
        RuleFor(c => c.DefaultUnit).NotEmpty();
    }
}