using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Material;
using Tebipaints.Domain.Material.Enums;
using Tebipaints.Domain.Material.Errors;

namespace Tebipaints.Application.Materials.CreateMaterial;

public class CreateMaterialCommandHandler : ICommandHandler<CreateMaterialCommand, Guid>
{
    private readonly IMaterialRepository _materialRepository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateMaterialCommandHandler(
        IMaterialRepository materialRepository,
        IUnitOfWork unitOfWork)
    {
        _materialRepository = materialRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<Guid>> Handle(
        CreateMaterialCommand command,
        CancellationToken cancellationToken)
    {
        // Check if code is already in use
        var existingMaterial = await _materialRepository
            .GetByCodeAsync(command.Code, cancellationToken);

        if (existingMaterial is not null)
        {
            return Result.Failure<Guid>(MaterialErrors.DuplicateCode(command.Code));
        }

        if (!Enum.TryParse<MaterialType>(command.Type, true, out var materialType))
        {
            return Result.Failure<Guid>(MaterialErrors.InvalidMaterialType(command.Type));
        }
        
        var material = Material.Create(
            command.Code,
            command.Name,
            command.Description,
            materialType,
            command.DefaultUnit);

        if (material.IsFailure)
        {
            return Result.Failure<Guid>(material.Error);
        }

        _materialRepository.Add(material.Value);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success(material.Value.Id);
    }
}