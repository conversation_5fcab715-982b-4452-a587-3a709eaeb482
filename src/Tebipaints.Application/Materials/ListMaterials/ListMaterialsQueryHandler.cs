using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Application.Materials.ListMaterials;

internal sealed class ListMaterialsQueryHandler : IQueryHandler<ListMaterialsQuery, IReadOnlyList<MaterialResponse>>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;

    public ListMaterialsQueryHandler(ISqlConnectionFactory sqlConnectionFactory)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
    }

    public async Task<Result<IReadOnlyList<MaterialResponse>>> Handle(ListMaterialsQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();

        const string sql = """
                           SELECT
                           id as Id,
                           code as Code,
                           name as Name,
                           description as Description,
                           type as Type,
                           default_unit as DefaultUnit,
                           status as Status
                           FROM materials
                           WHERE status != 'Archived'
                           ORDER BY name;
                           """;

        var materials = await connection.QueryAsync<MaterialResponse>(sql);
        
        return Result.Success<IReadOnlyList<MaterialResponse>>(materials.ToList());
    }
}