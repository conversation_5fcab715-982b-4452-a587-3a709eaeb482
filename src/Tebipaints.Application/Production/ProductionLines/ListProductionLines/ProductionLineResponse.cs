namespace Tebipaints.Application.Production.ProductionLines.ListProductionLines;

public class ProductionLineResponse
{
    public Guid Id { get; init; }
    public string Name { get; init; }
    public string Description { get; init; }
    public string Status { get; init; }
    public List<CapabilityResponse> Capabilities { get; set; } = new();
    public List<BatchScheduleResponse> BatchSchedules { get; set; } = new();
    
    // Production Metrics
    public int TotalBatches { get; init; }
    public int CompletedBatches { get; init; }
    public int FailedBatches { get; init; }
    
    // Efficiency Metrics
    public TimeSpan? AverageBatchDuration { get; init; }
    public decimal AverageYieldRate { get; init; }
    public int BatchesLast30Days { get; init; }
    public decimal AverageStartDelay { get; init; }
    
    // Calculated Properties
    public decimal CompletionRate => TotalBatches > 0 
        ? (decimal)CompletedBatches / TotalBatches * 100 
        : 0;
    
    public decimal OnTimeStartRate => AverageStartDelay <= 0 
        ? 100 
        : 100 - (AverageStartDelay / (24 * 60) * 100);
}

public class BatchScheduleResponse
{
    public string BatchNumber { get; init; }
    public DateTime? StartTime { get; init; }
    public DateTime? EndTime { get; init; }
}

public class CapabilityResponse
{
    public decimal MinBatchSizeValue { get; init; }
    public string MinBatchSizeUnit { get; init; }
    public decimal MaxBatchSizeValue { get; init; }
    public string MaxBatchSizeUnit { get; init; }
}