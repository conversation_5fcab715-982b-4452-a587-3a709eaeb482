using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Application.Production.ProductionLines.ListProductionLines;

public class ListProductionLinesQueryHandler : IQueryHandler<ListProductionLinesQuery, IReadOnlyList<ProductionLineResponse>>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;

    public ListProductionLinesQueryHandler(ISqlConnectionFactory sqlConnectionFactory)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
    }

    public async Task<Result<IReadOnlyList<ProductionLineResponse>>> Handle(ListProductionLinesQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();

        const string sql = """
                           WITH batch_data AS (
                               SELECT 
                                   pls.production_line_id,
                                   wb.id as batch_id,
                                   wb.status,
                                   wb.actual_duration,
                                   wb.estimated_duration,
                                   wb.actual_yield,
                                   wb.target_quantity_value,
                                   wb.actual_start_date,
                                   wb.planned_start_date,
                                   wb.batch_number,
                                   pls.start_time,
                                   pls.end_time
                               FROM production_lines pl
                               LEFT JOIN production_line_schedules pls ON pls.production_line_id = pl.id
                               LEFT JOIN work_order_batches wb ON wb.id = pls.batch_id
                           )
                           SELECT 
                               pl.id as "Id",
                               pl.name as "Name",
                               pl.description as "Description",
                               pl.status as "Status",
                               c.min_batch_size_value as "MinBatchSizeValue",
                               c.min_batch_size_unit as "MinBatchSizeUnit",
                               c.max_batch_size_value as "MaxBatchSizeValue",
                               c.max_batch_size_unit as "MaxBatchSizeUnit",
                               b.batch_number as "BatchNumber",
                               b.start_time as "StartTime",
                               b.end_time as "EndTime",
                               -- Metrics
                               (SELECT COUNT(DISTINCT batch_id)::integer
                                FROM batch_data
                                WHERE production_line_id = pl.id) as "TotalBatches",
                               (SELECT COUNT(DISTINCT batch_id)::integer
                                FROM batch_data
                                WHERE production_line_id = pl.id
                                AND status = 'Completed') as "CompletedBatches",
                               (SELECT COUNT(DISTINCT batch_id)::integer
                                FROM batch_data
                                WHERE production_line_id = pl.id
                                AND status = 'Failed') as "FailedBatches",
                               (SELECT AVG(
                                   CASE 
                                       WHEN actual_duration IS NOT NULL THEN EXTRACT(EPOCH FROM actual_duration)
                                       ELSE EXTRACT(EPOCH FROM estimated_duration)
                                   END)::integer
                                FROM batch_data
                                WHERE production_line_id = pl.id) as "AverageBatchDurationSeconds",
                               COALESCE(
                                   (SELECT AVG(actual_yield * 100.0 / NULLIF(target_quantity_value, 0))::decimal(10,2)
                                    FROM batch_data
                                    WHERE production_line_id = pl.id
                                    AND status = 'Completed'),
                                   0
                               ) as "AverageYieldRate",
                               (SELECT COUNT(DISTINCT batch_id)::integer
                                FROM batch_data
                                WHERE production_line_id = pl.id
                                AND planned_start_date >= CURRENT_DATE - INTERVAL '30 days') as "BatchesLast30Days",
                               COALESCE(
                                   (SELECT AVG(EXTRACT(EPOCH FROM (actual_start_date - planned_start_date)) / 60)::decimal(10,2)
                                    FROM batch_data
                                    WHERE production_line_id = pl.id
                                    AND actual_start_date IS NOT NULL),
                                   0
                               ) as "AverageStartDelay"
                           FROM production_lines pl
                           LEFT JOIN capabilities c ON c.production_line_id = pl.id
                           LEFT JOIN batch_data b ON b.production_line_id = pl.id
                           ORDER BY pl.name;
                           """;

        var rows = await connection.QueryAsync(sql);
        var productionLineDictionary = new Dictionary<Guid, ProductionLineResponse>();

        foreach (var row in rows)
        {
            var id = row.Id;
            if (!productionLineDictionary.TryGetValue(id, out ProductionLineResponse existingProductionLine))
            {
                existingProductionLine = new ProductionLineResponse
                {
                    Id = id,
                    Name = row.Name,
                    Description = row.Description,
                    Status = row.Status,
                    TotalBatches = Convert.ToInt32(row.TotalBatches),
                    CompletedBatches = Convert.ToInt32(row.CompletedBatches),
                    FailedBatches = Convert.ToInt32(row.FailedBatches),
                    AverageBatchDuration = row.AverageBatchDurationSeconds != null
                        ? TimeSpan.FromSeconds(Convert.ToInt32(row.AverageBatchDurationSeconds))
                        : null,
                    AverageYieldRate = Convert.ToDecimal(row.AverageYieldRate ?? 0),
                    BatchesLast30Days = Convert.ToInt32(row.BatchesLast30Days),
                    AverageStartDelay = Convert.ToDecimal(row.AverageStartDelay ?? 0),
                    Capabilities = new List<CapabilityResponse>(),
                    BatchSchedules = new List<BatchScheduleResponse>()
                };
                productionLineDictionary.Add(id, existingProductionLine);
            }

            if (row.MinBatchSizeValue != null)
            {
                var capability = new CapabilityResponse
                {
                    MinBatchSizeValue = Convert.ToDecimal(row.MinBatchSizeValue),
                    MinBatchSizeUnit = row.MinBatchSizeUnit,
                    MaxBatchSizeValue = Convert.ToDecimal(row.MaxBatchSizeValue),
                    MaxBatchSizeUnit = row.MaxBatchSizeUnit
                };

                if (!existingProductionLine.Capabilities.Any(c =>
                    c.MinBatchSizeValue == capability.MinBatchSizeValue &&
                    c.MaxBatchSizeValue == capability.MaxBatchSizeValue))
                {
                    existingProductionLine.Capabilities.Add(capability);
                }
            }

            if (!string.IsNullOrEmpty(row.BatchNumber))
            {
                var batch = new BatchScheduleResponse
                {
                    BatchNumber = row.BatchNumber,
                    StartTime = row.StartTime,
                    EndTime = row.EndTime
                };

                if (!existingProductionLine.BatchSchedules.Any(b => b.BatchNumber == batch.BatchNumber))
                {
                    existingProductionLine.BatchSchedules.Add(batch);
                }
            }
        }

        return Result.Success<IReadOnlyList<ProductionLineResponse>>(productionLineDictionary.Values.ToList());
    }
}