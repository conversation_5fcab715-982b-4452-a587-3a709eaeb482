using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Production;
using Tebipaints.Domain.Production.Enums;
using Tebipaints.Domain.Production.Errors;
using Tebipaints.Domain.Production.Repositories;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Production.ProductionLines.AddProductionLine;

public sealed class AddProductionLineCommandHandler : ICommandHandler<AddProductionLineCommand, Guid>
{
    private readonly IProductionLineRepository _productionLineRepository;
    private readonly IUnitOfWork _unitOfWork;

    public AddProductionLineCommandHandler(
        IProductionLineRepository productionLineRepository,
        IUnitOfWork unitOfWork)
    {
        _productionLineRepository = productionLineRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<Guid>> Handle(
        AddProductionLineCommand command,
        CancellationToken cancellationToken)
    {
        var capabilities = new List<ProductionCapability>();

        foreach (var commandCapability in command.Capabilities)
        {
            if (!Enum.TryParse<ProductType>(commandCapability.ProductType, out var productType))
            {
                return Result.Failure<Guid>(ProductionLineErrors.UnsupportedProductType(productType));
            }

            var prodCapability = ProductionCapability.Create(
                productType,
                new Measurement(commandCapability.MinBatchSize,
                    UnitOfMeasureConverter.FromString(commandCapability.Units).Value),
                new Measurement(commandCapability.MaxBatchSize, UnitOfMeasureConverter.FromString(commandCapability.Units).Value));

            if (prodCapability.IsFailure)
            {
                return Result.Failure<Guid>(prodCapability.Error);
            }
            
            capabilities.Add(prodCapability.Value);
        }
        
        var productionLine = ProductionLine.Create(
            command.Name,
            command.Description,
            capabilities);

        if (productionLine.IsFailure)
        {
            return Result.Failure<Guid>(productionLine.Error);
        }

        _productionLineRepository.Add(
            productionLine.Value);

        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success(productionLine.Value.Id);
    }
}