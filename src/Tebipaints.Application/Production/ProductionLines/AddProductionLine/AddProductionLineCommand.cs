using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Production.Enums;

namespace Tebipaints.Application.Production.ProductionLines.AddProductionLine;

public record AddProductionLineCommand(
    string Name,
    string Description,
    List<ProductionCapabilityDto> Capabilities) : ICommand<Guid>;
    
public sealed record ProductionCapabilityDto(
    string ProductType,
    decimal MinBatchSize,
    decimal MaxBatchSize,
    string Units);