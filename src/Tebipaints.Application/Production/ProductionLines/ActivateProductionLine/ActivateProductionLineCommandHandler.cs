using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Production.Errors;
using Tebipaints.Domain.Production.Repositories;

namespace Tebipaints.Application.Production.ProductionLines.ActivateProductionLine;

public class ActivateProductionLineCommandHandler : ICommandHandler<ActivateProductionLineCommand>
{
    private readonly IProductionLineRepository _productionLineRepository;
    private readonly IUnitOfWork _unitOfWork;

    public ActivateProductionLineCommandHandler(
        IProductionLineRepository productionLineRepository,
        IUnitOfWork unitOfWork)
    {
        _productionLineRepository = productionLineRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(
        ActivateProductionLineCommand command,
        CancellationToken cancellationToken)
    {
        var productionLine = await _productionLineRepository
            .GetByIdAsync(command.ProductionLineId, cancellationToken);

        if (productionLine is null)
        {
            return Result.Failure(ProductionLineErrors.NotFound(command.ProductionLineId));
        }

        var result = productionLine.Activate();

        if (result.IsFailure)
        {
            return result;
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}