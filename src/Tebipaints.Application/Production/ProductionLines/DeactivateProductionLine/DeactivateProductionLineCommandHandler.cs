using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Production.Errors;
using Tebipaints.Domain.Production.Repositories;

namespace Tebipaints.Application.Production.ProductionLines.DeactivateProductionLine;

public class DeactivateProductionLineCommandHandler : ICommandHandler<DeactivateProductionLineCommand>
{
    private readonly IProductionLineRepository _productionLineRepository;
    private readonly IUnitOfWork _unitOfWork;

    public DeactivateProductionLineCommandHandler(
        IProductionLineRepository productionLineRepository,
        IUnitOfWork unitOfWork)
    {
        _productionLineRepository = productionLineRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(
        DeactivateProductionLineCommand command,
        CancellationToken cancellationToken)
    {
        var productionLine = await _productionLineRepository
            .GetByIdAsync(command.ProductionLineId, cancellationToken);

        if (productionLine is null)
        {
            return Result.Failure(ProductionLineErrors.NotFound(command.ProductionLineId));
        }

        var result = productionLine.Deactivate(command.Reason);

        if (result.IsFailure)
        {
            return result;
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}