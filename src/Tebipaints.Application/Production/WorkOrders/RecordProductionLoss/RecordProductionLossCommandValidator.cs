using FluentValidation;

namespace Tebipaints.Application.Production.WorkOrders.RecordProductionLoss;

public class RecordProductionLossCommandValidator : AbstractValidator<RecordProductionLossCommand>
{
    public RecordProductionLossCommandValidator()
    {
        RuleFor(b => b.WorkOrderId).NotEmpty();
        RuleFor(b => b.BatchId).NotEmpty();
        RuleFor(b => b.Quantity).GreaterThan(0);
        RuleFor(b => b.LossType).NotEmpty();
        RuleFor(b => b.Reason).NotEmpty();
    }
}