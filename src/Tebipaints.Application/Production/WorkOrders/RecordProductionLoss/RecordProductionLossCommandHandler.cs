using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Production.Enums;
using Tebipaints.Domain.Production.Errors;
using Tebipaints.Domain.Production.Repositories;

namespace Tebipaints.Application.Production.WorkOrders.RecordProductionLoss;

public class RecordProductionLossCommandHandler : ICommandHandler<RecordProductionLossCommand>
{
    private readonly IWorkOrderRepository _workOrderRepository;
    private readonly IUnitOfWork _unitOfWork;

    public RecordProductionLossCommandHandler(IWorkOrderRepository workOrderRepository, IUnitOfWork unitOfWork)
    {
        _workOrderRepository = workOrderRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(
        RecordProductionLossCommand request,
        CancellationToken cancellationToken)
    {
        var workOrder = await _workOrderRepository.GetByIdAsync(
            request.WorkOrderId,
            cancellationToken);

        if (workOrder is null)
        {
            return Result.Failure(ProductionErrors.WorkOrderNotFound(request.WorkOrderId));
        }

        var batch = workOrder.Batches.FirstOrDefault(b => 
            b.Id == request.BatchId);

        if (batch is null)
        {
            return Result.Failure(ProductionErrors.BatchNotFound(request.BatchId));
        }

        if (!Enum.TryParse<ProductionLossType>(request.LossType, true, out var lossType))
        {
            return Result.Failure(ProductionErrors.InvalidLossType(request.LossType));
        }

        var result = batch.RecordProductionLoss(
            request.Quantity,
            lossType,
            request.Reason);

        if (result.IsFailure)
        {
            return Result.Failure(result.Error);
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}