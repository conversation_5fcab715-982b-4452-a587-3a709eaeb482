using MediatR;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.Production.Events;
using Tebipaints.Domain.Production.Repositories;
using Tebipaints.Domain.Production.Services;

namespace Tebipaints.Application.Production.WorkOrders.CreateWorkOrder;

internal sealed class WorkOrderCreatedBatchSchedulingHandler : INotificationHandler<WorkOrderCreatedDomainEvent>
{
    private readonly IWorkOrderRepository _workOrderRepository;
    private readonly IProductRepository _productRepository;
    private readonly BatchSchedulingService _batchSchedulingService;
    private readonly IUnitOfWork _unitOfWork;

    public WorkOrderCreatedBatchSchedulingHandler(
        IWorkOrderRepository workOrderRepository,
        IProductRepository productRepository,
        IUnitOfWork unitOfWork,
        BatchSchedulingService batchSchedulingService)
    {
        _workOrderRepository = workOrderRepository;
        _productRepository = productRepository;
        _unitOfWork = unitOfWork;
        _batchSchedulingService = batchSchedulingService;
    }

    public async Task Handle(WorkOrderCreatedDomainEvent notification, CancellationToken cancellationToken)
    {
        // 1. Get the work order
        var workOrder = await _workOrderRepository.GetByIdAsync(notification.WorkOrderId, cancellationToken);
        if (workOrder == null)
        {
            throw new ApplicationException("The work order does not exist.");
        }
        
        // 2. Get the product type for scheduling
        var product = await _productRepository.GetByIdAsync(notification.ProductId, cancellationToken);
        if (product == null)
        {
            throw new ApplicationException("The product does not exist.");
        }
        
        // 3. Schedule batches on production lines
        var scheduleResult = await _batchSchedulingService.ScheduleBatches(
            workOrder,
            product.Type,
            cancellationToken);

        if (scheduleResult.IsFailure)
        {
            throw new ApplicationException(scheduleResult.Error.Name);
        }

        // 4. Save batch schedules
        foreach (var schedule in scheduleResult.Value)
        {
            
        }
        
        await _unitOfWork.SaveChangesAsync(cancellationToken);
    }
}