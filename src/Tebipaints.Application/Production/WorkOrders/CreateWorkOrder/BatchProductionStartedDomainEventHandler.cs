using MediatR;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Inventory;
using Tebipaints.Domain.Material;
using Tebipaints.Domain.Production.Events;
using Tebipaints.Domain.Production.Repositories;
using Tebipaints.Domain.Production.Services;

namespace Tebipaints.Application.Production.WorkOrders.CreateWorkOrder;

public sealed class BatchProductionStartedDomainEventHandler : INotificationHandler<BatchProductionStartedDomainEvent>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IBatchRepository _batchRepository;
    private readonly ProductionInventoryService _productionInventoryService;
    private readonly IMaterialInventoryRepository _materialInventoryRepository;

    public BatchProductionStartedDomainEventHandler(
        IUnitOfWork unitOfWork,
        IBatchRepository batchRepository,
        ProductionInventoryService productionInventoryService,
        IMaterialInventoryRepository materialInventoryRepository)
    {
        _unitOfWork = unitOfWork;
        _batchRepository = batchRepository;
        _productionInventoryService = productionInventoryService;
        _materialInventoryRepository = materialInventoryRepository;
    }

    public async Task Handle(BatchProductionStartedDomainEvent notification, CancellationToken cancellationToken)
    {
        // Get batch
        var batch = await _batchRepository.GetByIdAsync(notification.BatchId, cancellationToken);
        if (batch == null)
        {
            throw new ApplicationException($"Batch {notification.BatchId} does not exist");
        }
        
        // Get all required material inventories
        var materialIds = batch.Ingredients.Select(m => m.MaterialId).ToList();
        
        var materialInventories = await _materialInventoryRepository
            .GetMaterialInventoriesAsync(materialIds);
        
        // Consume materials as production starts
        var consumptionResult = _productionInventoryService
            .ConsumeMaterials(
                batch, materialInventories, cancellationToken);

        if (consumptionResult.IsFailure)
        {
            throw new InvalidOperationException(
                $"Failed to consume materials for batch {notification.BatchNumber}: {consumptionResult.Error}");
        }
    }
}