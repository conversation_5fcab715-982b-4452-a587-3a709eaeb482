using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation;
using Tebipaints.Domain.Formulation.Errors;
using Tebipaints.Domain.Material;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.Product.Errors;
using Tebipaints.Domain.Production;
using Tebipaints.Domain.Production.Errors;
using Tebipaints.Domain.Production.Repositories;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Production.WorkOrders.CreateWorkOrder;

public class CreateWorkOrderCommandHandler: ICommandHandler<CreateWorkOrderCommand, WorkOrder>
{
    private readonly IProductRepository _productRepository;
    private readonly IProductionLineRepository _productionLineRepository;
    private readonly IFormulationRepository _formulationRepository;
    private readonly IMaterialRepository _materialRepository;
    private readonly IWorkOrderRepository _workOrderRepository;
    private readonly IDocumentNumberGenerator _documentNumberGenerator;
    private readonly IUnitOfWork _unitOfWork;

    public CreateWorkOrderCommandHandler(IProductRepository productRepository,
        IUnitOfWork unitOfWork,
        IWorkOrderRepository workOrderRepository,
        IFormulationRepository formulationRepository,
        IMaterialRepository materialRepository,
        IProductionLineRepository productionLineRepository,
        IDocumentNumberGenerator documentNumberGenerator)
    {
        _productRepository = productRepository;
        _unitOfWork = unitOfWork;
        _workOrderRepository = workOrderRepository;
        _formulationRepository = formulationRepository;
        _materialRepository = materialRepository;
        _productionLineRepository = productionLineRepository;
        _documentNumberGenerator = documentNumberGenerator;
    }

    public async Task<Result<WorkOrder>> Handle(CreateWorkOrderCommand request, CancellationToken cancellationToken)
    {
        // Get product and formulation details
        var product = await _productRepository.GetByIdAsync(request.ProductId, cancellationToken);
        if (product == null)
        {
            return Result.Failure<WorkOrder>(ProductErrors.InvalidProductId());
        }

        if (product.FormulationId is null)
        {
            return Result.Failure<WorkOrder>(ProductErrors.InvalidFormulationId());
        }

        var formulation = await _formulationRepository.GetByIdAsync(product.FormulationId.Value, cancellationToken);
        if (formulation == null)
        {
            return Result.Failure<WorkOrder>(FormulationErrors.FormulationNotFound());
        }

        // Validate that formulation is approved for production use
        if (formulation.Status != Domain.Formulation.Enums.FormulationStatus.Approved)
        {
            return Result.Failure<WorkOrder>(FormulationErrors.ApprovalRequired());
        }
        
        var materialsDetails = await _materialRepository.GetMaterialDetailsAsync(formulation.Ingredients.ToList());
        if (materialsDetails == null)
        {
            return Result.Failure<WorkOrder>(WorkOrderErrors.MaterialsNotFound());
        }

        var productType = product.Type;
        
        // Get the default production line for this product type
        var productionLine = await _productionLineRepository
            .GetDefaultLineForProductTypeAsync(productType, cancellationToken);
        
        if (productionLine == null)
            return Result.Failure<WorkOrder>(ProductionLineErrors.NoSuitableProductionLine());
        
        // Find the specific capability for this product type
        var capability = productionLine.Capabilities
            .FirstOrDefault(c => c.ProductType == productType);
        
        if (capability == null)
            return Result.Failure<WorkOrder>(ProductionLineErrors.ProductTypeNotSupported(productType));
        
        // Use the capability's max batch size
        var maxBatchSize = capability.MaxBatchSize;
        
        // create formulation details
        var unitOfMeasure = UnitOfMeasureConverter.FromString(request.Units);
        var measurementValue = (decimal)request.TotalQuantity;
        var formulationDetails = new FormulationDetails(
            formulation.Id,
            maxBatchSize,
            TimeSpan.FromHours(formulation.EstimatedProductionTime.Hours),
            formulation.CalculateIngredientQuantities(new Measurement(measurementValue, unitOfMeasure.Value)).Value,
            materialsDetails);
        
        // create work order
        var workOrderNumber = await _documentNumberGenerator.GetNextWorkOrderNumberAsync(cancellationToken);

        if (!Enum.TryParse<PackagingType>(request.PackagingType, true, out var packagingType))
        {
            return Result.Failure<WorkOrder>(WorkOrderErrors.InvalidPackagingType());
        }
        
        var workOrder = WorkOrder.Create(
            workOrderNumber.ToString(), // TODO: implement work order number generation
            request.ProductId,
            request.VariantId,
            packagingType,
            new Measurement(request.PackagingCapacity, UnitOfMeasureConverter.FromString(request.PackagingCapacityUnit).Value),
            request.FormulationId,
            new Measurement(measurementValue, unitOfMeasure.Value),
            request.DueDate,
            request.PlannedStartDate,
            formulationDetails);

        if (workOrder.IsFailure)
        {
            return Result.Failure<WorkOrder>(workOrder.Error);
        }
        
        _workOrderRepository.Add(workOrder.Value);
        
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        return Result.Success<WorkOrder>(workOrder.Value);
    }
}