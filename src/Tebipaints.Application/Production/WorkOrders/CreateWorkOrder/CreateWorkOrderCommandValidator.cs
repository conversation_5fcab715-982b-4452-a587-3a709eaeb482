using FluentValidation;

namespace Tebipaints.Application.Production.WorkOrders.CreateWorkOrder;

internal class CreateWorkOrderCommandValidator : AbstractValidator<CreateWorkOrderCommand>
{
    public CreateWorkOrderCommandValidator()
    {
        RuleFor(w => w.FormulationId).NotEmpty();
        RuleFor(w => w.ProductId).NotEmpty();
        RuleFor(w => w.DueDate).NotEmpty();
        RuleFor(w => w.TotalQuantity).NotEmpty().GreaterThan(0);
        RuleFor(w => w.PlannedStartDate).NotEmpty();
    }
}