using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Production;

namespace Tebipaints.Application.Production.WorkOrders.CreateWorkOrder;

public record CreateWorkOrderCommand(
    Guid ProductId,
    Guid FormulationId,
    Guid VariantId,
    string PackagingType,
    decimal PackagingCapacity,
    string PackagingCapacityUnit,
    double TotalQuantity,
    string Units,
    DateTime DueDate,
    DateTime PlannedStartDate
    ) : ICommand<WorkOrder>;