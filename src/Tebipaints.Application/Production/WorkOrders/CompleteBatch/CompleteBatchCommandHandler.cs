using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Production.Errors;
using Tebipaints.Domain.Production.Repositories;

namespace Tebipaints.Application.Production.WorkOrders.CompleteBatch;

public class CompleteBatchCommandHandler : ICommandHandler<CompleteBatchCommand>
{
    private readonly IWorkOrderRepository _workOrderRepository;
    private readonly IUnitOfWork _unitOfWork;

    public CompleteBatchCommandHandler(IWorkOrderRepository workOrderRepository, IUnitOfWork unitOfWork)
    {
        _workOrderRepository = workOrderRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(CompleteBatchCommand request, CancellationToken cancellationToken)
    {
        var workOrder = await _workOrderRepository.GetByIdAsync(
            request.WorkOrderId,
            cancellationToken);

        if (workOrder is null)
        {
            return Result.Failure(ProductionErrors.WorkOrderNotFound(request.WorkOrderId));
        }

        var batch = workOrder.Batches.FirstOrDefault(b => 
            b.Id == request.BatchId);

        if (batch is null)
        {
            return Result.Failure(ProductionErrors.BatchNotFound(request.BatchId));
        }

        var result = batch.CompleteProduction(request.ActualYield);
        if (result.IsFailure)
        {
            return Result.Failure(result.Error);
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}