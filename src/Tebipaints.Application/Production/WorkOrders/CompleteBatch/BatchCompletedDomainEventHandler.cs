using MediatR;
using Microsoft.Extensions.Logging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Inventory;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.Production.Events;
using Tebipaints.Domain.Production.Repositories;
using Tebipaints.Domain.Production.Services;

namespace Tebipaints.Application.Production.WorkOrders.CompleteBatch;

public class BatchCompletedDomainEventHandler : INotificationHandler<BatchCompletedDomainEvent>
{
    private readonly IWorkOrderRepository _workOrderRepository;
    private readonly IProductInventoryRepository _productInventoryRepository;
    private readonly IProductRepository _productRepository;
    private readonly ProductionInventoryService _inventoryService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<BatchCompletedDomainEventHandler> _logger;

    public BatchCompletedDomainEventHandler(IWorkOrderRepository workOrderRepository, IUnitOfWork unitOfWork, ILogger<BatchCompletedDomainEventHandler> logger, IProductInventoryRepository productInventoryRepository, ProductionInventoryService inventoryService, IProductRepository productRepository)
    {
        _workOrderRepository = workOrderRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _productInventoryRepository = productInventoryRepository;
        _inventoryService = inventoryService;
        _productRepository = productRepository;
    }

    public async Task Handle(BatchCompletedDomainEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            // 1. Get the work order
            var workOrder = await _workOrderRepository.GetByIdAsync(
                notification.WorkOrderId,
                cancellationToken);

            if (workOrder is null)
            {
                _logger.LogError(
                    "Work order {WorkOrderId} not found for batch completion",
                    notification.WorkOrderId);
                return;
            }

            // 2. Get the completed batch
            var completedBatch = workOrder.Batches
                .FirstOrDefault(b => b.BatchNumber == notification.BatchNumber);

            if (completedBatch is null)
            {
                _logger.LogError(
                    "Batch {BatchNumber} not found in work order {WorkOrderNumber}",
                    notification.BatchNumber,
                    workOrder.WorkOrderNumber);
                return;
            }
            
            // 3. Get the product and validate variant/packaging
            var product = await _productRepository.GetByIdWithVariantAsync(workOrder.ProductId, cancellationToken);
            if (product is null)
            {
                _logger.LogError(
                    "Product {ProductId} not found",
                    workOrder.ProductId);
                return;
            }
            
            var variant = product.Variants.FirstOrDefault(v => v.Id == workOrder.VariantId);
            if (variant is null)
            {
                _logger.LogError(
                    "Variant {VariantId} not found for product {ProductId}",
                    workOrder.VariantId,
                    workOrder.ProductId);
                return;
            }
            
            var packagingOption = variant.PackagingOptions
                .FirstOrDefault(p => 
                    p.Type == workOrder.PackagingType && 
                    p.Capacity == workOrder.PackagingCapacity);

            if (packagingOption is null)
            {
                _logger.LogError(
                    "Packaging option not found for variant {VariantId} with type {PackagingType} and capacity {Capacity}",
                    variant.Id,
                    workOrder.PackagingType,
                    workOrder.PackagingCapacity);
                return;
            }
            
            // 4. Get, create or record production in inventory
            var productInventory = await _productInventoryRepository
                .GetByIdAsync(workOrder.ProductId, cancellationToken);

            if (productInventory is null)
            {
                _logger.LogError(
                    "Product inventory not found for product {ProductId}",
                    workOrder.ProductId);

                var createResult = ProductInventory.Create(
                    workOrder.ProductId,
                    InventoryLocation.Factory,
                    0,
                    0);
                
                if (createResult.IsFailure)
                {
                    _logger.LogError(
                        "Failed to create product inventory for product {ProductId}: {Error}",
                        workOrder.ProductId,
                        createResult.Error);
                    return;
                }
                
                productInventory = createResult.Value;
                _productInventoryRepository.Add(productInventory);
            }
            
            var recordResult = _inventoryService.RecordProduction(
                completedBatch,
                productInventory,
                variant.Id,
                packagingOption,
                cancellationToken);

            if (recordResult.IsFailure)
            {
                _logger.LogError(
                    "Failed to record production for batch {BatchNumber}: {Error}",
                    completedBatch.BatchNumber,
                    recordResult.Error);
                return;
            }

            // 5. Start the next batch if available
            var currentBatchIndex = workOrder.Batches
                .ToList()
                .FindIndex(b => b.BatchNumber == notification.BatchNumber);

            if (currentBatchIndex != -1 && currentBatchIndex < workOrder.Batches.Count - 1)
            {
                var nextBatch = workOrder.Batches.ElementAt(currentBatchIndex + 1);
                var startResult = nextBatch.StartProduction();

                if (startResult.IsFailure)
                {
                    _logger.LogError(
                        "Failed to start next batch {BatchNumber}: {Error}",
                        nextBatch.BatchNumber,
                        startResult.Error);
                }
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation(
                "Successfully processed batch completion for {BatchNumber}",
                completedBatch.BatchNumber);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error processing batch completion for {BatchNumber}",
                notification.BatchNumber);
            throw;
        }
    }
}