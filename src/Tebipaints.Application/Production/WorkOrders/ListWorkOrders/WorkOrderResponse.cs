namespace Tebipaints.Application.Production.WorkOrders.ListWorkOrders;

public class WorkOrderResponse
{
    public Guid Id { get; init; }
    public string WorkOrderNumber { get; init; }
    public string ProductName { get; init; }
    public decimal TotalBatchSize { get; init; }
    public string Status { get; init; }
    public string FormulationName { get; init; }
    public DateTime ProductionDate { get; init; }
    public int NumberOfBatches { get; init; }
    public ProductionReportResponse ProductionReport { get; init; }
    public List<BillOfMaterialResponse> BillOfMaterials { get; init; }
    public List<BatchResponse> Batches { get; init; }
}

public class BillOfMaterialResponse
{
    public Guid WorkOrderId { get; init; }
    List<ComponentResponse> Components { get; init; }
}

public class ComponentResponse
{
    public Guid Id { get; init; }
    public string Name { get; init; }
    public decimal Quantity { get; init; }
    public string Unit { get; init; }
}

public class ProductionReportResponse
{
    public Guid Id { get; init; }
    public decimal ActualQuantityProduced { get; init; }
    public decimal PlannedQuantity { get; init; }
    public decimal QuantityRejected { get; init; }
    public decimal Deviation { get; init; }
}

public class BatchResponse
{
    public Guid Id { get; init; }
    public string BatchNumber { get; init; }
    public DateTime PlannedStartDate { get; init; }
    public TimeSpan EstimatedDuration { get; init; }
    public string Status { get; init; }
}