using System.Text.Json;
using System.Text.Json.Serialization;
using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Application.Production.WorkOrders.ListWorkOrders;

public class ListWorkOrdersQueryHandler : IQueryHandler<ListWorkOrdersQuery, IReadOnlyList<WorkOrderResponse>>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;

    public ListWorkOrdersQueryHandler(ISqlConnectionFactory sqlConnectionFactory)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
    }

    public async Task<Result<IReadOnlyList<WorkOrderResponse>>> Handle(ListWorkOrdersQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();

        const string sql = """
                    SELECT
                        wo.id,
                        wo.work_order_number,
                        p.name_value AS product_name,
                        wo.status,
                        f.name AS formulation_name,
                        wo.start_date AS production_date,
                        wo.total_quantity_value AS total_batch_size,
                        COUNT(wob.id) AS number_of_batches,
                        -- Production report fields
                        COALESCE(SUM(wob.actual_yield), 0)::decimal(10,2) AS actual_quantity_produced,
                        wo.total_quantity_value::decimal(10,2) AS planned_quantity,
                        COALESCE(SUM(wob.target_quantity_value - wob.actual_yield), 0)::decimal(10,2) AS quantity_rejected,
                        COALESCE(SUM(wob.actual_yield - wob.target_quantity_value), 0)::decimal(10,2) AS deviation,
                        -- JSON array of batches
                    	(
                    		SELECT json_agg(json_build_object(
                    		    'id', b.id,
                    			'batchNumber', b.batch_number,
                    			'plannedStartDate', b.planned_start_date,
                    			'estimatedDuration', b.estimated_duration,
                    			'status', b.status
                    		))
                    		FROM work_order_batches b
                    		WHERE b.work_order_id = wo.id
                    	) AS batches,
                        -- Bill of Materials as JSON
                        (
                            SELECT jsonb_agg(
                                jsonb_build_object(
                                    'WorkOrderId', bom.work_order_id::text,
                                    'Components', (
                                        SELECT jsonb_agg(
                                            jsonb_build_object(
                                                'Id', m.id::text,
                                                'Name', m.name,
                                                'Quantity', bom.required_quantity_value::decimal(10,2),
                                                'Unit', bom.required_quantity_unit
                                            )
                                        )
                                        FROM materials m
                                        WHERE m.id = bom.material_id
                                    )
                                )
                            )
                            FROM bill_of_materials bom
                            WHERE bom.work_order_id = wo.id
                        ) AS bill_of_materials
                    FROM work_orders wo
                    JOIN products p ON p.id = wo.product_id
                    JOIN formulations f ON f.id = wo.formulation_id
                    LEFT JOIN work_order_batches wob ON wob.work_order_id = wo.id
                    WHERE DATE_TRUNC('month', wo.start_date) = DATE_TRUNC('month', CURRENT_DATE)
                    GROUP BY
                        wo.id, wo.work_order_number, p.name_value, wo.status, f.name, wo.start_date, wo.total_quantity_value
                    ORDER BY wo.start_date DESC
                    LIMIT 100
                    """;

        var rows = await connection.QueryAsync(sql);

        var result = new List<WorkOrderResponse>();
        foreach (var row in rows)
        {
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                NumberHandling = JsonNumberHandling.AllowReadingFromString
            };
            
            // Parse BillOfMaterials JSON
            var billOfMaterials = new List<BillOfMaterialResponse>();
            if (row.bill_of_materials != null)
            {
                var bomJson = row.bill_of_materials.ToString();
                var bomArray = JsonSerializer.Deserialize<List<BillOfMaterialResponse>>(bomJson, options);
                if (bomArray != null)
                    billOfMaterials = bomArray;
            }

            // Parse Batches
            var batches = new List<BatchResponse>();
            if (row.batches != null)
            {
                var batchesJson = row.batches.ToString();
                var batchArray = JsonSerializer.Deserialize<List<BatchResponse>>(batchesJson, options);
                if (batchArray != null)
                    batches = batchArray;
            }

            var productionReport = new ProductionReportResponse
            {
                Id = row.id,
                ActualQuantityProduced = Convert.ToDecimal(row.actual_quantity_produced),
                PlannedQuantity = Convert.ToDecimal(row.planned_quantity),
                QuantityRejected = Convert.ToDecimal(row.quantity_rejected),
                Deviation = Convert.ToDecimal(row.deviation)
            };

            result.Add(new WorkOrderResponse
            {
                Id = row.id,
                WorkOrderNumber = row.work_order_number,
                ProductName = row.product_name,
                TotalBatchSize = Convert.ToDecimal(row.total_batch_size),
                Status = row.status,
                FormulationName = row.formulation_name,
                ProductionDate = row.production_date,
                NumberOfBatches = Convert.ToInt32(row.number_of_batches),
                ProductionReport = productionReport,
                BillOfMaterials = billOfMaterials,
                Batches = batches
            });
        }

        return Result.Success<IReadOnlyList<WorkOrderResponse>>(result);
    }
}