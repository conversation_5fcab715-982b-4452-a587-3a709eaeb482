using FluentValidation;

namespace Tebipaints.Application.Production.WorkOrders.RecordBatchQualityCheck;

public class RecordBatchQualityCheckCommandValidator : AbstractValidator<RecordBatchQualityCheckCommand>
{
    public RecordBatchQualityCheckCommandValidator()
    {
        RuleFor(b => b.WorkOrderId).NotEmpty();
        RuleFor(b => b.BatchId).NotEmpty();
        RuleFor(b => b.Parameter).NotEmpty();
        RuleFor(b => b.MeasuredValue).NotEmpty();
        RuleFor(b => b.MinimumValue).NotEmpty();
        RuleFor(b => b.MaximumValue).NotEmpty();
        RuleFor(b => b.CheckedBy).NotEmpty();
    }
}