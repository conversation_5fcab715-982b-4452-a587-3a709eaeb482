using MediatR;
using Microsoft.Extensions.Logging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Inventory;
using Tebipaints.Domain.Production.Events;
using Tebipaints.Domain.Production.Repositories;
using Tebipaints.Domain.Production.Services;

namespace Tebipaints.Application.Production.WorkOrders.StartWorkOrder;

public class WorkOrderStartedDomainHandler : INotificationHandler<WorkOrderStartedDomainEvent>
{
    private readonly IWorkOrderRepository _workOrderRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<WorkOrderStartedDomainHandler> _logger;

    public WorkOrderStartedDomainHandler(IWorkOrderRepository workOrderRepository,
        IUnitOfWork unitOfWork,
        ILogger<WorkOrderStartedDomainHandler> logger)
    {
        _workOrderRepository = workOrderRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task Handle(WorkOrderStartedDomainEvent notification, CancellationToken cancellationToken)
    {
        try
        {
            // 1. Get the work order
            var workOrder = await _workOrderRepository.GetByIdAsync(notification.WorkOrderId, cancellationToken);
            if (workOrder == null)
            {
                throw new InvalidOperationException($"Work order with id {notification.WorkOrderId} could not be found.");
            }
            
            // 4. Start the first batch
            var firstBatch = workOrder.Batches.First();
            var startResult = firstBatch.StartProduction();

            if (startResult.IsFailure)
            {
                _logger.LogError(
                    "Failed to start batch {BatchNumber}: {Error}",
                    firstBatch.BatchNumber,
                    startResult.Error);
            }
            
            await _unitOfWork.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation(
                "Successfully processed work order start for {WorkOrderNumber}",
                workOrder.WorkOrderNumber);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Error processing work order start for {WorkOrderId}",
                notification.WorkOrderId);
            throw;
        }
    }
}