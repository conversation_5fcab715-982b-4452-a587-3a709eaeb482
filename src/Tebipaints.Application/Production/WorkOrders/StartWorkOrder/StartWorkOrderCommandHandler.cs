using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Production.Errors;
using Tebipaints.Domain.Production.Repositories;

namespace Tebipaints.Application.Production.WorkOrders.StartWorkOrder;

public class StartWorkOrderCommandHandler : ICommandHandler<StartWorkOrderCommand>
{
    private readonly IWorkOrderRepository _workOrderRepository;
    private readonly IUnitOfWork _unitOfWork;

    public StartWorkOrderCommandHandler(IWorkOrderRepository workOrderRepository, IUnitOfWork unitOfWork)
    {
        _workOrderRepository = workOrderRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(
        StartWorkOrderCommand request,
        CancellationToken cancellationToken)
    {
        var workOrder = await _workOrderRepository.GetByIdAsync(
            request.WorkOrderId,
            cancellationToken);

        if (workOrder is null)
        {
            return Result.Failure(ProductionErrors.WorkOrderNotFound(request.WorkOrderId));
        }

        var result = workOrder.Start();
        if (result.IsFailure)
        {
            return Result.Failure(result.Error);
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}