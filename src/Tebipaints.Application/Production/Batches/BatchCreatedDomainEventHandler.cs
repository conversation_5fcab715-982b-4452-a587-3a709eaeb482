using MediatR;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Inventory;
using Tebipaints.Domain.Production.Events;
using Tebipaints.Domain.Production.Repositories;
using Tebipaints.Domain.Production.Services;

namespace Tebipaints.Application.Production.Batches;

internal sealed class BatchCreatedDomainEventHandler : INotificationHandler<BatchCreatedDomainEvent>
{
    private readonly ProductionInventoryService _inventoryService;
    private readonly IMaterialInventoryRepository _materialInventoryRepository;
    private readonly IBatchRepository _batchRepository;
    private readonly IUnitOfWork _unitOfWork;

    public BatchCreatedDomainEventHandler(
        ProductionInventoryService inventoryService,
        IMaterialInventoryRepository materialInventoryRepository,
        IBatchRepository batchRepository, IUnitOfWork unitOfWork)
    {
        _inventoryService = inventoryService;
        _materialInventoryRepository = materialInventoryRepository;
        _batchRepository = batchRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task Handle(BatchCreatedDomainEvent notification, CancellationToken cancellationToken)
    {
        // Get all required material inventories
        var materialIds = notification.Batch.Ingredients.Select(i => i.MaterialId).ToList();
        
        var materialInventories = await _materialInventoryRepository
            .GetMaterialInventoriesAsync(materialIds); 
        
        // Attempt to reserve materials
        var batch = notification.Batch;
        var reservationResult = _inventoryService
            .ReserveMaterialsForBatch(
                batch, materialInventories, cancellationToken);

        // Save changes
        if (reservationResult.IsFailure)
        {
            // If reservation fails, mark the batch as MaterialShortage
            var dbBatch = await _batchRepository.GetByIdAsync(
                batch.Id, cancellationToken);

            if (dbBatch is not null)
            {
                dbBatch.MarkMaterialShortage(reservationResult.Error.Name);
                await _unitOfWork.SaveChangesAsync(cancellationToken);
            }
        }
    }
}