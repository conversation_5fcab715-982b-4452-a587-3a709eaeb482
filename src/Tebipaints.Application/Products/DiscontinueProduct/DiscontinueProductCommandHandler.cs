using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.Product.Errors;

namespace Tebipaints.Application.Products.DiscontinueProduct;

public class DiscontinueProductCommandHandler
{
    private readonly IProductRepository _productRepository;
    private readonly IUnitOfWork _unitOfWork;

    public DiscontinueProductCommandHandler(
        IProductRepository productRepository,
        IUnitOfWork unitOfWork)
    {
        _productRepository = productRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(
        DiscontinueProductCommand command,
        CancellationToken cancellationToken)
    {
        var product = await _productRepository
            .GetByIdAsync(command.ProductId, cancellationToken);

        if (product is null)
        {
            return Result.Failure(ProductErrors.NotFound(command.ProductId));
        }

        var result = product.Discontinue();
        if (result.IsFailure)
        {
            return result;
        }

        _productRepository.Update(product, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}