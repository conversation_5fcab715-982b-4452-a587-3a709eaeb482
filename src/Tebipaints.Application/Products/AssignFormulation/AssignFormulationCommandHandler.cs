using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.Product.Errors;

namespace Tebipaints.Application.Products.AssignFormulation;

public class AssignFormulationCommandHandler : ICommandHandler<AssignFormulationCommand>
{
    private readonly IProductRepository _productRepository;
    private readonly IFormulationRepository _formulationRepository;
    private readonly IUnitOfWork _unitOfWork;

    public AssignFormulationCommandHandler(
        IProductRepository productRepository,
        IFormulationRepository formulationRepository,
        IUnitOfWork unitOfWork)
    {
        _productRepository = productRepository;
        _formulationRepository = formulationRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(
        AssignFormulationCommand command,
        CancellationToken cancellationToken)
    {
        var product = await _productRepository
            .GetByIdAsync(command.ProductId, cancellationToken);

        if (product is null)
        {
            return Result.Failure(ProductErrors.NotFound(command.ProductId));
        }

        var formulation = await _formulationRepository
            .GetByIdAsync(command.FormulationId, cancellationToken);

        if (formulation is null)
        {
            return Result.Failure(ProductErrors.FormulationNotFound(command.FormulationId));
        }

        var result = product.AssignFormulation(command.FormulationId);
        if (result.IsFailure)
        {
            return result;
        }

        _productRepository.Update(product, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}