using FluentValidation;

namespace Tebipaints.Application.Products.CreateProduct;

internal class CreateProductCommandValidator : AbstractValidator<CreateProductCommand>
{
    public CreateProductCommandValidator()
    {
        RuleFor(p => p.ProductName).NotEmpty();
        RuleFor(p => p.Color).NotEmpty();
        RuleFor(p => p.Volume).NotEmpty().GreaterThan(0);
        RuleFor(p => p.ProductType).NotEmpty();
        RuleFor(p => p.Unit).NotEmpty();
    }
}