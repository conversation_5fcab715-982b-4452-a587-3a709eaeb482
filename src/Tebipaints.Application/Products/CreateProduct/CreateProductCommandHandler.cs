using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.Production.Enums;
using Tebipaints.Domain.Production.Repositories;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Products.CreateProduct;

internal sealed class CreateProductCommandHandler : ICommandHandler<CreateProductCommand,Guid>
{
    private readonly IProductRepository _productRepository;
    private readonly IDocumentNumberGenerator _documentNumberGenerator;
    private readonly IUnitOfWork _unitOfWork;

    public CreateProductCommandHandler(IUnitOfWork unitOfWork, IProductRepository productRepository, IDocumentNumberGenerator documentNumberGenerator)
    {
        _unitOfWork = unitOfWork;
        _productRepository = productRepository;
        _documentNumberGenerator = documentNumberGenerator;
    }

    public async Task<Result<Guid>> Handle(CreateProductCommand request, CancellationToken cancellationToken)
    {
        var productName = new ProductName(request.ProductName);
        var color = new Color(request.Color);
        var nextSequence = await _documentNumberGenerator.GetNextSkuAsync(cancellationToken);

        var volume = Volume.Create(
            request.Volume,
            VolumetricUnit.FromUnit(request.Unit)).Value;

        var sku = SKU.Create(
            request.Color,
            volume.ToString(),
            DateTime.UtcNow.Year,
            DateTime.UtcNow.Month,
            nextSequence).Value;
        
        ProductType productType = (ProductType)request.ProductType; 
        
        var product = Product.Create(
            productName,
            sku,
            color,
            volume,
            productType).Value;
        
        _productRepository.Add(product);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success(product.Id);
    }
}