using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Application.Products.ListProductsQuery;

public class ListProductsQueryHandler : I<PERSON>ueryHandler<ListProductsQuery, IReadOnlyList<ProductResponse>>
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;

    public ListProductsQueryHandler(ISqlConnectionFactory sqlConnectionFactory)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
    }

    public async Task<Result<IReadOnlyList<ProductResponse>>> Handle(ListProductsQuery request, CancellationToken cancellationToken)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();

        const string sql = @"
            SELECT
                id AS Id,
                formulation_id AS FormulationId,
                name_value AS Name,
                sku_value AS Sku,
                color_value AS Color,
                volume_value AS VolumeValue,
                volume_unit AS VolumeUnit,
                type AS Type,
                price_amount AS PriceAmount,
                price_currency AS PriceCurrency,
                created_on_utc AS CreatedOnUtc,
                status AS Status
            FROM products
            ORDER BY name_value
        ";

        var products = (await connection.QueryAsync<ProductResponse>(sql)).ToList();

        return Result.Success<IReadOnlyList<ProductResponse>>(products);
    }
}