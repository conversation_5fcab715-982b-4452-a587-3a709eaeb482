using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.Product.Errors;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Products.UpdatePrice;

public class UpdatePriceCommandHandler : ICommandHandler<UpdatePriceCommand, Guid>
{
    private readonly IProductRepository _productRepository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdatePriceCommandHandler(IProductRepository productRepository, IUnitOfWork unitOfWork)
    {
        _productRepository = productRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<Guid>> Handle(UpdatePriceCommand request, CancellationToken cancellationToken)
    {
        var product = await _productRepository.GetByIdAsync(request.ProductId, cancellationToken);

        if (product == null)
        {
            return Result.Failure<Guid>(ProductErrors.InvalidProductId());
        }

        var newPrice = new Money(request.Price, Currency.Ghs);
        product.UpdatePrice(newPrice);

        _productRepository.Update(product, cancellationToken);
        
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        return Result.Success(product.Id);
    }
}