using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Invoice;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Invoices.IssueRefund;

public class IssueRefundCommandHandler : ICommandHandler<IssueRefundCommand, Result>
{
    private readonly IInvoiceRepository _invoiceRepository;
    private readonly IUnitOfWork _unitOfWork;

    public IssueRefundCommandHandler(IInvoiceRepository invoiceRepository, IUnitOfWork unitOfWork)
    {
        _invoiceRepository = invoiceRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<Result>> Handle(IssueRefundCommand request, CancellationToken cancellationToken)
    {
        var invoice = await _invoiceRepository
            .GetByIdAsync(request.InvoiceId, cancellationToken);

        if (invoice is null)
        {
            return Result.Failure(
                InvoiceErrors.NotFound(request.InvoiceId));
        }

        var result = invoice.IssueRefund(
            new Money(request.Amount, Currency.Ghs),
            request.Reason,
            DateTime.UtcNow);

        if (result.IsFailure)
        {
            return Result.Failure(result.Error);
        }

        await _invoiceRepository.UpdateAsync(invoice, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}