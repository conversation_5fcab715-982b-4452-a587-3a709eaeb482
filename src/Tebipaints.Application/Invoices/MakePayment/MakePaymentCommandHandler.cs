using Tebipaints.Application.Abstractions.Messaging;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Invoice;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Application.Invoices.MakePayment;

public class MakePaymentCommandHandler : ICommandHandler<MakePaymentCommand, Guid>
{
    private readonly IInvoiceRepository _invoiceRepository;
    private readonly IUnitOfWork _unitOfWork;

    public MakePaymentCommandHandler(IInvoiceRepository invoiceRepository, IUnitOfWork unitOfWork)
    {
        _invoiceRepository = invoiceRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<Guid>> Handle(MakePaymentCommand request, CancellationToken cancellationToken)
    {
        var invoice = await _invoiceRepository
            .GetByIdAsync(request.InvoiceId, cancellationToken);

        if (invoice is null)
        {
            return Result.Failure<Guid>(
                InvoiceErrors.NotFound(request.InvoiceId));
        }

        if (!Enum.TryParse(request.PaymentMethod, true, out PaymentMethod paymentMethod))
        {
            return Result.Failure<Guid>(InvoiceErrors.NotAnAcceptablePaymentMethod());
        }

        var nextSequence = await _invoiceRepository.GetNextPaymentSequenceAsync(cancellationToken);
        
        var payment = Payment.Create(
            invoice.Id,
            new Money(request.Amount, Currency.Ghs),
            paymentMethod,
            request.Reference,
            DateTime.UtcNow);

        var result = invoice.MakePayment(payment.Value, nextSequence);

        if (result.IsFailure)
        {
            return Result.Failure<Guid>(result.Error);
        }

        await _invoiceRepository.UpdateAsync(invoice, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Result.Success(payment.Value.Id);
    }
}