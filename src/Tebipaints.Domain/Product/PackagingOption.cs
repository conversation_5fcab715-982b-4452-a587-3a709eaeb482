using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Product.Errors;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Product;

public sealed record PackagingOption
{
    private PackagingOption(){}
    
    private PackagingOption(
        PackagingType type,
        string material,
        Measurement capacity,
        Money price)
    {
        Type = type;
        Material = material;
        Capacity = capacity;
        Price = price;
        Status = PackagingStatus.Active;
    }

    public PackagingType Type { get; }
    public string Material { get; }
    public Measurement Capacity { get; }
    public Money Price { get; private set; }
    public PackagingStatus Status { get; private set; }
    
    public static Result<PackagingOption> Create(
        PackagingType type,
        string material,
        Measurement capacity,
        Money price)
    {
        if (string.IsNullOrWhiteSpace(material))
        {
            return Result.Failure<PackagingOption>(ProductErrors.InvalidPackagingMaterial());
        }

        if (capacity.Value <= 0)
        {
            return Result.Failure<PackagingOption>(ProductErrors.InvalidPackagingCapacity());
        }

        return Result.Success(new PackagingOption(
            type,
            material.Trim(),
            capacity,
            price));
    }

    public Result UpdatePrice(Money newPrice)
    {
        if (Status != PackagingStatus.Active)
        {
            return Result.Failure(ProductErrors.PackagingNotActive());
        }

        Price = newPrice;
        return Result.Success();
    }
}