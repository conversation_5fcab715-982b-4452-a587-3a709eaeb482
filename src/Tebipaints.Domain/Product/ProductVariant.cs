using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Product.Errors;
using Tebipaints.Domain.Product.Events;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Product;

public sealed class ProductVariant : Entity
{
    private readonly List<PackagingOption> _packagingOptions = [];

    private ProductVariant(){}
    
    private ProductVariant(
        Guid id,
        Guid productId,
        SKU sku,
        Volume volume,
        Color? color) : base(id)
    {
        ProductId = productId;
        Sku = sku;
        Volume = volume;
        Color = color;
    }
    
    public Guid ProductId { get; private set; }
    public SKU Sku { get; private set; }
    public Volume Volume { get; private set; }
    public Color? Color { get; private set; }
    public ProductVariantStatus Status { get; private set; }
    public IReadOnlyCollection<PackagingOption> PackagingOptions => _packagingOptions.AsReadOnly();

    public static Result<ProductVariant> Create(
        Guid productId,
        SKU sku,
        Volume volume,
        Color? color)
    {
        if (productId == Guid.Empty)
        {
            return Result.Failure<ProductVariant>(ProductErrors.InvalidProductId());
        }

        var variant = new ProductVariant(
            Guid.NewGuid(),
            productId,
            sku,
            volume,
            color);
        
        variant.RaiseDomainEvent(new ProductVariantCreatedDomainEvent(
            variant.Id,
            variant.ProductId,
            variant.Sku.Value));
        
        return Result.Success(variant);
    }

    public Result AddPackagingOption(
        PackagingType type,
        string material,
        Measurement capacity,
        Money price)
    {
        // Add validation for appropriate packaging types
        if (!IsValidPackagingType(type, capacity))
        {
            return Result.Failure(ProductErrors.InvalidPackagingTypeForCapacity());
        }
        
        var packagingOption = PackagingOption.Create(
            type,
            material,
            capacity,
            price);

        if (packagingOption.IsFailure)
        {
            return Result.Failure(packagingOption.Error);
        }

        _packagingOptions.Add(packagingOption.Value);

        RaiseDomainEvent(new PackagingOptionAddedDomainEvent(
            Id,
            ProductId,
            packagingOption.Value));

        return Result.Success();
    }
    
    public Result Discontinue()
    {
        if (Status == ProductVariantStatus.Discontinued)
        {
            return Result.Failure(ProductErrors.VariantAlreadyDiscontinued());
        }

        Status = ProductVariantStatus.Discontinued;

        RaiseDomainEvent(new ProductVariantDiscontinuedDomainEvent(
            Id,
            ProductId));

        return Result.Success();
    }
    
    private bool IsValidPackagingType(PackagingType type, Measurement capacity)
    {
        return type switch
        {
            PackagingType.Bucket => capacity.Value is >= 10 and <= 25,  // 10-25L buckets
            PackagingType.Can => capacity.Value is >= 1 and <= 5,       // 1-5L cans
            PackagingType.Drum => capacity.Value is >= 50 and <= 200,   // 50-200L drums
            PackagingType.Pail => capacity.Value is >= 5 and <= 20,     // 5-20L pails
            PackagingType.Tin => capacity.Value is >= 0.1m and <= 1,     // 100ml-1L tins
            PackagingType.Gallon => capacity.Value is >= 3.5m and <= 4,  // ~4L gallons
            PackagingType.Container => capacity.Value >= 500,           // 500L+ containers
            PackagingType.Sachet => capacity.Value is >= 0.01m and <= 0.1m, // 10-100ml sachets
            _ => false
        };
    }
}