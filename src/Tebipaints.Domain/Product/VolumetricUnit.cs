namespace Tebipaints.Domain.Product;

public record VolumetricUnit
{
    internal static readonly VolumetricUnit None = new("");
    public static readonly VolumetricUnit Ltr = new("Ltr");
    public static readonly VolumetricUnit MLtr = new("mLtr");

    private VolumetricUnit(string unit) => Unit = unit;
    
    public string Unit { get; init; }

    public static VolumetricUnit FromUnit(string? unit)
    {
        return All.FirstOrDefault(u => u.Unit == unit) ?? 
               throw new ApplicationException($"Unknown volumetric unit '{unit}'");
    }

    public static readonly IReadOnlyCollection<VolumetricUnit> All = new[]
    {
        Ltr,
        MLtr
    };
}