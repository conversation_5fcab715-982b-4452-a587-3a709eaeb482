using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Domain.Product.Errors;

public class ProductErrors
{
    public static Error InvalidManufacturingDate() =>
        new("Product.InvalidManufacturingDate", 
            "Manufacturing date cannot be in the future.");

    public static Error InvalidExpirationDate() =>
        new("Product.InvalidExpirationDate", 
            "Expiration date must be after manufacturing date.");

    public static Error InvalidPrice() =>
        new("Product.InvalidPrice", 
            "Price must be greater than zero.");

    public static Error AlreadyDiscontinued() =>
        new("Product.AlreadyDiscontinued", 
            "Product is already discontinued.");
    
    public static Error InvalidBatchNumber() =>
        new("Product.InvalidBatchNumber",
            "The batch number is invalid.");

    public static Error InvalidBatchNumberFormat() => new(
        "Product.InvalidBatchNumberFormat",
        "The batch number format is invalid.");

    public static Error InvalidVolume() => new(
        "Product.InvalidVolume",
        "Volume must be greater than zero.");
    
    public static Error InvalidProductId() => new(
        "ProductBatch.InvalidProductId",
        "The product id must be a valid GUID.");
    
    public static Error InvalidPackagingMaterial() => new(
        "ProductBatch.InvalidPackagingMaterial",
        "The packaging material is invalid.");
    
    public static Error InvalidPackagingCapacity() => new(
        "ProductBatch.InvalidPackagingCapacity",
        "The capacity must be greater than zero.");
    
    public static Error VariantAlreadyDiscontinued() => new(
        "ProductBatch.VariantAlreadyDiscontinued",
        "The variant has already been discontinued.");
    
    public static Error PackagingNotActive() => new(
        "ProductBatch.InvalidPackagingCapacity",
        "The capacity must be greater than zero.");

    public static Error InvalidFormulationId() => new(
        "Product.InvalidFormulationId", 
        "The formulation id cannot be empty or in an invalid format.");

    public static Error NotFound(Guid id) => new(
        "Product.NotFound",
        $"The product with this code {id} was not found.");
    
    public static Error FormulationNotFound(Guid id) => new(
        "Product.NotFound",
        $"The product with this code {id} was not found.");

    public static Error InvalidPackagingTypeForCapacity() => new(
        "Product.InvalidPackagingTypeForCapacity",
        "The selected packaging type is not valid for the specified capacity.");

    public static Error PackagingTypeNotSupported(PackagingType type) => new(
        "Product.PackagingTypeNotSupported",
        $"The packaging type {type} is not supported for this product.");
}