using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Product.Errors;

namespace Tebipaints.Domain.Product;

public record Volume
{
    public decimal Value { get; }
    public VolumetricUnit Unit { get; }

    private Volume(decimal value, VolumetricUnit unit)
    {
        Value = value;
        Unit = unit;
    }

    public static Result<Volume> Create(decimal value, VolumetricUnit unit)
    {
        if (value <= 0)
            return Result.Failure<Volume>(ProductErrors.InvalidVolume());
        
        return Result.Success(new Volume(value, unit));
    }
    
    public override string ToString() => $"{Value}{Unit}";
}