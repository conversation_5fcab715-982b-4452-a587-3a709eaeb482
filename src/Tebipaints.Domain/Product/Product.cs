using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Product.Errors;
using Tebipaints.Domain.Product.Events;
using Tebipaints.Domain.Production.Enums;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Product;

public sealed class Product : Entity
{
    private readonly List<ProductVariant> _variants = [];
    
    private Product(){}
    
    private Product(
        Guid id,
        Guid formulationId,
        ProductName name,
        SKU sku,
        Color color,
        Volume volume,
        ProductType productType,
        DateTime createdOnUtc) : base(id)
    {
        Name = name;
        Sku = sku;
        Color = color;
        Volume = volume;
        Type = productType;
        CreatedOnUtc = createdOnUtc;
        Status = ProductStatus.Active;
        FormulationId = formulationId;
    }
    
    public Guid? FormulationId { get; private set; }
    public ProductName Name { get; private set; }
    public SKU Sku { get; private set; }
    public Color Color { get; private set; }
    public Volume Volume { get; private set; }
    public ProductType Type { get; private set; }
    public Money? Price { get; private set; }
    public DateTime CreatedOnUtc { get; private set; }
    public ProductStatus Status { get; private set; }
    public IReadOnlyCollection<ProductVariant> Variants => _variants.AsReadOnly();

    /// <summary>
    /// Creates a new product with the specified details.
    /// </summary>
    /// <param name="name">The name of the product</param>
    /// <param name="sku">The Stock Keeping Unit (SKU)</param>
    /// <param name="color">The color of the product</param>
    /// <param name="volume">The volume of the product</param>
    /// <param name="productType"></param>
    /// <param name="formulationId">The assigned formulation </param>
    /// <returns>A Result containing the new Product if successful, or failure details if invalid</returns>
    /// <remarks>
    /// A product can be created if:
    /// - All required fields are provided
    /// - Manufacturing date is not in the future
    /// - Expiration date is after manufacturing date
    /// - Price is greater than zero
    /// </remarks>
    public static Result<Product> Create(ProductName name,
        SKU sku,
        Color color,
        Volume volume,
        ProductType productType,
        Guid? formulationId = null)
    {
    
        // if (manufacturingDate > DateTime.UtcNow)
        // {
        //     return Result.Failure<Product>(ProductErrors.InvalidManufacturingDate());
        // }
        //
        // if (expirationDate <= manufacturingDate)
        // {
        //     return Result.Failure<Product>(ProductErrors.InvalidExpirationDate());
        // }
        //
        // if (price.Amount <= 0)
        // {
        //     return Result.Failure<Product>(ProductErrors.InvalidPrice());
        // }
        
        
        var product = new Product(
            Guid.NewGuid(),
            formulationId ?? Guid.Empty,
            name,
            sku,
            color,
            volume,
            productType,
            DateTime.UtcNow);
        
        product.RaiseDomainEvent(new ProductCreatedDomainEvent(product.Id, product.Sku.Value));
        
        return Result.Success(product);
    }
    
    /// <summary>
    /// Updates the price of the product.
    /// </summary>
    /// <param name="newPrice">The new price to set</param>
    /// <returns>Success if the price was updated, or failure if the price is invalid</returns>
    public Result UpdatePrice(Money newPrice)
    {
        if (newPrice.Amount <= 0)
        {
            return Result.Failure(ProductErrors.InvalidPrice());
        }

        Price = newPrice;
        
        RaiseDomainEvent(new ProductPriceUpdatedDomainEvent(Id, newPrice));
        
        return Result.Success();
    }

    /// <summary>
    /// Discontinues the product, preventing it from being sold.
    /// </summary>
    /// <returns>Success if the product was discontinued, or failure if it cannot be discontinued</returns>
    public Result Discontinue()
    {
        if (Status == ProductStatus.Discontinued)
        {
            return Result.Failure(ProductErrors.AlreadyDiscontinued());
        }

        Status = ProductStatus.Discontinued;
        
        RaiseDomainEvent(new ProductDiscontinuedDomainEvent(Id));
        
        return Result.Success();
    }

    public Result AssignFormulation(Guid formulationId)
    {
        if (formulationId == Guid.Empty)
        {
            return Result.Failure(ProductErrors.InvalidFormulationId());
        }
        
        FormulationId = formulationId;
        
        RaiseDomainEvent(new FormulationAssignedDomainEvent(Id, formulationId));
        
        return Result.Success();
    }
    
    public Result AddVariant(
        SKU sku,
        Volume volume,
        Color? color = null)
    {
        var variantResult = ProductVariant.Create(
            Id,  // Pass the product's ID
            sku,
            volume,
            color ?? Color);  // Use product's color if not specified

        if (variantResult.IsFailure)
        {
            return Result.Failure(variantResult.Error);
        }

        _variants.Add(variantResult.Value);
        
        return Result.Success();
    }
}