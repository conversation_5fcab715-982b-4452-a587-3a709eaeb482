using System.Text.RegularExpressions;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Product.Errors;

namespace Tebipaints.Domain.Product;

public record BatchNumber
{
    public string Value { get; }
    
    private BatchNumber(string value) => Value = value;

    public static Result<BatchNumber> Create(string batchNumber)
    {
        if (string.IsNullOrWhiteSpace(batchNumber))
            return Result.Failure<BatchNumber>(ProductErrors.InvalidBatchNumber());

        if (!Regex.IsMatch(batchNumber, @"^[A-Z0-9-]+$"))
            return Result.Failure<BatchNumber>(ProductErrors.InvalidBatchNumberFormat());

        return Result.Success(new BatchNumber(batchNumber));
    }
}