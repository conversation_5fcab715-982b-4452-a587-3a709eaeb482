using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation.Errors;

namespace Tebipaints.Domain.Formulation;

public sealed class ChangeLog : Entity
{
    private readonly List<ChangeLogEntry> _entries = new();

    public IReadOnlyList<ChangeLogEntry> Entries => _entries.AsReadOnly();

    private ChangeLog(){}
    
    private ChangeLog (
        Guid changeLogId,
        List<ChangeLogEntry> entries) : base(changeLogId)
    {
        _entries = entries;
    }

    public static ChangeLog Create(List<ChangeLogEntry>? entries = null )
    {
        return new ChangeLog(Guid.NewGuid(), entries ?? new());
    }

    public Result AddEntry (ChangeLogEntry entry)
    {
        if(entry is null)
        {
            return Result.Failure(FormulationErrors.InvalidLogEntry());
        }

        _entries.Add(entry);
        return Result.Success();
    }

    public Result ClearEntries()
    {
        _entries.Clear();

        return Result.Success();
    }
}