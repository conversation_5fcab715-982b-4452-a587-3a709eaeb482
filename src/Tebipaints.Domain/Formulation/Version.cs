using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation.Errors;

namespace Tebipaints.Domain.Formulation;

public sealed class Version : Entity
{
    public DateTime TimeStamp { get; private set; }
    public FormulationSnapshot Snapshot { get; private set; }
    public ChangeLog? ChangeLog { get; private set; }
    public bool IsFinalized { get; private set; }

    private Version(){}
    
    private Version(
        Guid versionId,
        DateTime timeStamp,
        FormulationSnapshot snapshot,
        ChangeLog? changeLog = null
    ) : base(versionId)
    {
        TimeStamp = timeStamp;
        Snapshot = snapshot;
        ChangeLog = changeLog;
    }

    public static Result<Version> CreateInitial(
        DateTime timeStamp,
        FormulationSnapshot snapshot
    )
    {

        if(snapshot is null)
        {
            return Result.Failure<Version>(FormulationErrors.InvalidSnapshot());
        }

        return new Version(
            Guid.NewGuid(),
            timeStamp,
            snapshot
        );
    }

    public static Result<Version> Create(
        DateTime timeStamp,
        FormulationSnapshot snapshot,
        ChangeLog changeLog
    )
    {
        if(snapshot is null)
        {
            return Result.Failure<Version>(FormulationErrors.InvalidSnapshot());
        }

        return new Version(
            Guid.NewGuid(),
            timeStamp,
            snapshot,
            changeLog
        );
    }
}