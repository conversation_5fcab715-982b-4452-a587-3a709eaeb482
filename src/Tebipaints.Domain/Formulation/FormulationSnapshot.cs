using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation.Errors;
using Tebipaints.Domain.Production;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Formulation;

public sealed record FormulationSnapshot
{
    public Measurement MinimumProductionQuantity { get; private set; }
    public TimeSpan EstimatedProductionTime { get; private set; }
    public int ShelfLife { get; private set; }
    public IReadOnlyList<IngredientSnapshot> Ingredients { get; private set; }
    public IReadOnlyList<string> Instructions { get; private set; }

    private FormulationSnapshot(){}
    
    private FormulationSnapshot(
        Measurement minimumProductionQuantity,
        TimeSpan productionTime,
        int shelfLife,
        List<IngredientSnapshot> ingredients,
        List<string> instructions)
    {
        MinimumProductionQuantity = minimumProductionQuantity;
        Instructions = instructions;
        Ingredients = ingredients;
        EstimatedProductionTime = productionTime;
        ShelfLife = shelfLife;
    }

    /// <summary>
    /// Creates a new formulation snapshot.
    /// </summary>
    /// <param name="minimumProductionQuantity">The minimum quantity that can be produced</param>
    /// <param name="instructions">Production instructions</param>
    /// <param name="productionTime">Estimated production time</param>
    /// <param name="shelfLife">Product shelf life in months</param>
    /// <param name="ingredients">List of ingredients and their proportions</param>
    /// <returns>A Result containing the new FormulationSnapshot if valid, or failure details if invalid</returns>
    /// <remarks>
    /// All parameters must be valid:
    /// - Minimum production quantity must be greater than zero
    /// - Instructions must not be empty
    /// - Production time must be positive
    /// - Shelf life must be greater than zero
    /// - Must have at least two ingredients
    /// - Total ingredient proportions must sum to 100%
    /// </remarks>
    public static Result<FormulationSnapshot> Create(
        Measurement minimumProductionQuantity,
        TimeSpan productionTime,
        int shelfLife,
        List<IngredientSnapshot> ingredients,
        List<string>? instructions = null
    )
    {
        if (minimumProductionQuantity.Value <= 0)
        {
            return Result.Failure<FormulationSnapshot>(
                FormulationErrors.InvalidMinimumProductionQuantity());
        }

        if (productionTime <= TimeSpan.Zero)
        {
            return Result.Failure<FormulationSnapshot>(
                FormulationErrors.InvalidProductionTime());
        }

        if (shelfLife <= 0)
        {
            return Result.Failure<FormulationSnapshot>(
                FormulationErrors.InvalidShelfLife());
        }

        if (ingredients.Count < 2)
        {
            return Result.Failure<FormulationSnapshot>(
                FormulationErrors.InsufficientIngredients());
        }

        return Result.Success(new FormulationSnapshot(
            minimumProductionQuantity,
            productionTime,
            shelfLife,
            ingredients,
            instructions ?? []));

    }
}