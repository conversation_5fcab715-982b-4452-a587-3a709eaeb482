using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation.Errors;

namespace Tebipaints.Domain.Formulation;

public sealed record QualitySpecification
{
    public string PropertyName { get; private init; }
    public string TestMethod { get; private init; }
    public decimal MinValue { get; private init; }
    public decimal MaxValue { get; private init; }
    public string Unit { get; private init; }
    public bool IsCritical { get; private init; }
    public string? Notes { get; private init; }

    private QualitySpecification(){}
    private QualitySpecification(
        string propertyName,
        string testMethod,
        decimal minValue,
        decimal maxValue,
        string unit,
        bool isCritical,
        string? notes)
    {
        PropertyName = propertyName;
        TestMethod = testMethod;
        MinValue = minValue;
        MaxValue = maxValue;
        Unit = unit;
        IsCritical = isCritical;
        Notes = notes;
    }

    public static Result<QualitySpecification> Create(
        string propertyName,
        string testMethod,
        decimal minValue,
        decimal maxValue,
        string unit,
        bool isCritical = false,
        string? notes = null)
    {
        if (string.IsNullOrWhiteSpace(propertyName))
            return Result.Failure<QualitySpecification>(FormulationErrors.InvalidPropertyName());

        if (string.IsNullOrWhiteSpace(testMethod))
            return Result.Failure<QualitySpecification>(FormulationErrors.InvalidTestMethod());

        if (string.IsNullOrWhiteSpace(unit))
            return Result.Failure<QualitySpecification>(FormulationErrors.InvalidUnit());

        if (minValue > maxValue)
            return Result.Failure<QualitySpecification>(FormulationErrors.InvalidRange());

        return Result.Success(new QualitySpecification(
            propertyName.Trim(),
            testMethod.Trim(),
            minValue,
            maxValue,
            unit.Trim(),
            isCritical,
            notes?.Trim()));
    }

    public bool IsWithinSpecification(decimal testValue)
    {
        return testValue >= MinValue && testValue <= MaxValue;
    }

    public decimal GetDeviationPercentage(decimal testValue)
    {
        if (IsWithinSpecification(testValue))
            return 0;

        var target = (MinValue + MaxValue) / 2;
        return Math.Abs((testValue - target) / target) * 100;
    }
}
