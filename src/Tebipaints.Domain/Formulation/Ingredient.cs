using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation.Errors;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Formulation;

public sealed class Ingredient : Entity
{
    public Guid MaterialId { get; private set; }
    public Measurement Quantity { get; private set; }

    private Ingredient(){}
    
    private Ingredient(
        Guid ingredientId,
        Guid materialId,
        Measurement quantity
    ) : base(ingredientId)
    {
        MaterialId = materialId;
        Quantity = quantity;
    }

    /// <summary>
    /// Creates a new ingredient for a formulation.
    /// </summary>
    /// <param name="materialId">The ID of the raw material</param>
    /// <param name="quantity">The quantity needed</param>
    /// <param name="units">The units of measurement</param>
    /// <returns>A Result containing the new Ingredient if valid, or failure details if invalid</returns>
    public static Result<Ingredient> Create(
        Guid materialId,
        double quantity,
        string units
    )
    {
        if (materialId == Guid.Empty)
        {
            return Result.Failure<Ingredient>(
                IngredientErrors.InvalidMaterialId());
        }

        if (quantity <= 0)
        {
            return Result.Failure<Ingredient>(
                IngredientErrors.InvalidQuantity());
        }
        var unitOfMeasure = UnitOfMeasureConverter.FromString(units);
        var ingredient = new Ingredient(
            Guid.NewGuid(),
            materialId,
            new Measurement((decimal)quantity, unitOfMeasure.Value)
        );
        
        return Result.Success(ingredient);
    }

    /// <summary>
    /// Updates the quantity of the ingredient.
    /// </summary>
    /// <param name="quantity">The new quantity</param>
    /// <returns>Success if updated, or failure if quantity is invalid</returns>
    public Result UpdateQuantity(Measurement quantity)
    {
        if(quantity.Value <= 0)
        {
            return Result.Failure(FormulationErrors.InvalidIngredientQuantity());
        }

        Quantity = quantity;

        return Result.Success();
    }
}