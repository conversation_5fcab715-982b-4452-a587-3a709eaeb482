using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation.Errors;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Formulation;

public sealed class FormulationCost
{
    public Money MaterialCost { get; private set; } = null!;
    public Money LaborCost { get; private set; } = null!;
    public Money OverheadCost { get; private set; } = null!;
    public Money TotalCost { get; private set; } = null!;
    public Money CostPerUnit { get; private set; } = null!;
    public DateTime CalculatedAt { get; private set; }
    public string CalculationBasis { get; private set; } = string.Empty;

    
    private FormulationCost(){}

    private FormulationCost(
        Money materialCost,
        Money laborCost,
        Money overheadCost,
        Money costPerUnit,
        string calculationBasis)
    {
        MaterialCost = materialCost;
        LaborCost = laborCost;
        OverheadCost = overheadCost;
        TotalCost = new Money(
            materialCost.Amount + laborCost.Amount + overheadCost.Amount,
            materialCost.Currency);
        CostPerUnit = costPerUnit;
        CalculatedAt = DateTime.UtcNow;
        CalculationBasis = calculationBasis;
    }

    public static Result<FormulationCost> Create(
        Money materialCost,
        Money laborCost,
        Money overheadCost,
        decimal productionQuantity,
        string calculationBasis)
    {
        if (materialCost.Amount < 0)
            return Result.Failure<FormulationCost>(FormulationErrors.InvalidCost("Material cost cannot be negative"));

        if (laborCost.Amount < 0)
            return Result.Failure<FormulationCost>(FormulationErrors.InvalidCost("Labor cost cannot be negative"));

        if (overheadCost.Amount < 0)
            return Result.Failure<FormulationCost>(FormulationErrors.InvalidCost("Overhead cost cannot be negative"));

        if (productionQuantity <= 0)
            return Result.Failure<FormulationCost>(FormulationErrors.InvalidProductionQuantity());

        if (string.IsNullOrWhiteSpace(calculationBasis))
            return Result.Failure<FormulationCost>(FormulationErrors.InvalidCalculationBasis());

        var totalCost = materialCost.Amount + laborCost.Amount + overheadCost.Amount;
        var costPerUnit = new Money(totalCost / productionQuantity, materialCost.Currency);

        return Result.Success(new FormulationCost(
            materialCost,
            laborCost,
            overheadCost,
            costPerUnit,
            calculationBasis));
    }

    public FormulationCost ScaleForQuantity(decimal newQuantity, decimal baseQuantity)
    {
        var scalingFactor = newQuantity / baseQuantity;

        return new FormulationCost(
            new Money(MaterialCost.Amount * scalingFactor, MaterialCost.Currency),
            new Money(LaborCost.Amount * scalingFactor, LaborCost.Currency),
            new Money(OverheadCost.Amount * scalingFactor, OverheadCost.Currency),
            CostPerUnit, // Cost per unit remains the same
            $"{CalculationBasis} (scaled for {newQuantity} units)");
    }


}
