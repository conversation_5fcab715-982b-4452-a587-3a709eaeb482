using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Formulation;

public sealed record IngredientSnapshot
{
    public Guid IngredientId { get; private set; }
    public Guid RawMaterialId { get; private set; }
    public Measurement Quantity { get; private set; }

    private IngredientSnapshot(){}
    
    private IngredientSnapshot (
        Guid ingredientId,
        Guid rawMaterialId,
        Measurement quantity
    )
    {
        IngredientId = ingredientId;
        RawMaterialId = rawMaterialId;
        Quantity = quantity;
    }

    public static IngredientSnapshot Create(
        Guid ingredientId,
        Guid rawMaterialId,
        Measurement quantity
    )
    {
        return new IngredientSnapshot(
            ingredientId,
            rawMaterialId,
            quantity
        );
    }
}