using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Domain.Formulation.Errors;

public class FormulationErrors
{
    public static Error InvalidFormulationId() => new(
        "Formulation.InvalidFormulationId",
        "The provided id value is invalid");
    
    public static Error FormulationNotFound() => new(
        "Formulation.FormulationNotFound",
        "The specified formulation was not found");
    
    public static Error FormulationNotFound(Guid id) => new(
        "Formulation.FormulationNotFound",
        $"The specified formulation with id {id} was not found");
    
    public static Error FormulationAlreadyArchived() => new(
        "Formulation.FormulationAlreadyArchived",
        "The formulation already has the same status. Check values");
    
    public static Error InvalidMinimumProductionQuantity() => new(
        "Formulation.InvalidMinimumProductionQuantity",
        "The provided minimum production quantity is invalid");
    
    public static Error CannotCalculateQuantities() => new(
        "Formulation.CannotCalculateQuantities",
        "Could not calculate ingredient quantities. Check parameters");
    
    public static Error CannotCreateAFormulationWithoutIngredients() => new(
        "Formulation.CannotCreateAFormulationWithoutIngredients",
        "Cannot create a formulation without ingredients");
    
    public static Error InvalidVersionId() => new(
        "Formulation.InvalidVersionId",
        "The provided version id value is invalid");
    
    public static Error FailedToCreateANewVersion() => new(
        "Formulation.FailedToCreateANewVersion",
        "Cannot create a new version without any modifications detected");
    
    public static Error FailedToModifyFormulation() => new(
        "Formulation.FailedToModifyFormulation",
        "Could not modify the formulation");
    
    public static Error NoModificationDetected() => new(
        "Formulation.NoModificationDetected",
        "No modification detected. The new value and old value are the same");
    
    public static Error InvalidIngredientId() => new(
        "Formulation.InvalidIngredientId",
        "The provided ingredient id value is invalid");
    
    public static Error InvalidIngredientQuantity() => new(
        "Formulation.InvalidIngredientQuantity",
        "The provided ingredient id value is invalid");
    
    public static Error InvalidChangeLogId() => new(
        "Formulation.InvalidChangeLogId",
        "The provided change log id value is invalid");
    
    public static Error InvalidLogEntry() => new(
        "Formulation.InvalidLogEntry",
        "The provided log entry is empty or invalid");
    
    public static Error InvalidSnapshot() => new(
        "Formulation.InvalidSnapshot",
        "The provided snapshot cannot be empty");

    public static Error NoCurrentVersion() => new(
        "Formulation.NoCurrentVersion",
        "No current version found");

    public static Error CannotModifyArchivedFormulation() => new(
        "Formulation.CannotModifyArchivedFormulation",
        "Cannot modify archived formulation");

    public static Error AuthorRequired() => new(
        "Formulation.AuthorRequired",
        "Cannot modify formulation without an author.");

    public static Error InvalidInstructions() => new(
        "Formulation.InvalidInstructions",
        "The provided instructions are invalid");

    public static Error InsufficientIngredients() => new(
        "Formulation.InsufficientIngredient",
        "You must provide at least one ingredient");
    
    public static Error InvalidProductionTime() => new(
        "Formulation.InvalidProductionTime",
        "Production time specified for formulation is invalid");
    
    public static Error InvalidShelfLife() => new(
        "Formulation.InvalidShelfLife",
        "Shelf life specified for formulation is invalid");

    // Costing Errors
    public static Error InvalidCost(string message) => new(
        "Formulation.InvalidCost",
        message);

    public static Error InvalidProductionQuantity() => new(
        "Formulation.InvalidProductionQuantity",
        "Production quantity must be greater than zero");

    public static Error InvalidCalculationBasis() => new(
        "Formulation.InvalidCalculationBasis",
        "Calculation basis cannot be empty");

    // Quality Specification Errors
    public static Error InvalidPropertyName() => new(
        "Formulation.InvalidPropertyName",
        "Property name cannot be empty");

    public static Error InvalidTestMethod() => new(
        "Formulation.InvalidTestMethod",
        "Test method cannot be empty");

    public static Error InvalidUnit() => new(
        "Formulation.InvalidUnit",
        "Unit cannot be empty");

    public static Error InvalidRange() => new(
        "Formulation.InvalidRange",
        "Minimum value cannot be greater than maximum value");

    // Approval Errors
    public static Error CannotApproveFormulation(string reason) => new(
        "Formulation.CannotApproveFormulation",
        $"Cannot approve formulation: {reason}");

    public static Error CannotRejectFormulation(string reason) => new(
        "Formulation.CannotRejectFormulation",
        $"Cannot reject formulation: {reason}");

    public static Error ApprovalRequired() => new(
        "Formulation.ApprovalRequired",
        "Formulation must be approved before use in production");
}