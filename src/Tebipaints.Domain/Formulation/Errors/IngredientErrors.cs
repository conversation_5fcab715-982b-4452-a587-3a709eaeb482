using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Domain.Formulation.Errors;

public class IngredientErrors
{
    public static Error InvalidMaterialId() =>
        new("Ingredient.InvalidMaterialId", 
            "Material ID must be provided.");

    public static Error InvalidQuantity() =>
        new("Ingredient.InvalidQuantity", 
            "Quantity must be greater than zero.");

    public static Error InvalidProportion() =>
        new("Ingredient.InvalidProportion", 
            "Proportion must be between 0 and 100.");

    public static Error InvalidUnits() =>
        new("Ingredient.InvalidUnits", 
            "Units of measurement must be specified.");
}