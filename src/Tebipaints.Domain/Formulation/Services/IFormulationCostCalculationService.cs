using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Formulation.Services;

/// <summary>
/// Domain service for calculating formulation costs based on current material prices
/// and production parameters. This service encapsulates the complex business logic
/// for cost calculation that involves multiple aggregates (materials, contracts, etc.)
/// </summary>
public interface IFormulationCostCalculationService
{
    /// <summary>
    /// Calculates the cost for a formulation based on its ingredients and production parameters
    /// </summary>
    /// <param name="ingredients">List of ingredients with their quantities</param>
    /// <param name="productionQuantity">The quantity to be produced</param>
    /// <param name="estimatedProductionTime">Time required for production</param>
    /// <param name="currency">Currency for cost calculation</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>FormulationCost or failure if calculation cannot be performed</returns>
    Task<Result<FormulationCost>> CalculateCostAsync(
        IReadOnlyList<Ingredient> ingredients,
        Measurement productionQuantity,
        TimeSpan estimatedProductionTime,
        Currency currency,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Recalculates cost for an existing formulation (used when material prices change)
    /// </summary>
    /// <param name="formulation">The formulation to recalculate</param>
    /// <param name="currency">Currency for cost calculation</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated FormulationCost or failure</returns>
    Task<Result<FormulationCost>> RecalculateCostAsync(
        Formulation formulation,
        Currency currency,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates if a formulation cost is within acceptable business thresholds
    /// </summary>
    /// <param name="cost">The cost to validate</param>
    /// <param name="productType">Type of product being produced</param>
    /// <returns>Validation result with any threshold violations</returns>
    Result ValidateCostThresholds(FormulationCost cost, string productType);
}
