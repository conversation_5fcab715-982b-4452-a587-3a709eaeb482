using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation.Enums;
using Tebipaints.Domain.Formulation.Errors;
using Tebipaints.Domain.Formulation.Events;
using Tebipaints.Domain.Formulation.Services;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Formulation;

public sealed class Formulation : Entity
{
    private readonly List<Ingredient> _ingredients = new();
    private readonly List<Version> _versions = new();
    
    private Formulation(){}
    
    private Formulation(
        Guid formulationId,
        string name,
        string description,
        FormulationStatus status,
        int shelfLife,
        TimeSpan productionTime,
        List<Version> versions,
        List<Ingredient> ingredients) : base(formulationId)
    {
        Name = name;
        Description = description;
        Status = status;
        ShelfLife = shelfLife;
        EstimatedProductionTime = productionTime;
        _ingredients = ingredients;
        _versions = versions;
    }
    
    public string Name { get; private set; }
    public string Description { get; private set; }
    public int ShelfLife { get; private set; }
    public TimeSpan EstimatedProductionTime { get; private set; }
    public FormulationStatus Status { get; private set; }
    public FormulationCost? EstimatedCost { get; private set; }
    public IReadOnlyList<Ingredient> Ingredients => _ingredients.AsReadOnly();
    public IReadOnlyList<Version> Versions => _versions.AsReadOnly();
    
    /// <summary>
    /// Creates a new formulation with initial version.
    /// </summary>
    /// <param name="name">Name of the formulation</param>
    /// <param name="description">Description of the formulation</param>
    /// <param name="minimumProductionQuantity">Minimum quantity that can be produced</param>
    /// <param name="instructions">List of production instructions</param>
    /// <param name="productionTimeInHours">Estimated production time in hours</param>
    /// <param name="shelfLife">Shelf life of product</param>
    /// <param name="ingredients">List of ingredients required</param>
    /// <returns>A Result containing the new Formulation if successful, or failure details if invalid</returns>
    /// <remarks>
    /// This operation:
    /// - Creates the initial version with provided details
    /// - Sets status to Pending
    /// - Validates minimum requirements
    /// - Raises a FormulationCreated event
    /// </remarks>
    public static Result<Formulation> Create(
        string name,
        string? description,
        Measurement minimumProductionQuantity,
        int productionTimeInHours,
        int shelfLife,
        List<Ingredient> ingredients,
        List<string>? instructions = null
    )
    {
        List<Version> versions = new();

        if(minimumProductionQuantity.Value <= 0)
        {
            return Result.Failure<Formulation>(FormulationErrors.InvalidMinimumProductionQuantity());
        }

        if (ingredients.Count <= 1)
        {
            return Result.Failure<Formulation>(FormulationErrors.CannotCreateAFormulationWithoutIngredients());
        }

        // create ingredient snapshot from list<Ingredient>
        
        var ingredientSnapshotList = ingredients.ConvertAll(ingredient => IngredientSnapshot.Create(
            ingredient.Id,
            ingredient.MaterialId,
            ingredient.Quantity
        ));
        
        // create a snapshot of the formulation
        var snapshot = FormulationSnapshot.Create(
            minimumProductionQuantity,
            TimeSpan.FromHours(productionTimeInHours),
            shelfLife,
            ingredientSnapshotList,
            instructions ?? new List<string>());

        // create a version with the snapshot
        var version = Version.CreateInitial(DateTime.UtcNow, snapshot.Value);
        versions.Add(version.Value);

        // create the formulation
        var formulation = new Formulation(
            Guid.NewGuid(),
            name.Trim(),
            description?.Trim() ?? string.Empty,
            FormulationStatus.Draft,
            shelfLife,
            TimeSpan.FromHours(productionTimeInHours),
            versions,
            ingredients);

        formulation.RaiseDomainEvent(new FormulationCreatedDomainEvent(formulation));
        
        return Result.Success(formulation);
    }

    /// <summary>
    /// Updates the estimated cost for this formulation.
    /// This should be called after creation and whenever material prices change.
    /// </summary>
    /// <param name="cost">The calculated cost</param>
    /// <returns>Success if cost is updated</returns>
    public Result UpdateEstimatedCost(FormulationCost cost)
    {
        if (cost == null)
        {
            return Result.Failure(FormulationErrors.InvalidCost("Cost cannot be null"));
        }

        var previousCost = EstimatedCost;
        EstimatedCost = cost;

        // Raise event if cost has changed significantly (more than 5%)
        if (previousCost != null && HasSignificantCostChange(previousCost, cost))
        {
            RaiseDomainEvent(new FormulationCostUpdatedDomainEvent(Id, previousCost, cost));
        }

        return Result.Success();
    }

    /// <summary>
    /// Determines if the cost change is significant enough to warrant notification
    /// </summary>
    private static bool HasSignificantCostChange(FormulationCost previousCost, FormulationCost newCost)
    {
        if (previousCost.TotalCost.Amount == 0) return true;

        var changePercentage = Math.Abs(newCost.TotalCost.Amount - previousCost.TotalCost.Amount) / previousCost.TotalCost.Amount;
        return changePercentage > 0.05m; // 5% threshold
    }
    
    /// <summary>
    /// Modifies the formulation by creating a new version.
    /// </summary>
    /// <param name="author">Name of the person making the modification</param>
    /// <param name="minimumProductionQuantity">Updated minimum production quantity</param>
    /// <param name="instructions">Updated production instructions</param>
    /// <param name="productionTimeInHours">Updated production time in hours</param>
    /// <param name="shelfLife">Shelf life of product</param>
    /// <param name="ingredients">Updated list of ingredients</param>
    /// <returns>Success if the modification was applied, or failure if invalid</returns>
    /// <remarks>
    /// This operation:
    /// - Creates a new version with the changes
    /// - Tracks changes in the changelog
    /// - Validates all updates
    /// - Raises a FormulationModified event
    /// </remarks>
    public Result ModifyFormulation(
        string author,
        Measurement minimumProductionQuantity,
        int productionTimeInHours,
        int shelfLife,
        List<Ingredient> ingredients,
        List<string>? instructions = null
    )
    {
        if (Status == FormulationStatus.Archived)
        {
            return Result.Failure(FormulationErrors.CannotModifyArchivedFormulation());
        }

        if (string.IsNullOrWhiteSpace(author))
        {
            return Result.Failure(FormulationErrors.AuthorRequired());
        }

        if (minimumProductionQuantity.Value <= 0)
        {
            return Result.Failure(FormulationErrors.InvalidMinimumProductionQuantity());
        }

        if (ingredients.Count <= 1)
        {
            return Result.Failure(FormulationErrors.InsufficientIngredients());
        }
        
        
        Version? currentVersion = GetCurrentVersion();
        if (currentVersion is null)
        {
            return Result.Failure(FormulationErrors.NoCurrentVersion());
        }
        
        var changeLog = ChangeLog.Create();

        // check for differences in minimum production quantity
            // log change
        if (minimumProductionQuantity != currentVersion.Snapshot.MinimumProductionQuantity)
        {
            changeLog.AddEntry(ChangeLogEntry.Create(DateTime.UtcNow, "Modified minimum production quantity", author));
        }

        // check for differences in instruction
            // log change
        if (instructions != currentVersion.Snapshot.Instructions)
        {
            changeLog.AddEntry(ChangeLogEntry.Create(DateTime.UtcNow, "Modified instructions", author));
        }

        // check for differences in shelf life
            // log change
        if(shelfLife != currentVersion.Snapshot.ShelfLife)
        {
            changeLog.AddEntry(ChangeLogEntry.Create(DateTime.UtcNow, "Modified shelf life", author));
        }
        
        // create ingredient snapshot from list<Ingredient>
        var ingredientSnapshotList = ingredients.ConvertAll(ingredient => IngredientSnapshot.Create(
            ingredient.Id,
            ingredient.MaterialId,
            ingredient.Quantity
        ));
        
        // check for differences in ingredients
        // log change
        if (!currentVersion.Snapshot.Ingredients.SequenceEqual(ingredientSnapshotList))
        {
            changeLog.AddEntry(ChangeLogEntry.Create(DateTime.UtcNow, "Modified ingredients list", author));
        }

        // create a snapshot of the formulation
        var snapshot = FormulationSnapshot.Create(
            minimumProductionQuantity,
            TimeSpan.FromHours(productionTimeInHours),
            shelfLife,
            ingredientSnapshotList,
            instructions);

        // create a version with the snapshot
        var version = Version.Create(DateTime.UtcNow, snapshot.Value, changeLog);
        _versions.Add(version.Value);
        EstimatedProductionTime = TimeSpan.FromHours(productionTimeInHours);

        RaiseDomainEvent(new VersionCreatedDomainEvent(version.Value.Id));// change event to FormulationModifiedEvent

        return Result.Success();
    }
    
    /// <summary>
    /// Calculates ingredient quantities for a specific batch size.
    /// </summary>
    /// <param name="batchQuantity">The desired batch quantity</param>
    /// <returns>A dictionary of ingredient IDs and their adjusted quantities</returns>
    /// <remarks>
    /// Calculations are based on the minimum production quantity from the current version.
    /// </remarks>
    public Result<Dictionary<Guid, Measurement>> CalculateIngredientQuantities(Measurement batchQuantity)
    {
        Dictionary<Guid, Measurement> adjustedIngredients = new();

        if(batchQuantity.Value <= 0 )
        {
            return Result.Failure<Dictionary<Guid, Measurement>>(FormulationErrors.CannotCalculateQuantities());
        }

        // Get the current version
        var currentVersion = GetCurrentVersion();
        if (currentVersion is null)
        {
            return Result.Failure<Dictionary<Guid, Measurement>>(
                FormulationErrors.NoCurrentVersion());
        }

        // Retrieve the ingredients snapshot & minimum production quantity
        var ingredientsList = currentVersion.Snapshot.Ingredients;

        // calculate the required quantity for each ingredient based on the batchQuantity and the minimum production quantity
        var scalingFactor = batchQuantity.Value / currentVersion.Snapshot.MinimumProductionQuantity.Value;
        foreach(var ingredient in ingredientsList)
        {
            var adjustedQuantity = ingredient.Quantity.WithValue(ingredient.Quantity.Value * scalingFactor) ;

            // Add each ingredient to the dictionary using the name and adjusted quantity values
            adjustedIngredients.Add(ingredient.RawMaterialId, adjustedQuantity);
        }

        return  Result.Success(adjustedIngredients);
    }
    
    /// <summary>
    /// Calculates the actual production time based on batch quantity.
    /// </summary>
    /// <param name="actualProductionQuantity">The actual quantity being produced</param>
    /// <returns>The adjusted production time</returns>
    public Result<TimeSpan> CalculateActualProductionTime(double actualProductionQuantity)
    {
        //1. fetch minimum production quantity from current version
        Version? currentVersion = GetCurrentVersion();
        double minimumProductionQuantity = 1; 

        if(currentVersion is not null)
        {
            minimumProductionQuantity = (double)currentVersion.Snapshot.MinimumProductionQuantity.Value;
        }
        // Calculate the production ratio
        double productionRatio = actualProductionQuantity / minimumProductionQuantity;

        // Apply the ratio to the estimated production time
        TimeSpan actualProductionTime = TimeSpan.FromTicks((long)(EstimatedProductionTime.Ticks * productionRatio));

        return Result.Success(actualProductionTime);
    }
    
    private Version? GetCurrentVersion() =>
        _versions.OrderByDescending(v => v.TimeStamp).FirstOrDefault();
    
    /// <summary>
    /// Submits the formulation for approval review.
    /// </summary>
    /// <param name="submittedBy">The person submitting for approval</param>
    /// <returns>Success if submitted, or failure if invalid state</returns>
    public Result SubmitForApproval(string submittedBy)
    {
        if (string.IsNullOrWhiteSpace(submittedBy))
        {
            return Result.Failure(FormulationErrors.AuthorRequired());
        }

        if (Status != FormulationStatus.Draft && Status != FormulationStatus.Pending)
        {
            return Result.Failure(FormulationErrors.CannotApproveFormulation("Formulation must be in Draft or Pending status"));
        }

        Status = FormulationStatus.UnderReview;

        RaiseDomainEvent(new FormulationSubmittedForApprovalDomainEvent(Id, submittedBy));

        return Result.Success();
    }

    /// <summary>
    /// Approves the formulation for production use.
    /// </summary>
    /// <param name="approvedBy">The person approving the formulation</param>
    /// <param name="approvalNotes">Optional approval notes</param>
    /// <returns>Success if approved, or failure if invalid state</returns>
    public Result ApproveFormulation(string approvedBy, string? approvalNotes = null)
    {
        if (string.IsNullOrWhiteSpace(approvedBy))
        {
            return Result.Failure(FormulationErrors.AuthorRequired());
        }

        if (Status != FormulationStatus.UnderReview)
        {
            return Result.Failure(FormulationErrors.CannotApproveFormulation("Formulation must be under review"));
        }

        Status = FormulationStatus.Approved;

        RaiseDomainEvent(new FormulationApprovedDomainEvent(Id, approvedBy, approvalNotes));

        return Result.Success();
    }

    /// <summary>
    /// Rejects the formulation with reasons.
    /// </summary>
    /// <param name="rejectedBy">The person rejecting the formulation</param>
    /// <param name="rejectionReason">The reason for rejection</param>
    /// <returns>Success if rejected, or failure if invalid state</returns>
    public Result RejectFormulation(string rejectedBy, string rejectionReason)
    {
        if (string.IsNullOrWhiteSpace(rejectedBy))
        {
            return Result.Failure(FormulationErrors.AuthorRequired());
        }

        if (string.IsNullOrWhiteSpace(rejectionReason))
        {
            return Result.Failure(FormulationErrors.CannotRejectFormulation("Rejection reason is required"));
        }

        if (Status != FormulationStatus.UnderReview)
        {
            return Result.Failure(FormulationErrors.CannotRejectFormulation("Formulation must be under review"));
        }

        Status = FormulationStatus.Rejected;

        RaiseDomainEvent(new FormulationRejectedDomainEvent(Id, rejectedBy, rejectionReason));

        return Result.Success();
    }

    /// <summary>
    /// Archives the formulation, preventing further modifications.
    /// </summary>
    /// <returns>Success if archived, or failure if already archived</returns>
    public Result ArchiveFormulation()
    {
        if(Status.Equals(FormulationStatus.Archived))
        {
            return Result.Failure(FormulationErrors.FormulationAlreadyArchived());
        }

        RaiseDomainEvent(new FormulationArchivedDomainEvent(Id));
        Status = FormulationStatus.Archived;

        return Result.Success();
    }
}