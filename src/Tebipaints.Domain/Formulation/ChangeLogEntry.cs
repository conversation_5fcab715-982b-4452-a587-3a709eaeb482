namespace Tebipaints.Domain.Formulation;

public sealed record ChangeLogEntry
{
    public DateTime TimeStamp { get; private set; }
    public string Description { get; private set; }
    public string Author { get; private set; }

    private ChangeLogEntry(
        DateTime timeStamp,
        string description,
        string author)
    {
        TimeStamp = timeStamp;
        Description = description;
        Author = author;
    }

    public static ChangeLogEntry Create(
        DateTime timeStamp,
        string description,
        string author
    )
    {
        return new ChangeLogEntry( timeStamp, description, author);
    }
}