using System.Data.Common;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.Production.Enums;
using Tebipaints.Domain.Production.Errors;
using Tebipaints.Domain.Production.Events;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Production;

public class WorkOrder : Entity
{
    private readonly List<Batch> _batches = [];
    private readonly List<BillOfMaterialItem> _billOfMaterials = [];
    
    private WorkOrder(){}
    
    private WorkOrder(
        Guid id,
        string workOrderNumber,
        Guid productId,
        Guid variantId,
        PackagingType packagingType,
        Measurement packagingCapacity,
        Guid formulationId,
        Measurement totalQuantity,
        DateTime startDate,
        DateTime dueDate,
        List<BillOfMaterialItem> billOfMaterials,
        List<Batch> batches) : base(id)
    {
        WorkOrderNumber = workOrderNumber;
        ProductId = productId;
        VariantId = variantId;
        PackagingType = packagingType;
        PackagingCapacity = packagingCapacity;
        FormulationId = formulationId;
        TotalQuantity = totalQuantity;
        StartDate = startDate;
        DueDate = dueDate;
        Status = WorkOrderStatus.Pending;
        _billOfMaterials = billOfMaterials;
        _batches = batches;
    }

    public string WorkOrderNumber { get; private set; }
    public Guid ProductId { get; private set; }
    public Guid VariantId { get; private set; }
    public PackagingType PackagingType { get; private set; }
    public Measurement PackagingCapacity { get; private set; }
    public Guid FormulationId { get; private set; }
    public Measurement TotalQuantity { get; private set; }
    public DateTime StartDate { get; private set; }
    public DateTime DueDate { get; private set; }
    public WorkOrderStatus Status { get; private set; }
    public IReadOnlyCollection<Batch> Batches => _batches.AsReadOnly();
    public IReadOnlyCollection<BillOfMaterialItem> BillOfMaterials => 
        _billOfMaterials.AsReadOnly();

    /// <summary>
    /// Creates a new work order with its associated batches.
    /// </summary>
    /// <param name="workOrderNumber">Unique identifier for the work order</param>
    /// <param name="productId">Product to be manufactured</param>
    /// <param name="packagingCapacity"></param>
    /// <param name="formulationId">Formulation to be used</param>
    /// <param name="totalQuantity">Total quantity to produce</param>
    /// <param name="dueDate">Required completion date</param>
    /// <param name="startDate"></param>
    /// <param name="formulationDetails">Required formulation details for planning</param>
    /// <param name="scheduleConfig"></param>
    /// <param name="variantId"></param>
    /// <param name="packagingType"></param>
    /// <returns>A Result containing the new WorkOrder if valid</returns>
    public static Result<WorkOrder> Create(
        string workOrderNumber,
        Guid productId,
        Guid variantId,
        PackagingType packagingType,
        Measurement packagingCapacity,
        Guid formulationId,
        Measurement totalQuantity,
        DateTime dueDate,
        DateTime startDate,
        FormulationDetails formulationDetails,
        ProductionScheduleConfig? scheduleConfig = null)
    {
        if (string.IsNullOrWhiteSpace(workOrderNumber))
        {
            return Result.Failure<WorkOrder>(WorkOrderErrors.InvalidWorkOrderNumber());
        }

        if (productId == Guid.Empty)
        {
            return Result.Failure<WorkOrder>(WorkOrderErrors.InvalidProductId());
        }

        if (formulationId == Guid.Empty)
        {
            return Result.Failure<WorkOrder>(WorkOrderErrors.InvalidFormulationId());
        }

        if (totalQuantity.Value <= 0)
        {
            return Result.Failure<WorkOrder>(WorkOrderErrors.InvalidQuantity());
        }

        if (dueDate <= DateTime.UtcNow)
        {
            return Result.Failure<WorkOrder>(WorkOrderErrors.InvalidDueDate());
        }
        
        var config = scheduleConfig ?? ProductionScheduleConfig.Default;
        var adjustedStartDate = AdjustToNextWorkingTime(startDate, config);
        
        

        var workOrder = new WorkOrder(
            Guid.NewGuid(),
            workOrderNumber,
            productId,
            variantId,
            packagingType,
            packagingCapacity,
            formulationId,
            totalQuantity,
            adjustedStartDate,
            dueDate,
            [],
            []);
        
        var (batches, bom) = GenerateBatchesAndBom(
            workOrder.Id,
            workOrderNumber,
            totalQuantity,
            formulationDetails,
            adjustedStartDate,
            config);
        
        // add generated batches and BOM
        workOrder._batches.AddRange(batches);
        workOrder._billOfMaterials.AddRange(bom);
        
        workOrder.RaiseDomainEvent(new WorkOrderCreatedDomainEvent(
            workOrder.Id,
            workOrder.WorkOrderNumber,
            workOrder.ProductId,
            workOrder.TotalQuantity,
            workOrder.DueDate,
            batches.Count));
        
        // Validate that the last batch ends before the due date
        var lastBatchEndTime = batches.Max(b => 
            b.PlannedStartDate.Add(formulationDetails.Duration));
            
        if (lastBatchEndTime > dueDate)
        {
            return Result.Failure<WorkOrder>(
                WorkOrderErrors.ScheduleExceedsDueDate(dueDate, lastBatchEndTime));
        }
        
        return Result.Success(workOrder);
    }

    private static (List<Batch> batches, List<BillOfMaterialItem> bom) GenerateBatchesAndBom(
        Guid workOrderId,
        string workOrderNumber,
        Measurement totalQuantity,
        FormulationDetails formulationDetails,
        DateTime startDate,
        ProductionScheduleConfig config)
    {
        
        var numberOfFullBatches = (int)(totalQuantity.Value / formulationDetails.MaxBatchSize.Value);
        
        var batches = new List<Batch>();
        var remainingQuantity = new Measurement( totalQuantity.Value % formulationDetails.MaxBatchSize.Value, totalQuantity.Unit);
        var batchStartDate = DateTime.UtcNow;
        var currentDate = startDate;
        var requiredMaterials = formulationDetails.GetRequiredMaterials;
        var batchNumber = 1;
        
        // Create full-size batches
        for (int i = 0; i < numberOfFullBatches; i++)
        {
            var batchDuration = formulationDetails.Duration + config.CleanupTime;
            
            var batchIngredients = requiredMaterials
                .Select(kvp => new BatchIngredient(
                    kvp.Key,
                    kvp.Value)).ToList();

            var batchResult = Batch.Create(
                workOrderId,
                $"{workOrderNumber}-B{batchNumber:D2}",
                formulationDetails.FormulationId,
                formulationDetails.MaxBatchSize,
                currentDate,
                batchDuration,
                batchIngredients);

            if (batchResult.IsFailure)
            {
                throw new InvalidOperationException(
                    $"Failed to create batch: {batchResult.Error.Name}");
            }
            
            batches.Add(batchResult.Value);
            batchStartDate = batchStartDate.Add(formulationDetails.Duration);
            
            // Calculate next batch start time
            currentDate = AdjustToNextWorkingTime(
                currentDate.Add(formulationDetails.Duration).Add(config.CleanupTime),
                config);
                
            batchNumber++;
        }
        
        // create final batch if there's a remaining quantity
        if (remainingQuantity.Value > 0)
        {
            var scalingFactor = remainingQuantity.Value / formulationDetails.MaxBatchSize.Value;
            
            var batchIngredients = requiredMaterials
                .Select(kvp => new BatchIngredient(
                    kvp.Key,
                    kvp.Value * scalingFactor)).ToList();
            
            var batchResult = Batch.Create(
                workOrderId,
                $"{workOrderNumber}-B{numberOfFullBatches + 1:D2}",
                formulationDetails.FormulationId,
                remainingQuantity,
                currentDate,
                formulationDetails.Duration + config.CleanupTime,
                batchIngredients);

            if (batchResult.IsFailure)
            {
                throw new InvalidOperationException(
                    $"Failed to create batch: {batchResult.Error.Name}");
            }
            
            batches.Add(batchResult.Value);
        }
        
        // Create BOM from total required materials
        var scalingFactorTotal = totalQuantity.Value / formulationDetails.MaxBatchSize.Value;
        var bom = requiredMaterials
            .Select(kvp => new BillOfMaterialItem(
                kvp.Key,
                kvp.Value.WithValue(kvp.Value.Value * scalingFactorTotal)))
            .ToList();
        
        return (batches, bom);
    }

    /// <summary>
    /// Starts the work order production process.
    /// </summary>
    /// <returns>Success if started, or failure if invalid state transition</returns>
    public Result Start()
    {
        if (Status != WorkOrderStatus.Pending)
        {
            return Result.Failure(WorkOrderErrors.InvalidStatusTransition(Status, WorkOrderStatus.InProgress));
        }

        Status = WorkOrderStatus.InProgress;
        
        RaiseDomainEvent(new WorkOrderStartedDomainEvent(Id, WorkOrderNumber));
        
        return Result.Success();
    }

    /// <summary>
    /// Completes the work order after all batches are finished.
    /// </summary>
    /// <returns>Success if completed, or failure if invalid state transition</returns>
    public Result Complete()
    {
        if (Status != WorkOrderStatus.InProgress)
        {
            return Result.Failure(WorkOrderErrors.InvalidStatusTransition(Status, WorkOrderStatus.Completed));
        }

        if (_batches.Any(b => b.Status != BatchStatus.Completed))
        {
            return Result.Failure(WorkOrderErrors.IncompleteBatches());
        }
        
        Status = WorkOrderStatus.Completed;
        
        RaiseDomainEvent(new WorkOrderCompletedDomainEvent(Id, WorkOrderNumber));
        
        return Result.Success();
    }
    
    /// <summary>
    /// Cancels the work order.
    /// </summary>
    /// <param name="reason">Reason for cancellation</param>
    /// <returns>Success if cancelled, or failure if invalid state transition</returns>
    public Result Cancel(string reason)
    {
        if (Status is WorkOrderStatus.Completed or WorkOrderStatus.Cancelled)
        {
            return Result.Failure(WorkOrderErrors.InvalidStatusTransition(
                Status, 
                WorkOrderStatus.Cancelled));
        }

        if (string.IsNullOrWhiteSpace(reason))
        {
            return Result.Failure(WorkOrderErrors.CancellationReasonRequired());
        }

        Status = WorkOrderStatus.Cancelled;
        
        RaiseDomainEvent(new WorkOrderCancelledDomainEvent(
            Id, 
            WorkOrderNumber,
            reason));

        return Result.Success();
    }
    
    private static DateTime GetNextWorkingDay(DateTime date, ProductionScheduleConfig config)
    {
        var currentDate = date.Date;
    
        // If it's after shift end, move to next day
        if (date.TimeOfDay > config.ShiftEndTime)
        {
            currentDate = currentDate.AddDays(1);
        }

        // Find next working day
        while (!config.WorkingDays.Contains(currentDate.DayOfWeek))
        {
            currentDate = currentDate.AddDays(1);
        }

        // Return date at shift start time
        return currentDate.Add(config.ShiftStartTime);
    }
    
    private static DateTime AdjustToNextWorkingTime(
        DateTime dateTime,
        ProductionScheduleConfig config)
    {
        // Ensure we're working with UTC
        var currentDateTime = DateTime.SpecifyKind(dateTime, DateTimeKind.Utc);
    
        // If it's not a working day or it's after hours, move to the next working day
        while (!config.WorkingDays.Contains(currentDateTime.DayOfWeek) || 
               currentDateTime.TimeOfDay >= config.ShiftEndTime)
        {
            // Move to the next day at shift start time
            currentDateTime = currentDateTime.Date.AddDays(1).Add(config.ShiftStartTime);
        }

        // If it's before shift start on a working day, move to shift start
        if (currentDateTime.TimeOfDay < config.ShiftStartTime)
        {
            currentDateTime = currentDateTime.Date.Add(config.ShiftStartTime);
        }

        // Keep checking until we find a working day
        while (!config.WorkingDays.Contains(currentDateTime.DayOfWeek))
        {
            currentDateTime = currentDateTime.AddDays(1).Date.Add(config.ShiftStartTime);
        }

        return currentDateTime;
    }
}