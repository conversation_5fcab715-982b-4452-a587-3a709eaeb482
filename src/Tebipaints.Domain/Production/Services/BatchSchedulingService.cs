using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Production.Enums;
using Tebipaints.Domain.Production.Errors;
using Tebipaints.Domain.Production.Repositories;

namespace Tebipaints.Domain.Production.Services;

public sealed class BatchSchedulingService
{
    private readonly IProductionLineRepository _productionLineRepository;
    private readonly IUnitOfWork _unitOfWork;

    public BatchSchedulingService(IProductionLineRepository productionLineRepository, IUnitOfWork unitOfWork)
    {
        _productionLineRepository = productionLineRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<List<BatchSchedule>>> ScheduleBatches(
        WorkOrder workOrder,
        ProductType productType,
        CancellationToken cancellationToken = default)
    {
        var schedules = new List<BatchSchedule>();
        
        var productionLines = await _productionLineRepository.GetAvailableLines(workOrder.StartDate, cancellationToken);

        if (productionLines.Count == 0)
        {
            return Result.Failure<List<BatchSchedule>>(
                ProductionLineErrors.NoProductionLinesAvailable());
        }
        
        // Filter lines by capability
        var suitableLines = productionLines
            .Where(l => l.Status == ProductionLineStatus.Active &&
                        l.CanProduce(productType))
            .ToList();

        if (!suitableLines.Any())
        {
            return Result.Failure<List<BatchSchedule>>(
                ProductionLineErrors.NoSuitableProductionLine());
        }

        // try schedule each batch
        foreach (var batch in workOrder.Batches)
        {
            var scheduleResult = await TryScheduleBatch(
                batch,
                productType,
                suitableLines,
                workOrder.StartDate,
                workOrder.DueDate,
                cancellationToken);

            if (scheduleResult.IsFailure)
            {
                // Rollback all previous schedules
                foreach (var schedule in schedules)
                {
                    var line = suitableLines.First(l => l.Id == schedule.ProductionLineId);
                    line.UnscheduleBatch(schedule.BatchId);
                }
                
                return Result.Failure<List<BatchSchedule>>(scheduleResult.Error);
            }

            schedules.Add(scheduleResult.Value);
        }
        
        // Validate overall schedule meets due date
        var lastScheduleEnd = schedules.Max(s => s.EndTime);
        if (lastScheduleEnd > workOrder.DueDate)
        {
            // Rollback all schedules
            foreach (var schedule in schedules)
            {
                var line = suitableLines.First(l => l.Id == schedule.ProductionLineId);
                line.UnscheduleBatch(schedule.BatchId);
            }
            
            return Result.Failure<List<BatchSchedule>>(
                ProductionErrors.ScheduleExceedsDueDate(workOrder.DueDate, lastScheduleEnd));
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        return Result.Success(schedules);
    }
    
    private async Task<Result<BatchSchedule>> TryScheduleBatch(
        Batch batch,
        ProductType productType,
        List<ProductionLine> availableLines,
        DateTime earliestStart,
        DateTime dueDate,
        CancellationToken cancellationToken)
    {
        foreach (var line in availableLines)
        {
            var scheduleResult = await TryScheduleBatchOnLine(
                batch,
                line,
                productType,
                earliestStart,
                dueDate,
                cancellationToken);

            if (scheduleResult.IsSuccess)
            {
                return scheduleResult;
            }
        }

        return Result.Failure<BatchSchedule>(
            ProductionErrors.NoAvailableTimeSlot(batch.BatchNumber));
    }
    
    private async Task<Result<BatchSchedule>> TryScheduleBatchOnLine(
        Batch batch,
        ProductionLine line,
        ProductType productType,
        DateTime earliestStart,
        DateTime dueDate,
        CancellationToken cancellationToken)
    {
        var currentStart = earliestStart;
        var batchDuration = batch.EstimatedDuration;

        while (currentStart.Add(batchDuration) <= dueDate)
        {
            var scheduleResult = line.ScheduleBatch(
                batch.Id,
                productType,
                batch.TargetQuantity,
                currentStart,
                currentStart.Add(batchDuration));

            if (scheduleResult.IsSuccess)
            {
                return Result.Success(new BatchSchedule(
                    batch.Id,
                    line.Id,
                    currentStart,
                    currentStart.Add(batchDuration)));
            }

            // Find next potential start time based on existing schedules
            var nextAvailableSlot = line.Schedules
                .Where(s => s.StartTime > currentStart)
                .OrderBy(s => s.StartTime)
                .FirstOrDefault();

            if (nextAvailableSlot is null)
            {
                // No more schedules after current time, try next day
                currentStart = currentStart.AddDays(1).Date;
            }
            else
            {
                // Try scheduling after the next occupied slot
                currentStart = nextAvailableSlot.EndTime;
            }
        }

        return Result.Failure<BatchSchedule>(
            ProductionErrors.NoAvailableTimeSlot(batch.BatchNumber));
    }
}