using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Inventory;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.Production.Enums;
using Tebipaints.Domain.Production.Errors;

namespace Tebipaints.Domain.Production.Services;

public sealed class ProductionInventoryService
{
    /// <summary>
    /// Reserves materials needed for a specific batch.
    /// </summary>
    public Result<List<MaterialReservation>> ReserveMaterialsForBatch(
        Batch batch,
        IReadOnlyDictionary<Guid, MaterialInventory> materialInventories,
        CancellationToken cancellationToken)
    {
        var reservations = new List<MaterialReservation>();

        foreach (var ingredient in batch.Ingredients)
        {
            if (!materialInventories.TryGetValue(ingredient.MaterialId, out var inventory))
            {
                return Result.Failure<List<MaterialReservation>>(
                    ProductionErrors.MaterialNotFound(ingredient.MaterialId));
            }

            var reservationResult = inventory.ReserveStock(
                (double)ingredient.RequiredQuantity.Value,
                $"Batch: {batch.BatchNumber}");

            if (reservationResult.IsFailure)
            {
                // Rollback previous reservations
                RollbackReservations(reservations, materialInventories);
                return Result.Failure<List<MaterialReservation>>(reservationResult.Error);
            }

            reservations.Add(reservationResult.Value);
        }

        return Result.Success(reservations);
    }

    /// <summary>
    /// Consumes materials for a completed batch.
    /// </summary>
    public Result ConsumeMaterials(
        Batch batch,
        IReadOnlyDictionary<Guid, MaterialInventory> materialInventories,
        CancellationToken cancellationToken)
    {
        foreach (var ingredient in batch.Ingredients)
        {
            if (!materialInventories.TryGetValue(ingredient.MaterialId, out var inventory))
            {
                return Result.Failure(ProductionErrors.MaterialNotFound(
                    ingredient.MaterialId));
            }

            var consumeResult = inventory.RemoveStock(
                (double)ingredient.RequiredQuantity.Value,
                $"Batch: {batch.BatchNumber}");

            if (consumeResult.IsFailure)
            {
                return Result.Failure(consumeResult.Error);
            }
        }

        return Result.Success();
    }
    
    /// <summary>
    /// Records produced product into inventory.
    /// </summary>
    public Result RecordProduction(
        Batch batch,
        ProductInventory productInventory,
        Guid variantId,
        PackagingOption packagingOption,
        CancellationToken cancellationToken)
    {
        if (batch.Status != BatchStatus.Completed)
        {
            return Result.Failure(ProductionErrors.BatchNotCompleted(batch.BatchNumber));
        }

        if (!batch.ActualYield.HasValue)
        {
            return Result.Failure(ProductionErrors.BatchYieldNotRecorded(batch.BatchNumber));
        }
        
        var quantity = (int)Math.Floor(batch.ActualYield!.Value);
        
        if (quantity <= 0)
        {
            return Result.Failure(ProductionErrors.InvalidProductionQuantity());
        }

        var result = productInventory.AddVariantStock(
            variantId,
            packagingOption,
            quantity,
            $"Batch: {batch.BatchNumber}");

        if (result.IsFailure)
        {
            return Result.Failure(result.Error);
        }

        return Result.Success();
    }

    /// <summary>
    /// Releases material reservations if work order is cancelled.
    /// </summary>
    public Result ReleaseReservations(
        List<MaterialReservation> reservations,
        IReadOnlyDictionary<Guid, MaterialInventory> materialInventories,
        CancellationToken cancellationToken)
    {
        foreach (var reservation in reservations)
        {
            if (materialInventories.TryGetValue(reservation.MaterialId, out var inventory))
            {
                var result = inventory.CancelReservation(reservation.Quantity, $"Released: {reservation.Purpose}");
                if (result.IsFailure)
                {
                    return Result.Failure(result.Error);
                }
            }
        }

        return Result.Success();
    }

    private void RollbackReservations(
        List<MaterialReservation> reservations,
        IReadOnlyDictionary<Guid, MaterialInventory> materialInventories)
    {
        foreach (var reservation in reservations)
        {
            if (materialInventories.TryGetValue(reservation.MaterialId, out var inventory))
            {
                 inventory.CancelReservation(reservation.Quantity, $"Released: {reservation.Purpose}");
            }
        }
    }
}