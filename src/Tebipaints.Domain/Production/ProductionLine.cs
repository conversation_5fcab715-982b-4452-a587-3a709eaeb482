using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Production.Enums;
using Tebipaints.Domain.Production.Errors;
using Tebipaints.Domain.Production.Events;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Production;

public sealed class ProductionLine : Entity
{
    private readonly List<ProductionCapability> _capabilities = [];
    private readonly List<ProductType> _productTypes = [];
    private readonly List<BatchSchedule> _schedules = [];

    private ProductionLine(){}
    
    private ProductionLine(
        Guid id,
        string name,
        string description,
        ProductionLineStatus status,
        List<ProductionCapability> capabilities) : base(id)
    {
        Name = name;
        Description = description;
        Status = status;
        _capabilities = capabilities;
    }
    
    public string Name { get; private set; }
    public string Description { get; private set; }
    public ProductionLineStatus Status { get; private set; }
    public IReadOnlyCollection<ProductionCapability> Capabilities => _capabilities.AsReadOnly();
    public IReadOnlyCollection<ProductType> SupportedProductTypes => _productTypes.AsReadOnly();
    public IReadOnlyCollection<BatchSchedule> Schedules => _schedules.AsReadOnly();
    
    /// <summary>
    /// Creates a new production line.
    /// </summary>
    public static Result<ProductionLine> Create(
        string name,
        string description,
        List<ProductionCapability> capabilities)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            return Result.Failure<ProductionLine>(
                ProductionLineErrors.InvalidName());
        }

        if (capabilities.Count == 0)
        {
            return Result.Failure<ProductionLine>(
                ProductionLineErrors.NoCapabilitiesSpecified());
        }

        var productionLine = new ProductionLine(
            Guid.NewGuid(),
            name.Trim(),
            description?.Trim() ?? string.Empty,
            ProductionLineStatus.Active,
            capabilities);

        productionLine.RaiseDomainEvent(new ProductionLineCreatedDomainEvent(
            productionLine.Id,
            productionLine.Name));

        return Result.Success(productionLine);
    }
    
    public Result ScheduleBatch(
        Guid batchId,
        ProductType productType,
        Measurement batchSize,
        DateTime startTime,
        DateTime endTime)
    {
        if (Status != ProductionLineStatus.Active)
        {
            return Result.Failure(ProductionLineErrors.LineInactive());
        }

        if (startTime >= endTime)
        {
            return Result.Failure(ProductionLineErrors.InvalidScheduleTime());
        }

        if (!CanProduce(productType))
        {
            return Result.Failure(ProductionLineErrors.UnsupportedProductType(productType));
        }

        if (!CanHandleBatchSize(batchSize))
        {
            return Result.Failure(ProductionLineErrors.BatchSizeOutOfRange(batchSize));
        }

        if (HasScheduleConflict(startTime, endTime))
        {
            return Result.Failure(ProductionLineErrors.ScheduleConflict());
        }

        var schedule = new BatchSchedule(
            batchId,
            Id,
            startTime,
            endTime);

        _schedules.Add(schedule);

        RaiseDomainEvent(new BatchScheduledDomainEvent(
            Id,
            batchId,
            startTime,
            endTime));

        return Result.Success();
    }
    
    public Result UnscheduleBatch(Guid batchId)
    {
        var schedule = _schedules.FirstOrDefault(s => s.BatchId == batchId);
        
        if (schedule is null)
        {
            return Result.Failure(ProductionLineErrors.ScheduleNotFound());
        }

        _schedules.Remove(schedule);

        RaiseDomainEvent(new BatchUnscheduledDomainEvent(
            Id,
            batchId));

        return Result.Success();
    }
    
    public Result UpdateSchedule(
        Guid batchId,
        DateTime newStartTime,
        DateTime newEndTime)
    {
        if (Status != ProductionLineStatus.Active)
        {
            return Result.Failure(ProductionLineErrors.LineInactive());
        }

        var existingSchedule = _schedules.FirstOrDefault(s => s.BatchId == batchId);
        
        if (existingSchedule is null)
        {
            return Result.Failure(ProductionLineErrors.ScheduleNotFound());
        }

        if (newStartTime >= newEndTime)
        {
            return Result.Failure(ProductionLineErrors.InvalidScheduleTime());
        }

        if (HasScheduleConflict(newStartTime, newEndTime, batchId))
        {
            return Result.Failure(ProductionLineErrors.ScheduleConflict());
        }

        _schedules.Remove(existingSchedule);
        
        var updatedSchedule = new BatchSchedule(
            batchId,
            Id,
            newStartTime,
            newEndTime);

        _schedules.Add(updatedSchedule);

        RaiseDomainEvent(new BatchScheduleUpdatedDomainEvent(
            Id,
            batchId,
            newStartTime,
            newEndTime));

        return Result.Success();
    }
    
    /// <summary>
    /// Checks if the line can handle a specific product type.
    /// </summary>
    public bool CanProduce(ProductType productType) =>
        _capabilities.Any(c => c.ProductType == productType);

    /// <summary>
    /// Checks if the line can handle a specific batch size.
    /// </summary>
    public bool CanHandleBatchSize(Measurement batchSize)
    {
        var capability = _capabilities.FirstOrDefault(
            c => c.MinBatchSize <= batchSize && 
                 c.MaxBatchSize >= batchSize);
        
        return capability is not null;
    }
    
    private bool HasScheduleConflict(
        DateTime startTime,
        DateTime endTime,
        Guid? excludeBatchId = null)
    {
        return _schedules.Any(s =>
            s.BatchId != excludeBatchId &&
            !(endTime <= s.StartTime || startTime >= s.EndTime));
    }
    
    /// <summary>
    /// Deactivates the production line.
    /// </summary>
    public Result Deactivate(string reason)
    {
        if (Status == ProductionLineStatus.Inactive)
        {
            return Result.Failure(ProductionLineErrors.AlreadyInactive());
        }

        Status = ProductionLineStatus.Inactive;

        RaiseDomainEvent(new ProductionLineDeactivatedDomainEvent(
            Id,
            Name,
            reason));

        return Result.Success();
    }
    
    /// <summary>
    /// Activates the production line.
    /// </summary>
    public Result Activate()
    {
        if (Status == ProductionLineStatus.Active)
        {
            return Result.Failure(ProductionLineErrors.AlreadyActive());
        }

        Status = ProductionLineStatus.Active;

        RaiseDomainEvent(new ProductionLineActivatedDomainEvent(
            Id, Name));

        return Result.Success();
    }
}



