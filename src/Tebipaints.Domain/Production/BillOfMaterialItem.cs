using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Production;

public sealed record BillOfMaterialItem
{
    private BillOfMaterialItem() { }

    public BillOfMaterialItem(Guid materialId, Measurement requiredQuantity)
    {
        MaterialId = materialId;
        RequiredQuantity = requiredQuantity;
    }
    
    public Guid MaterialId { get; private init; }
    public Measurement RequiredQuantity { get; private init; }
}