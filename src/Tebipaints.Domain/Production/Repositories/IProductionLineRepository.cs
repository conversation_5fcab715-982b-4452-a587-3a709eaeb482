using Tebipaints.Domain.Production.Enums;

namespace Tebipaints.Domain.Production.Repositories;

public interface IProductionLineRepository
{
    /// <summary>
    /// Gets all production lines available from a specific date.
    /// </summary>
    Task<List<ProductionLine>> GetAvailableLines(
        DateTime fromDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a production line is occupied during a specific time period.
    /// </summary>
    Task<bool> IsLineOccupied(
        Guid lineId,
        DateTime startTime,
        TimeSpan duration,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a production line by its ID.
    /// </summary>
    Task<ProductionLine?> GetByIdAsync(
        Guid id,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all production lines.
    /// </summary>
    Task<List<ProductionLine>> GetAllAsync(
        CancellationToken cancellationToken = default);
    
    Task<ProductionLine?> GetDefaultLineForProductTypeAsync(
        ProductType productType, 
        CancellationToken cancellationToken = default);
        
    Task<IReadOnlyList<ProductionLine>> GetAvailableLinesForProductTypeAsync(
        ProductType productType,
        DateTime startDate,
        CancellationToken cancellationToken = default);
    
    void Add(ProductionLine line);
}