using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Production.Enums;
using Tebipaints.Domain.Production.Errors;
using Tebipaints.Domain.Production.Events;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Production;

public sealed class Batch : Entity
{
    private readonly List<BatchIngredient> _ingredients = [];
    private readonly List<QualityCheck> _qualityChecks = [];
    private readonly List<ProductionLoss> _productionLosses = [];
    
    private Batch(){}
    
    private Batch(
        Guid id,
        Guid workOrderId,
        string batchNumber,
        Guid formulationId,
        Measurement targetQuantity,
        BatchStatus status,
        DateTime plannedStartDate,
        TimeSpan estimatedDuration,
        List<BatchIngredient> ingredients) : base(id)
    {
        BatchNumber = batchNumber;
        FormulationId = formulationId;
        WorkOrderId = workOrderId;
        TargetQuantity = targetQuantity;
        Status = status;
        PlannedStartDate = plannedStartDate;
        EstimatedDuration = estimatedDuration;
        _ingredients = ingredients;
    }
    
    public string BatchNumber { get; private set; }
    public Guid FormulationId { get; private set; }
    public Guid WorkOrderId { get; private set; }
    public Measurement TargetQuantity { get; private set; }
    public BatchStatus Status { get; private set; }
    public DateTime PlannedStartDate { get; private set; }
    public DateTime? ActualStartDate { get; private set; }
    public DateTime? CompletionDate { get; private set; }
    public TimeSpan EstimatedDuration { get; private set; }
    public TimeSpan? ActualDuration { get; private set; }
    public double? ActualYield { get; private set; }
    public string? Notes { get; private set; }
    public IReadOnlyCollection<BatchIngredient> Ingredients => _ingredients.AsReadOnly();
    public IReadOnlyCollection<QualityCheck> QualityChecks => _qualityChecks.AsReadOnly();
    public IReadOnlyList<ProductionLoss> ProductionLosses => _productionLosses.AsReadOnly();
    public bool HasPassedQualityControl => _qualityChecks.All(qc => qc.HasPassed);
    public double TotalLoss => _productionLosses.Sum(l => l.Quantity);
    
    public static Result<Batch> Create(
        Guid workOrderId,
        string batchNumber,
        Guid formulationId,
        Measurement targetQuantity,
        DateTime plannedStartDate,
        TimeSpan estimatedDuration,
        List<BatchIngredient> ingredients)
    {
        if (string.IsNullOrWhiteSpace(batchNumber))
        {
            return Result.Failure<Batch>(BatchErrors.InvalidBatchNumber());
        }

        if (formulationId == Guid.Empty)
        {
            return Result.Failure<Batch>(BatchErrors.InvalidFormulationId());
        }

        if (targetQuantity.Value <= 0)
        {
            return Result.Failure<Batch>(BatchErrors.InvalidTargetQuantity());
        }

        if (plannedStartDate < DateTime.UtcNow.Date)
        {
            return Result.Failure<Batch>(BatchErrors.InvalidPlannedStartDate());
        }

        if (estimatedDuration <= TimeSpan.Zero)
        {
            return Result.Failure<Batch>(BatchErrors.InvalidEstimatedDuration());
        }

        if (!ingredients.Any())
        {
            return Result.Failure<Batch>(BatchErrors.NoIngredientsSpecified());
        }

        var batch = new Batch(
            Guid.NewGuid(),
            workOrderId,
            batchNumber.Trim(),
            formulationId,
            targetQuantity,
            BatchStatus.Planned,
            plannedStartDate,
            estimatedDuration,
            ingredients);

        batch.RaiseDomainEvent(new BatchCreatedDomainEvent(
            batch));

        return Result.Success(batch);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public Result StartProduction()
    {
        if (Status != BatchStatus.Planned)
        {
            return Result.Failure(BatchErrors.InvalidStatusTransition());
        }

        Status = BatchStatus.InProgress;
        ActualStartDate = DateTime.UtcNow;
        
        RaiseDomainEvent(new BatchProductionStartedDomainEvent(
            Id, BatchNumber, ActualStartDate.Value));
        
        return Result.Success();
    }
    
    public Result RecordQualityCheck(
        string parameter,
        double measuredValue,
        double minimumValue,
        double maximumValue,
        string checkedBy)
    {
        if (Status != BatchStatus.InProgress)
        {
            return Result.Failure(BatchErrors.InvalidStatusForQualityCheck());
        }

        var qualityCheck = QualityCheck.Create(
            parameter,
            measuredValue,
            minimumValue,
            maximumValue,
            checkedBy);

        _qualityChecks.Add(qualityCheck);

        RaiseDomainEvent(new BatchQualityCheckRecordedDomainEvent(
            Id,
            BatchNumber,
            parameter,
            qualityCheck.HasPassed, DateTime.UtcNow));

        return Result.Success();
    }
    
    public Result RecordProductionLoss(
        double quantity,
        ProductionLossType lossType,
        string reason)
    {
        if (quantity <= 0)
        {
            return Result.Failure(BatchErrors.InvalidLossQuantity());
        }

        var loss = new ProductionLoss(
            quantity,
            lossType,
            reason,
            DateTime.UtcNow);

        _productionLosses.Add(loss);

        RaiseDomainEvent(new BatchProductionLossRecordedDomainEvent(
            Id,
            BatchNumber,
            quantity,
            lossType,
            reason));

        return Result.Success();
    }
    
    public Result CompleteProduction(double actualYield)
    {
        if (Status != BatchStatus.InProgress)
        {
            return Result.Failure(BatchErrors.InvalidStatusTransition(
                Status, 
                BatchStatus.Completed));
        }

        if (actualYield <= 0)
        {
            return Result.Failure(BatchErrors.InvalidActualYield());
        }

        if (!_qualityChecks.Any())
        {
            return Result.Failure(BatchErrors.NoQualityChecksPerformed());
        }

        if (!HasPassedQualityControl)
        {
            return Result.Failure(BatchErrors.QualityCheckNotPassed());
        }

        // Validate if actual yield makes sense considering losses
        var expectedYield = (double)TargetQuantity.Value - TotalLoss;
        var yieldVariance = Math.Abs(actualYield - expectedYield);
        var maxAllowedVariance = expectedYield * 0.05; // 5% tolerance

        if (yieldVariance > maxAllowedVariance)
        {
            return Result.Failure(BatchErrors.YieldVarianceTooHigh(
                actualYield,
                expectedYield));
        }

        ActualYield = actualYield;
        Status = BatchStatus.Completed;
        CompletionDate = DateTime.UtcNow;

        RaiseDomainEvent(new BatchCompletedDomainEvent(
            Id,
            WorkOrderId,
            BatchNumber,
            TargetQuantity,
            ActualYield.Value,
            TotalLoss,
            ActualDuration,
            CompletionDate));

        return Result.Success();
    }
    
    public Result Cancel(string reason)
    {
        if (Status is BatchStatus.Completed or BatchStatus.Cancelled)
        {
            return Result.Failure(BatchErrors.InvalidStatusTransition());
        }

        if (string.IsNullOrWhiteSpace(reason))
        {
            return Result.Failure(BatchErrors.CancellationReasonRequired());
        }

        Status = BatchStatus.Cancelled;
        Notes = reason.Trim();

        RaiseDomainEvent(new BatchCancelledDomainEvent(
            Id,
            BatchNumber,
            reason));

        return Result.Success();
    }

    public Result MarkMaterialShortage(string reason)
    {
        if (Status is BatchStatus.Completed or BatchStatus.Cancelled)
        {
            return Result.Failure(BatchErrors.InvalidStatusTransition());
        }

        if (string.IsNullOrWhiteSpace(reason))
        {
            return Result.Failure(BatchErrors.CancellationReasonRequired());
        }

        Status = BatchStatus.MaterialShortage;
        Notes = reason.Trim();
        
        return Result.Success();
    }
}