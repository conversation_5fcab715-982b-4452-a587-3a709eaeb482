using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Production;

public sealed record FormulationDetails
{
    private readonly Dictionary<Guid, Measurement> _ingredientQuantities;
    private readonly Dictionary<Guid, MaterialDetails> _materialDetails;

    private FormulationDetails(){}
    
    public FormulationDetails(
        Guid formulationId,
        Measurement maxBatchSize,
        TimeSpan duration,
        Dictionary<Guid, Measurement> ingredientQuantities,
        Dictionary<Guid, MaterialDetails> materialDetails)
    {
        FormulationId = formulationId;
        MaxBatchSize = maxBatchSize;
        Duration = duration;
        _ingredientQuantities = ingredientQuantities;
        _materialDetails = materialDetails;
    }
    
    public Guid FormulationId { get; }
    public Measurement MaxBatchSize { get; } 
    public TimeSpan Duration { get; }
    
    public Dictionary<Guid, Measurement> GetRequiredMaterials => new(_ingredientQuantities);
    
    public BatchIngredient CreateBatchIngredient(Guid materialId, Measurement quantity)
    {
        var details = _materialDetails[materialId];
        return new BatchIngredient(
            materialId,
            quantity, 0);
    }
}