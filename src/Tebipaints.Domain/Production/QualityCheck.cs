using Tebipaints.Domain.Production.Enums;

namespace Tebipaints.Domain.Production;

public sealed record QualityCheck
{
    private QualityCheck(){}
    
    private QualityCheck(
        string parameter,
        double measuredValue,
        double minimumValue,
        double maximumValue,
        string checkedBy,
        DateTime checkedAt)
    {
        Parameter = parameter;
        MeasuredValue = measuredValue;
        MinimumValue = minimumValue;
        MaximumValue = maximumValue;
        CheckedBy = checkedBy;
        CheckedAt = checkedAt;
    }

    public string Parameter { get; }
    public double MeasuredValue { get; }
    public double MinimumValue { get; }
    public double MaximumValue { get; }
    public string CheckedBy { get; }
    public DateTime CheckedAt { get; }
    public bool HasPassed => 
        MeasuredValue >= MinimumValue && 
        MeasuredValue <= MaximumValue;

    public static QualityCheck Create(
        string parameter,
        double measuredValue,
        double minimumValue,
        double maximumValue,
        string checkedBy) =>
        new(
            parameter,
            measuredValue,
            minimumValue,
            maximumValue,
            checkedBy,
            DateTime.UtcNow);
}
    