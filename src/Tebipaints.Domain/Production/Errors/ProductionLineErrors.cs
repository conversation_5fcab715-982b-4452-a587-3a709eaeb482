using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Production.Enums;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Production.Errors;

public class ProductionLineErrors
{
    public static Error InvalidName() =>
        new("ProductionLine.InvalidName", 
            "Production line name must be provided.");
    
    public static Error NotFound(Guid id) =>
        new("ProductionLine.NotFound", 
            $"Production line with id {id} was not found.");

    public static Error NoCapabilitiesSpecified() =>
        new("ProductionLine.NoCapabilities", 
            "At least one capability must be specified.");

    public static Error InvalidMinBatchSize() =>
        new("ProductionLine.InvalidMinBatchSize", 
            "Minimum batch size must be greater than zero.");

    public static Error InvalidMaxBatchSize() =>
        new("ProductionLine.InvalidMaxBatchSize", 
            "Maximum batch size must be greater than minimum batch size.");

    public static Error AlreadyActive() =>
        new("ProductionLine.AlreadyActive", 
            "Production line is already active.");

    public static Error AlreadyInactive() =>
        new("ProductionLine.AlreadyInactive", 
            "Production line is already inactive.");
    
    public static Error NoProductionLinesAvailable() =>
        new("ProductionLine.NoProductionLinesAvailable", 
            "Production line must be specified.");
    
    public static Error NoSuitableProductionLine() =>
        new("ProductionLine.NoSuitableProductionLine",
            "No suitable production line is available.");
    
    public static Error ProductTypeNotSupported(ProductType type) =>
        new("ProductionLine.ProductTypeNotSupported",
            $"This product type {type} is not supported by this line.");
    
    public static Error InvalidScheduleTime() => new(
        "ProductionLine.InvalidScheduleTime",
        "End time must be after start time");

    public static Error ScheduleConflict() => new(
        "ProductionLine.ScheduleConflict",
        "The requested schedule conflicts with existing schedules");

    public static Error ScheduleNotFound() => new(
        "ProductionLine.ScheduleNotFound",
        "No schedule found for the specified batch");

    public static Error BatchSizeOutOfRange(Measurement batchSize) => new(
        "ProductionLine.BatchSizeOutOfRange",
        $"Batch size {batchSize} is outside the supported range for this production line");

    public static Error UnsupportedProductType(ProductType productType) => new(
        "ProductionLine.UnsupportedProductType",
        $"Product type {productType} is not supported by this production line");

    public static Error LineInactive() => new(
        "ProductionLine.LineInactive",
        "Production line is inactive.");
}