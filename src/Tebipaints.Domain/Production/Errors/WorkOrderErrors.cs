using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Production.Enums;

namespace Tebipaints.Domain.Production.Errors;

public class WorkOrderErrors
{
    public static Error InvalidWorkOrderNumber() =>
        new("WorkOrder.InvalidWorkOrderNumber", 
            "Work order number must be provided.");

    public static Error InvalidProductId() =>
        new("WorkOrder.InvalidProductId", 
            "Valid product ID must be provided.");

    public static Error InvalidFormulationId() =>
        new("WorkOrder.InvalidFormulationId", 
            "Valid formulation ID must be provided.");

    public static Error InvalidQuantity() =>
        new("WorkOrder.InvalidQuantity", 
            "Total quantity must be greater than zero.");

    public static Error InvalidDueDate() =>
        new("WorkOrder.InvalidDueDate", 
            "Due date must be in the future.");

    public static Error IncompleteBatches() =>
        new("WorkOrder.IncompleteBatches", 
            "All batches must be completed before completing the work order.");

    public static Error CancellationReasonRequired() =>
        new("WorkOrder.CancellationReasonRequired", 
            "A reason must be provided when cancelling a work order.");

    public static Error InvalidStatusTransition(
        WorkOrderStatus currentStatus, 
        WorkOrderStatus newStatus) =>
        new("WorkOrder.InvalidStatusTransition", 
            $"Cannot transition from {currentStatus} to {newStatus}.");

    public static Error ScheduleExceedsDueDate(DateTime dueDate, DateTime lastBatchEndTime) =>
        new("WorkOrder.ScheduleExceedsDueDate",
            $"The due date must be greater than {lastBatchEndTime}.");
    
    public static Error MaterialsNotFound( ) =>
        new("WorkOrder.MaterialsNotFound", 
            "List of ingredients not found");

    public static Error NotFound() =>
        new("WorkOrder.NotFound", "The work order does not exist.");

    public static Error InvalidPackagingType() => new(
        "WorkOrder.InvalidPackagingType",
        "The packaging type is not supported.");
}
