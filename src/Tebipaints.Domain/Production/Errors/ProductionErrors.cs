using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Domain.Production.Errors;

public class ProductionErrors
{
    public static Error MaterialNotFound(Guid materialId) =>
        new("ProductionLine.InvalidName", 
            $"Material {materialId} not found");
    
    public static Error WorkOrderNotFound(Guid workOrderId) =>
        new("Production.WorkOrderNotFound", 
            $"The work order with id {workOrderId} could not be found.");
    
    public static Error BatchNotFound(Guid batchId) =>
        new("Production.BatchNotFound", 
            $"The batch order with id {batchId} could not be found.");
    
    public static Error InvalidLossType(string lossType) =>
        new("Production.InvalidLossType", 
            $"{lossType} isn't a valid loss type.");

    public static Error InvalidProductionQuantity() => new(
        "Production.InvalidProductionQuantity",
        "The production quantity is invalid.");

    public static Error BatchNotCompleted(string batchBatchNumber) => new(
        "Production.BatchNotCompleted",
        $"The batch number {batchBatchNumber} is not completed.");

    public static Error BatchYieldNotRecorded(string batchBatchNumber) => new(
        "Production.BatchYieldNotRecorded",
        "The batch number {batchBatchNumber} has no yield recorded.");

    public static Error NoAvailableTimeSlot(string batchBatchNumber) => new(
        "Production.NoAvailableTimeSlot",
        $"There is no available time slot for batch {batchBatchNumber}.");

    public static Error ScheduleExceedsDueDate(
        DateTime workOrderDueDate,
        DateTime lastScheduleEnd) => new(
        "Production.ScheduleExceedsDueDate",
        $"The last schedule end date {lastScheduleEnd.ToShortDateString()} exceeds the due date of the work order {workOrderDueDate}.");
}