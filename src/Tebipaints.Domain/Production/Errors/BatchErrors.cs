using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Production.Enums;

namespace Tebipaints.Domain.Production.Errors;

public class BatchErrors
{
    public static Error InvalidTargetQuantity() => new(
        "Batch.InvalidTargetQuantity",
        "The quantity must be a positive integer and greater than zero.");
    
    public static Error InvalidPlannedStartDate() => new(
        "Batch.InvalidPlannedStartDate",
        "The planned start date cannot be earlier than today.");
    
    public static Error InvalidEstimatedDuration() => new(
        "Batch.InvalidEstimatedDuration",
        "The duration must be a positive integer and greater than zero.");
    
    public static Error NoIngredientsSpecified() => new(
        "Batch.NoIngredientsSpecified",
        "There are no ingredients specified.");
    
    public static Error InvalidFormulationId() => new(
        "Batch.InvalidFormulationId",
        "The id specified is not a valid GUID.");
    
    public static Error InvalidBatchNumber() => new(
        "Batch.InvalidBatchNumber",
        "The batch number is not in a valid format.");

    public static Error InvalidStatusTransition() => new(
        "Batch.InvalidStatusTransition",
        "");
    
    public static Error InvalidStatusTransition(BatchStatus fromStatus, BatchStatus ToStatus) =>
        new("Batch.InvalidStatusTransition",
        $"Status transition from {fromStatus} to {ToStatus} is not supported.");

    public static Error InvalidActualYield() => new(
        "Batch.InvalidActualYield",
        "The supplied figure must be a positive integer and greater than zero.");

    public static Error BatchNotStarted() => new(
        "Batch.BatchNotStarted",
        "The batch has not been started.");

    public static Error CannotAddQualityCheck() => new(
        "Batch.CannotAddQualityCheck",
        "");

    public static Error CancellationReasonRequired() => new(
        "Batch.CancellationReasonRequired",
        "");

    public static Error InvalidStatusForQualityCheck() => new(
        "Batch.InvalidStatusForQualityCheck",
        "The batch should already be in processing");
    
    public static Error InvalidLossQuantity() => new(
        "Batch.InvalidLossQuantity",
        "Quantity must be a positive integer and greater than zero.");
    
    public static Error QualityCheckNotPassed() => new(
        "Batch.QualityCheckNotPassed",
        "One or more quality checks have failed");
    
    public static Error NoQualityChecksPerformed() => new(
        "Batch.NoQualityChecksPerformed",
        "No quality checks have been performed on this batch");
    
    public static Error YieldVarianceTooHigh(double actual, double expected) => new(
        "Batch.InvalidLossQuantity",
        $"The yield variance between the actual: {actual} and the expected: {expected} is too large.");
    
}