using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Production;

public sealed record BatchIngredient
{
    // Private parameterless constructor for EF Core
    private BatchIngredient() { }

    public BatchIngredient(
        Guid materialId,
        Measurement requiredQuantity,
        double? actualQuantity = null)
    {
        MaterialId = materialId;
        RequiredQuantity = requiredQuantity;
        ActualQuantity = actualQuantity;
    }

    public Guid MaterialId { get; private init; }
    public Measurement RequiredQuantity { get; private init; }
    public double? ActualQuantity { get; private init; }
}