using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Production.Enums;
using Tebipaints.Domain.Production.Errors;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Production;

public sealed record ProductionCapability
{
    private ProductionCapability(){}
    
    private ProductionCapability(
        ProductType productType,
        Measurement minBatchSize,
        Measurement maxBatchSize)
    {
        ProductType = productType;
        MinBatchSize = minBatchSize;
        MaxBatchSize = maxBatchSize;
    }
    
    public ProductType ProductType { get; }
    public Measurement MinBatchSize { get; }
    public Measurement MaxBatchSize { get; }

    public static Result<ProductionCapability> Create(
        ProductType productType,
        Measurement minBatchSize,
        Measurement maxBatchSize)
    {
        if (minBatchSize.Value <= 0)
        {
            return Result.Failure<ProductionCapability>(
                ProductionLineErrors.InvalidMinBatchSize());
        }

        if (maxBatchSize <= minBatchSize)
        {
            return Result.Failure<ProductionCapability>(
                ProductionLineErrors.InvalidMaxBatchSize());
        }

        return Result.Success(new ProductionCapability(
            productType,
            minBatchSize,
            maxBatchSize));
    }
}