namespace Tebipaints.Domain.Production;

public record WorkOrderNumber
{
    private const string Prefix = "TBP-WO";
    private const int SequenceLength = 6;
    
    public string Value { get; }
    
    private WorkOrderNumber(string value) => Value = value;

    public static WorkOrderNumber Create(int sequence, DateTime? date = null)
    {
        date ??= DateTime.UtcNow;
        string datePart = date.Value.ToString("yyyyMM");
        string sequencePart = sequence.ToString($"D{SequenceLength}");
        
        return new WorkOrderNumber($"{Prefix}-{datePart}-{sequencePart}");
    }
    
    public static bool TryParse(string value, out WorkOrderNumber? number)
    {
        number = null;
        if (string.IsNullOrWhiteSpace(value)) return false;
        
        var pattern = $@"^{Prefix}-(\d{{6}})-(\d{{{SequenceLength}}})$";
        var match = System.Text.RegularExpressions.Regex.Match(value, pattern);
        
        if (!match.Success) return false;

        number = new WorkOrderNumber(value);

        return true;
    }
    
    public override string ToString() => Value;
}