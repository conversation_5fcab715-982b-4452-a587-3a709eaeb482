namespace Tebipaints.Domain.Production;

public record MaterialDetails(
    Guid MaterialId,
    string Name,
    string PreferredUnit,
    double UnitConversionFactor = 1.0)
{
    public double ConvertQuantity(double quantity, string fromUnit)
    {
        if (fromUnit == PreferredUnit) return quantity;
        // Add unit conversion logic here
        return quantity * UnitConversionFactor;
    }
}