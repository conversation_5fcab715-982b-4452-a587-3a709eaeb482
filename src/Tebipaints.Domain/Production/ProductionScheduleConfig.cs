namespace Tebipaints.Domain.Production;

public sealed record ProductionScheduleConfig
{
    private ProductionScheduleConfig(){}
    
    public ProductionScheduleConfig(
        TimeSpan shiftStartTime,
        TimeSpan shiftEndTime,
        DayOfWeek[] workingDays,
        TimeSpan cleanupTime)
    {
        ShiftStartTime = shiftStartTime;
        ShiftEndTime = shiftEndTime;
        WorkingDays = workingDays;
        CleanupTime = cleanupTime;
    }

    // Single shift configuration (e.g., 8 AM to 5 PM)
    public TimeSpan ShiftStartTime { get; }
    public TimeSpan ShiftEndTime { get; }
    public DayOfWeek[] WorkingDays { get; }
    public TimeSpan CleanupTime { get; }

    public static ProductionScheduleConfig Default => new(
        TimeSpan.FromHours(8),  // 8 AM
        TimeSpan.FromHours(17), // 5 PM
        new[] { 
            DayOfWeek.Monday, 
            DayOfWeek.Tuesday, 
            DayOfWeek.Wednesday, 
            DayOfWeek.Thursday, 
            DayOfWeek.Friday 
        },
        TimeSpan.FromMinutes(30));

    public TimeSpan AvailableHoursPerDay => ShiftEndTime - ShiftStartTime;
}