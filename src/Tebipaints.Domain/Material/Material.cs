using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Material.Enums;
using Tebipaints.Domain.Material.Errors;
using Tebipaints.Domain.Material.Events;

namespace Tebipaints.Domain.Material;

public sealed class Material : Entity
{
    private Material(){}
    
    private Material(
        Guid id,
        string code,
        string name,
        string description,
        MaterialType type,
        string defaultUnit,
        MaterialStatus status) : base(id)
    {
        Code = code;
        Name = name;
        Description = description;
        Type = type;
        DefaultUnit = defaultUnit;
        Status = status;
    }
    
    public string Code { get; private set; }
    public string Name { get; private set; }
    public string Description { get; private set; }
    public MaterialType Type { get; private set; }
    public string DefaultUnit { get; private set; }
    public MaterialStatus Status { get; private set; }
    
    public static Result<Material> Create(
        string code,
        string name,
        string description,
        MaterialType type,
        string defaultUnit)
    {
        if (string.IsNullOrWhiteSpace(code))
            return Result.Failure<Material>(MaterialErrors.InvalidCode());

        if (string.IsNullOrWhiteSpace(name))
            return Result.Failure<Material>(MaterialErrors.InvalidName());

        if (string.IsNullOrWhiteSpace(defaultUnit))
            return Result.Failure<Material>(MaterialErrors.InvalidUnit());

        var material = new Material(
            Guid.NewGuid(),
            code.Trim(),
            name.Trim(),
            description?.Trim() ?? string.Empty,
            type,
            defaultUnit.Trim(),
            MaterialStatus.Active);

        material.RaiseDomainEvent(new MaterialCreatedDomainEvent(
            material.Id, 
            material.Code,
            material.Name));

        return Result.Success(material);
    }
    
    public Result Update(
        string name,
        string description,
        string defaultUnit)
    {
        if (string.IsNullOrWhiteSpace(name))
            return Result.Failure(MaterialErrors.InvalidName());

        if (string.IsNullOrWhiteSpace(defaultUnit))
            return Result.Failure(MaterialErrors.InvalidUnit());

        Name = name.Trim();
        Description = description?.Trim() ?? Description;
        DefaultUnit = defaultUnit.Trim();

        RaiseDomainEvent(new MaterialUpdatedDomainEvent(
            Id,
            Code,
            Name));

        return Result.Success();
    }
    
    public Result Discontinue(string reason)
    {
        if (string.IsNullOrWhiteSpace(reason))
        {
            return Result.Failure(MaterialErrors.DiscontinueReasonRequired());
        }

        if (Status == MaterialStatus.Discontinued)
        {
            return Result.Failure(MaterialErrors.AlreadyDiscontinued());
        }

        Status = MaterialStatus.Discontinued;

        RaiseDomainEvent(new MaterialDiscontinuedDomainEvent(
            Id,
            Code,
            Name,
            reason));

        return Result.Success();
    }
}