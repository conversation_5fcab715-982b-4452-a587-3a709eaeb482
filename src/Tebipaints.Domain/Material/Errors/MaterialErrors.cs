using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Domain.Material.Errors;

public class MaterialErrors
{
    public static Error InvalidCode() =>
        new("Material.InvalidCode", 
            "Material code must be provided.");

    public static Error InvalidName() =>
        new("Material.InvalidName", 
            "Material name must be provided.");

    public static Error InvalidUnit() =>
        new("Material.InvalidUnit", 
            "Default unit must be provided.");
    
    public static Error DiscontinueReasonRequired() =>
        new("Material.DiscontinueReasonRequired", 
            "A reason must be provided.");
    
    public static Error AlreadyDiscontinued() =>
        new("Material.AlreadyDiscontinued", 
            "The material is already discontinued.");
    
    public static Error InvalidMaterialType(string type) =>
        new("Material.InvalidMaterialType", 
            $"The material type '{type}' is not supported.");

    public static Error DuplicateCode(string code) =>
        new("Material.DuplicateCode", 
            $"A material with this code {code} already exists.");
    
    public static Error NotFound(Guid id) =>
        new("Material.NotFound", 
            $"A material with this code {id} could not be found.");
    
    public static Error UsedInActiveFormulations(Guid id) =>
        new("Material.NotFound", 
            $"The material with this code {id} is currently used in an active formulation.");
}