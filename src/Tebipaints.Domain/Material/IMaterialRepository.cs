using Tebipaints.Domain.Formulation;
using Tebipaints.Domain.Production;

namespace Tebipaints.Domain.Material;

public interface IMaterialRepository
{
    Task<Material?> GetByIdAsync(Guid id, CancellationToken cancellationToken);
    Task<Material?> GetByCodeAsync(string code, CancellationToken cancellationToken);
    Task<Dictionary<Guid, MaterialDetails>?> GetMaterialDetailsAsync(List<Ingredient> ingredients);
    void Add(Material material);
}