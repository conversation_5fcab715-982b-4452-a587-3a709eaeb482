namespace Tebipaints.Domain.Material.Enums;

public enum MaterialType
{
    /// <summary>
    /// Provides color and opacity to the paint
    /// </summary>
    Pigment,
    
    /// <summary>
    /// Liquid component that dissolves or disperses the resin
    /// </summary>
    Solvent,
    
    /// <summary>
    /// Forms the paint film and binds the components together
    /// </summary>
    Resin,
    
    /// <summary>
    /// Modifies specific properties of the paint (e.g., drying time, flow, UV resistance)
    /// </summary>
    Additive,
    
    /// <summary>
    /// Extends the paint and provides bulk, opacity, and texture
    /// </summary>
    Filler
}