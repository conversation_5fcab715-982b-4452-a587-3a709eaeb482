using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Invoice.Events;
using Tebipaints.Domain.SalesOrder.Events;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.SalesOrder;

public sealed class SalesOrder : Entity
{
    private readonly List<SalesOrderLineItem> _orderLineItems = new();
    private readonly List<DiscountPolicy> _appliedDiscounts = new();
    
    private SalesOrder(){}
    
    private SalesOrder(
        Guid id,
        Guid? customerId,
        string? walkInCustomerName,
        OrderNumber orderNumber,
        DateTime createdOnUtc,
        SalesOrderStatus status,
        List<SalesOrderLineItem> lineItems) : base(id)
    {
        CustomerId = customerId;
        WalkInCustomerName = walkInCustomerName;
        OrderNumber = orderNumber;
        CreatedOnUtc = createdOnUtc;
        Status = status;
        _orderLineItems = lineItems;
    }
    
    public OrderNumber OrderNumber { get; private set; }
    public Guid? CustomerId { get; private set; }
    public string? WalkInCustomerName { get; private set; }
    public SalesOrderStatus Status { get; private set; }
    public DateTime CreatedOnUtc { get; private set; }
    public DateTime? FulfilledOnUtc { get; private set; }
    public Guid? InvoiceId { get; private set; }
    public IReadOnlyCollection<SalesOrderLineItem> SalesOrderLineItems => _orderLineItems.AsReadOnly();
    public IReadOnlyCollection<DiscountPolicy> AppliedDiscounts => _appliedDiscounts.AsReadOnly();

    public Money SubTotal => new(
        _orderLineItems.Sum(item => item.LineTotal.Amount),
        Currency.Ghs);
    
    public Money OrderLevelDiscount
    {
        get
        {
            var totalDiscountPercentage = _appliedDiscounts
                .Where(d => d.IsValid(DateTime.UtcNow))
                .Sum(d => d.Percentage);

            return new Money(
                SubTotal.Amount * (totalDiscountPercentage / 100m),
                SubTotal.Currency);
        }
    }
    
    public Money Total => new(
        SubTotal.Amount - OrderLevelDiscount.Amount,
        SubTotal.Currency);

    public static Result<SalesOrder> Create(
        Guid customerId,
        OrderNumber orderNumber,
        List<SalesOrderLineItem> lineItems)
    {
        if (customerId == Guid.Empty)
        {
            return Result.Failure<SalesOrder>(
                SalesOrderErrors.InvalidCustomerId());
        }

        if (string.IsNullOrWhiteSpace(orderNumber.Value))
        {
            return Result.Failure<SalesOrder>(
                SalesOrderErrors.InvalidOrderNumber());
        }

        if ( lineItems.Count == 0)
        {
            return Result.Failure<SalesOrder>(
                SalesOrderErrors.EmptyLineItems());
        }

        var order = new SalesOrder(
            Guid.NewGuid(),
            customerId,
            null,
            orderNumber,
            DateTime.UtcNow,
            SalesOrderStatus.Draft,
            lineItems);
        
        order.RaiseDomainEvent(new SalesOrderCreatedDomainEvent(
            order.Id,
            order.CustomerId,
            order.WalkInCustomerName,
            order.OrderNumber,
            order.CreatedOnUtc));

        return Result.Success(order);
    }

    public static Result<SalesOrder> CreateForWalkInCustomer(
        string customerName,
        OrderNumber orderNumber,
        List<SalesOrderLineItem> lineItems)
    {
        if (string.IsNullOrWhiteSpace(customerName))
        {
            return Result.Failure<SalesOrder>(
                SalesOrderErrors.InvalidWalkInCustomerName());
        }

        if (string.IsNullOrWhiteSpace(orderNumber.Value))
        {
            return Result.Failure<SalesOrder>(
                SalesOrderErrors.InvalidOrderNumber());
        }

        if (lineItems.Count == 0)
        {
            return Result.Failure<SalesOrder>(
                SalesOrderErrors.EmptyLineItems());
        }

        var order = new SalesOrder(
            Guid.NewGuid(),
            null, // No CustomerId for walk-in customers
            customerName,
            orderNumber,
            DateTime.UtcNow,
            SalesOrderStatus.Draft,
            lineItems);
        
        order.RaiseDomainEvent(new SalesOrderCreatedDomainEvent(
            order.Id,
            order.CustomerId,
            order.WalkInCustomerName,
            order.OrderNumber,
            order.CreatedOnUtc));

        return Result.Success(order);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="productId"></param>
    /// <param name="sku"></param>
    /// <param name="description"></param>
    /// <param name="quantity"></param>
    /// <param name="unitPrice"></param>
    /// <returns></returns>
    public Result AddLineItem(
        Guid productId,
        SKU sku,
        string description,
        int quantity,
        Money unitPrice)
    {
        var lineItemResult = SalesOrderLineItem.Create(
            productId,
            sku,
            description,
            quantity,
            unitPrice);

        if (lineItemResult.IsFailure)
        {
            return Result.Failure(lineItemResult.Error);
        }

        _orderLineItems.Add(lineItemResult.Value);
        return Result.Success();
    }
    
    /// <summary>
    /// Issues an invoice for this sales order.
    /// </summary>
    /// <returns>A Result containing the Invoice ID if successful, or failure details if invoice cannot be issued</returns>
    /// <remarks>
    /// An invoice can be issued if:
    /// - The order is in Fulfilled status
    /// - No invoice has been issued yet
    /// </remarks>
    public Result IssueInvoice()
    {
        if (InvoiceId.HasValue)
        {
            return Result.Failure(
                SalesOrderErrors.InvoiceAlreadyIssued());
        }

        if (Status != SalesOrderStatus.Fulfilled)
        {
            return Result.Failure(
                SalesOrderErrors.CannotIssueInvoiceForUnfulfilledOrder());
        }

        RaiseDomainEvent(new SalesOrderReadyForInvoicingDomainEvent(
            Id,
            CustomerId,
            WalkInCustomerName,
            Total,
            SalesOrderLineItems.Select(li => new InvoiceLineItemData(
                li.ProductId,
                li.Sku,
                li.Description,
                li.Quantity,
                li.UnitPrice,
                li.LineItemDiscount)).ToList(),
            OrderLevelDiscount));  // Include order-level discounts
    
        return Result.Success();
    }
    
    public Result SetInvoiceId(Guid invoiceId)
    {
        if (InvoiceId.HasValue)
        {
            return Result.Failure(
                SalesOrderErrors.InvoiceAlreadyIssued());
        }

        InvoiceId = invoiceId;
    
        RaiseDomainEvent(new SalesOrderInvoiceAssignedDomainEvent(
            Id, 
            invoiceId));
        
        return Result.Success();
    }
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="discount"></param>
    /// <returns></returns>
    public Result ApplyDiscount(DiscountPolicy discount)
    {
        if (!IsValidForDiscount(discount))
        {
            return Result.Failure(SalesOrderErrors.InvalidDiscountType());
        }

        _appliedDiscounts.Add(discount);
        
        RaiseDomainEvent(new SalesOrderDiscountAppliedDomainEvent(
            Id, 
            discount.Type,
            discount.Percentage));
            
        return Result.Success();
    }
    
    public Result ApplyLineItemDiscount(
        Guid lineItemId, 
        DiscountPolicy discount)
    {
        var lineItem = _orderLineItems
            .FirstOrDefault(li => li.Id == lineItemId);

        if (lineItem is null)
        {
            return Result.Failure(
                SalesOrderErrors.LineItemNotFound());
        }

        var result = lineItem.ApplyDiscount(discount);
        
        if (result.IsSuccess)
        {
            RaiseDomainEvent(new SalesOrderLineItemDiscountAppliedDomainEvent(
                Id,
                lineItemId,
                discount.Type,
                discount.Percentage));
        }

        return result;
    }
    
    public Result ApplyOrderLevelDiscount(DiscountPolicy discount)
    {
        if (!IsValidForOrderLevelDiscount(discount))
        {
            return Result.Failure(
                SalesOrderErrors.InvalidOrderLevelDiscountType());
        }

        _appliedDiscounts.Add(discount);
        
        RaiseDomainEvent(new SalesOrderDiscountAppliedDomainEvent(
            Id, 
            discount.Type,
            discount.Percentage));
            
        return Result.Success();
    }
    
    private bool IsValidForDiscount(DiscountPolicy discount) =>
        discount.Type is DiscountType.OrderValue 
            or DiscountType.CustomerLoyalty 
            or DiscountType.SeasonalPromotion 
            or DiscountType.BulkPurchase 
            or DiscountType.SettlementDiscount 
            or DiscountType.CouponCode;
    
    private bool IsValidForOrderLevelDiscount(DiscountPolicy discount) =>
        discount.Type is DiscountType.OrderValue 
            or DiscountType.CustomerLoyalty 
            or DiscountType.SeasonalPromotion 
            or DiscountType.BulkPurchase 
            or DiscountType.SettlementDiscount 
            or DiscountType.CouponCode;

    private bool IsValidForLineItemDiscount(DiscountPolicy discount) =>
        discount.Type is DiscountType.ProductSpecific 
            or DiscountType.QuantityBased 
            or DiscountType.BundleDiscount;
}