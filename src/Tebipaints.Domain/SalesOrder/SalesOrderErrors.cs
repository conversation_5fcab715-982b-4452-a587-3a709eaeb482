using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Domain.SalesOrder;

public class SalesOrderErrors
{
    public static Error InvalidDiscountType() => new(
        "SalesOrder.InvalidDiscountType",
        "Invalid discount type");

    public static Error InvalidWalkInCustomerName() => new(
        "SalesOrder.InvalidWalkInCustomerName",
        "The walk-in customer name cannot be empty.");

    public static Error InvalidProductId() => new(
        "SalesOrder.InvalidProductId",
        "The product id specified is not valid");

    public static Error InvalidSku() => new(
        "SalesOrder.InvalidSku",
        "The sku specified is not valid");

    public static Error InvalidDescription() => new(
        "SalesOrder.InvalidDescription",
        "The line item description cannot be empty");
    
    public static Error InvalidQuantity() => new(
        "SalesOrder.InvalidQuantity",
        "The line item quantity cannot be zero or negative");
    
    public static Error InvalidUnitPrice() => new(
        "SalesOrder.InvalidUnitPrice",
        "The line item unit price cannot be zero or negative");
    
    public static Error InvalidCustomerId() =>
        new("SalesOrder.InvalidCustomerId", 
            "Customer ID must be provided.");

    public static Error InvalidOrderNumber() =>
        new("SalesOrder.InvalidOrderNumber", 
            "Order number must not be empty.");

    public static Error EmptyLineItems() =>
        new("SalesOrder.EmptyLineItems", 
            "Sales order must contain at least one line item.");
    
    public static Error InvoiceAlreadyIssued() =>
        new("SalesOrder.InvoiceAlreadyIssued", 
            "An invoice has already been issued for this sales order.");

    public static Error CannotIssueInvoiceForUnfulfilledOrder() =>
        new("SalesOrder.CannotIssueInvoiceForUnfulfilledOrder", 
            "Cannot issue invoice for an unfulfilled sales order.");

    public static Error LineItemNotFound() => new(
        "SalesOrder.LineItemNotFound",
        "Line item not found.");

    public static Error InvalidOrderLevelDiscountType() => new(
        "SalesOrder.InvalidOrderLevelDiscountType",
        "Invalid order level discount type.");

    public static Error NotFound(Guid requestSalesOrderId) => new(
        "SalesOrder.NotFound",
        $"The sales order with id {requestSalesOrderId} does not exist.");
}