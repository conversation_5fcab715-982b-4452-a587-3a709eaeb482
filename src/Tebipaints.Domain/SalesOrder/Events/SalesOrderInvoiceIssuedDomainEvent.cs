using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.SalesOrder.Events;

public record SalesOrderInvoiceIssuedDomainEvent(
    Guid SalesOrderId,
    Guid InvoiceId,
    Guid? CustomerId,
    string? WalkInCustomerName,
    Money Total,
    List<InvoiceLineItemData> LineItems) : IDomainEvent;

public sealed record InvoiceLineItemData(
    Guid ProductId,
    SKU Sku,
    string Description,
    int Quantity,
    Money UnitPrice,
    Money LineItemDiscount);