using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.SalesOrder.Events;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.SalesOrder;

public sealed class SalesOrderLineItem : Entity
{
    private readonly List<DiscountPolicy> _appliedDiscounts = new();
    
    private SalesOrderLineItem(){}
    
    private SalesOrderLineItem(
        Guid id,
        Guid productId,
        SKU sku,
        string description,
        int quantity,
        Money unitPrice) : base(id)
    {
        ProductId = productId;
        Sku = sku;
        Description = description;
        Quantity = quantity;
        UnitPrice = unitPrice;
    }
    
    public Guid ProductId { get; private set; }
    public SKU Sku { get; private set; }
    public string Description { get; private set; }
    public int Quantity { get; private set; }
    public Money UnitPrice { get; private set; }
    public decimal DiscountPercentage { get; private set; }
    public IReadOnlyCollection<DiscountPolicy> AppliedDiscounts => _appliedDiscounts.AsReadOnly();

    public Money LineItemDiscount
    {
        get
        {
            var totalDiscountPercentage = _appliedDiscounts
                .Where(d => d.IsValid(DateTime.UtcNow))
                .Sum(d => d.Percentage);

            return new Money(
                UnitPrice.Amount * (totalDiscountPercentage / 100m),
                UnitPrice.Currency);
        }
    }

    public Money LineTotal => new(
        (UnitPrice.Amount - LineItemDiscount.Amount) * Quantity,
        UnitPrice.Currency);

    /// <summary>
    /// Creates a new sales order line item.
    /// </summary>
    /// <param name="productId">The ID of the product being ordered</param>
    /// <param name="sku">The Stock Keeping Unit of the product</param>
    /// <param name="description">Description of the product</param>
    /// <param name="quantity">Number of units ordered</param>
    /// <param name="unitPrice">Price per unit</param>
    /// <returns>A Result containing the new line item if successful, or failure details if invalid</returns>
    public static Result<SalesOrderLineItem> Create(
        Guid productId,
        SKU sku,
        string description,
        int quantity,
        Money unitPrice)
    {
        if (productId == Guid.Empty)
        {
            return Result.Failure<SalesOrderLineItem>(
                SalesOrderErrors.InvalidProductId());
        }

        if (string.IsNullOrWhiteSpace(sku.Value))
        {
            return Result.Failure<SalesOrderLineItem>(
                SalesOrderErrors.InvalidSku());
        }

        if (string.IsNullOrWhiteSpace(description))
        {
            return Result.Failure<SalesOrderLineItem>(
                SalesOrderErrors.InvalidDescription());
        }

        if (quantity <= 0)
        {
            return Result.Failure<SalesOrderLineItem>(
                SalesOrderErrors.InvalidQuantity());
        }

        if (unitPrice.Amount <= 0)
        {
            return Result.Failure<SalesOrderLineItem>(
                SalesOrderErrors.InvalidUnitPrice());
        }
        
        var lineItem = new SalesOrderLineItem(
            Guid.NewGuid(),
            productId,
            sku,
            description.Trim(),
            quantity,
            unitPrice);

        lineItem.RaiseDomainEvent(new SalesOrderLineItemCreatedDomainEvent(
            lineItem.Id,
            lineItem.ProductId,
            lineItem.Sku.Value,
            lineItem.Quantity));

        return Result.Success(lineItem);
    }

    public Result ApplyDiscount(DiscountPolicy discount)
    {
        if (!IsValidForDiscount(discount))
        {
            return Result.Failure(SalesOrderErrors.InvalidDiscountType());
        }

        _appliedDiscounts.Add(discount);
        return Result.Success();
    }
    
    private bool IsValidForDiscount(DiscountPolicy discount) =>
        discount.Type is DiscountType.ProductSpecific 
            or DiscountType.QuantityBased 
            or DiscountType.BundleDiscount;
}