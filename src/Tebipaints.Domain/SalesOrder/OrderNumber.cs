namespace Tebipaints.Domain.SalesOrder;

public record OrderNumber
{
    private const string Prefix = "TBP-SO";
    private const int SequenceLength = 3;
    
    public string Value { get; }
    
    private OrderNumber(string value) => Value = value;
    
    public static OrderNumber Create(int sequence, DateTime? date = null)
    {
        date ??= DateTime.UtcNow;
        string datePart = date.Value.ToString("yyyyMM");
        string sequencePart = sequence.ToString($"D{SequenceLength}");
        
        return new OrderNumber($"{Prefix}-{datePart}-{sequencePart}");
    }

    public static bool TryParse(string value, out OrderNumber? number)
    {
        number = null;
        if (string.IsNullOrWhiteSpace(value)) return false;
        
        var pattern = $@"^{Prefix}-(\d{{6}})-(\d{{{SequenceLength}}})$";
        var match = System.Text.RegularExpressions.Regex.Match(value, pattern);
        
        if (!match.Success) return false;

        number = new OrderNumber(value);

        return true;
    }
    
    public override string ToString() => Value;
}