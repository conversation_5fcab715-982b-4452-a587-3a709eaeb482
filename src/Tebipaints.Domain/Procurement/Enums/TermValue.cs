namespace Tebipaints.Domain.Procurement.Enums;

public enum TermValue
{
    // Payment
    Net15,
    Net30,
    Net60,
    CashOnDelivery,
    AdvancePayment,
    LetterOfCredit,
    PartialAdvance,
    MilestoneBased,

    // Delivery
    EXW,
    FOB,
    CIF,
    DAP,
    DDP,
    FactoryDelivery,
    WarehouseDelivery,
    BuyerPickup,

    // Quality
    SpecificationCompliance,
    RequiresInspection,
    CertificateOfAnalysisRequired,
    ReturnIfNonCompliant,
    WarrantyIncluded,

    // Pricing
    FixedPrice,
    VariablePrice,
    VolumeDiscount,
    PriceValidFor30Days,
    IncludesVAT,

    // Other
    Custom
}
