namespace Tebipaints.Domain.Procurement;

public interface IPurchaseInvoiceRepository
{
    Task<PurchaseInvoice?> GetByIdAsync(Guid purchaseInvoiceId, CancellationToken cancellationToken = default);
    Task<List<PurchaseInvoice>> GetByPurchaseOrderIdAsync(Guid purchaseOrderId, CancellationToken cancellationToken = default);
    Task<PurchaseInvoice?> GetBySupplierInvoiceNumberAsync(string supplierInvoiceNumber, Guid supplierId, CancellationToken cancellationToken = default);
    void Add(PurchaseInvoice purchaseInvoice);
    void Update(PurchaseInvoice purchaseInvoice);
    Task<bool> ExistsAsync(string supplierInvoiceNumber, Guid supplierId, CancellationToken cancellationToken = default);
}
