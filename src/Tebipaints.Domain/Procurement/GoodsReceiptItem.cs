using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement.Errors;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Procurement;

public sealed record GoodsReceiptItem
{
    private GoodsReceiptItem() { }
    
    private GoodsReceiptItem(
        Guid materialId,
        string materialName,
        Measurement orderedQuantity,
        Measurement receivedQuantity,
        Money unitPrice,
        string? notes,
        QualityStatus qualityStatus)
    {
        MaterialId = materialId;
        MaterialName = materialName;
        OrderedQuantity = orderedQuantity;
        ReceivedQuantity = receivedQuantity;
        UnitPrice = unitPrice;
        Notes = notes;
        QualityStatus = qualityStatus;
    }
    
    public Guid MaterialId { get; }
    public string MaterialName { get; }
    public Measurement OrderedQuantity { get; }
    public Measurement ReceivedQuantity { get; }
    public Money UnitPrice { get; }
    public string? Notes { get; init; }
    public QualityStatus QualityStatus { get; private set; }
    
    // Calculated properties
    public Money TotalValue => new Money(UnitPrice.Amount * ReceivedQuantity.Value, UnitPrice.Currency);
    public Measurement VarianceQuantity => ReceivedQuantity - OrderedQuantity;
    public decimal VariancePercentage => OrderedQuantity.Value == 0 ? 0 : 
        (decimal)((ReceivedQuantity.Value - OrderedQuantity.Value) / OrderedQuantity.Value * 100);
    public bool IsWithinTolerance => Math.Abs(VariancePercentage) <= 5.0m; // 5% tolerance
    public bool IsOverReceived => ReceivedQuantity > OrderedQuantity;
    public bool IsUnderReceived => ReceivedQuantity < OrderedQuantity;
    public bool IsFullyReceived => ReceivedQuantity >= OrderedQuantity && IsWithinTolerance;
    
    public static Result<GoodsReceiptItem> Create(
        Guid materialId,
        string materialName,
        Measurement orderedQuantity,
        Measurement receivedQuantity,
        Money unitPrice,
        string? notes = null,
        QualityStatus qualityStatus = QualityStatus.Pending)
    {
        if (materialId == Guid.Empty)
        {
            return Result.Failure<GoodsReceiptItem>(ProcurementErrors.InvalidMaterialId());
        }
        
        if (string.IsNullOrWhiteSpace(materialName))
        {
            return Result.Failure<GoodsReceiptItem>(ProcurementErrors.InvalidMaterialName());
        }
        
        if (orderedQuantity.Value <= 0)
        {
            return Result.Failure<GoodsReceiptItem>(ProcurementErrors.InvalidQuantity());
        }
        
        if (receivedQuantity.Value < 0)
        {
            return Result.Failure<GoodsReceiptItem>(ProcurementErrors.InvalidReceivedQuantity());
        }
        
        if (unitPrice.Amount < 0)
        {
            return Result.Failure<GoodsReceiptItem>(ProcurementErrors.InvalidUnitPrice());
        }
        
        // Ensure units are compatible
        if (receivedQuantity.Unit != orderedQuantity.Unit)
        {
            try
            {
                receivedQuantity = receivedQuantity.ConvertTo(orderedQuantity.Unit);
            }
            catch (InvalidOperationException)
            {
                return Result.Failure<GoodsReceiptItem>(ProcurementErrors.IncompatibleUnits());
            }
        }
        
        var item = new GoodsReceiptItem(
            materialId,
            materialName,
            orderedQuantity,
            receivedQuantity,
            unitPrice,
            notes,
            qualityStatus);
        
        return Result.Success(item);
    }
    
    public Result<GoodsReceiptItem> UpdateQualityStatus(QualityStatus newStatus, string? notes = null)
    {
        var updatedNotes = string.IsNullOrWhiteSpace(notes) ? Notes : 
            string.IsNullOrWhiteSpace(Notes) ? notes : $"{Notes}; {notes}";
        
        return Result.Success(this with 
        { 
            QualityStatus = newStatus,
            Notes = updatedNotes
        });
    }
    
    public Result<GoodsReceiptItem> WithNotes(string? newNotes)
    {
        return Result.Success(this with { Notes = newNotes });
    }
}

public enum QualityStatus
{
    Pending = 0,
    Approved = 1,
    Rejected = 2,
    RequiresInspection = 3
}
