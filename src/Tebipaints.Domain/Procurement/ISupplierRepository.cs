namespace Tebipaints.Domain.Procurement;

public interface ISupplierRepository
{
    Task<Supplier?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<Supplier?> GetByContractIdAsync(Guid contractId, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(string code, CancellationToken cancellationToken = default);
    void Add(Supplier supplier);
    void UpdateSupplier(Supplier supplier);
}