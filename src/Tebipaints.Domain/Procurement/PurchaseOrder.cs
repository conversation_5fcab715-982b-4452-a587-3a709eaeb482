using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement.Enums;
using Tebipaints.Domain.Procurement.Errors;
using Tebipaints.Domain.Procurement.Events;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Procurement;

public class PurchaseOrder : Entity
{
    private readonly List<PurchaseOrderItem> _items = [];
    
    private PurchaseOrder(){}
    
    private PurchaseOrder(
        Guid id,
        PurchaseOrderNumber orderNumber,
        Guid supplierId,
        DateTime orderDate,
        string requestedBy,
        DateTime promiseDate,
        decimal discountPercentage,
        Money freightCharge,
        Currency orderCurrency,
        decimal taxRate,
        PurchaseOrderStatus status,
        List<PurchaseOrderItem> items) : base(id)
    {
        OrderNumber = orderNumber;
        SupplierId = supplierId;
        OrderDate = orderDate;
        RequestedBy = requestedBy;
        Status = status;
        PromiseDate = promiseDate;
        DiscountPercentage = discountPercentage;
        FreightCharge = freightCharge;
        OrderCurrency = orderCurrency;
        TaxRate = taxRate;
        _items.AddRange(items);
    }
    
    public PurchaseOrderNumber OrderNumber { get; private set; }
    public Guid SupplierId { get; private set; }
    public DateTime OrderDate { get; private set; }
    public Currency OrderCurrency { get; private set; }
    public decimal TaxRate { get; private set; }
    public string? RequestedBy { get; private set; }
    public DateTime? ApprovedDate { get; private set; }
    public DateTime? PromiseDate { get; private set; }
    public decimal DiscountPercentage { get; private set; }
    public string? ApprovedBy { get; private set; }
    public Money FreightCharge { get; private set; }
    public PurchaseOrderStatus Status { get; private set; }
    public IReadOnlyCollection<PurchaseOrderItem> Items => _items.AsReadOnly();
    public decimal TotalAmount => _items.Sum(item => item.TotalPrice.Amount);

    public static Result<PurchaseOrder> Create(
        PurchaseOrderNumber orderNumber,
        Guid supplierId,
        string requestedBy,
        DateTime promiseDate,
        decimal? discountPercentage,
        Money? freightCharge,
        Currency? orderCurrency,
        decimal taxRate,
        List<PurchaseOrderItem>? items = null)
    {

        if (supplierId == Guid.Empty)
        {
            return Result.Failure<PurchaseOrder>(ProcurementErrors.InvalidSupplierId());
        }

        if (string.IsNullOrWhiteSpace(requestedBy))
        {
            return Result.Failure<PurchaseOrder>(ProcurementErrors.InvalidRequestedBy());
        }

        if (promiseDate < DateTime.UtcNow.Date)
        {
            return Result.Failure<PurchaseOrder>(ProcurementErrors.InvalidCompletionDate());
        }

        var actualDiscount = discountPercentage ?? 0.00m;
        if (actualDiscount is < 0 or > 100)
        {
            return Result.Failure<PurchaseOrder>(ProcurementErrors.InvalidPercentage());
        }

        var actualFreight = freightCharge ?? Money.Zero();
        if (actualFreight.Amount < 0)
        {
            return Result.Failure<PurchaseOrder>(ProcurementErrors.InvalidUnitPrice());
        }

        var actualCurrency = orderCurrency ?? Currency.None;
        if (actualCurrency == Currency.None)
        {
            return Result.Failure<PurchaseOrder>(ProcurementErrors.InvalidCurrency());
        }

        var orderItems = items ?? new List<PurchaseOrderItem>();
        // Check for duplicates
        if (orderItems.GroupBy(x => x.MaterialId).Any(g => g.Count() > 1))
        {
            return Result.Failure<PurchaseOrder>(ProcurementErrors.DuplicateOrderItem());
        }
        // Validate each item
        foreach (var item in orderItems)
        {
            var itemResult = PurchaseOrderItem.Create(
                item.MaterialId,
                item.MaterialName,
                item.Quantity,
                item.UnitPrice,
                item.Notes);
            if (itemResult.IsFailure)
            {
                return Result.Failure<PurchaseOrder>(itemResult.Error);
            }
        }

        var order = new PurchaseOrder(
            Guid.NewGuid(),
            orderNumber,
            supplierId,
            DateTime.UtcNow,
            requestedBy,
            promiseDate,
            actualDiscount,
            actualFreight,
            actualCurrency,
            taxRate,
            PurchaseOrderStatus.Draft,
            orderItems);
        
        order.RaiseDomainEvent(new PurchaseOrderCreatedDomainEvent(order.Id, order.OrderNumber.ToString()));
        
        return Result.Success(order);
    }
    
    public Result AddItem(PurchaseOrderItem item)
    {
        if (Status != PurchaseOrderStatus.Draft)
        {
            return Result.Failure(ProcurementErrors.CannotModifyNonDraftOrder());
        }
        
        if (_items.Any(x => x.Equals(item)))
        {
            return Result.Failure(ProcurementErrors.DuplicateOrderItem());
        }
            
        _items.Add(item);
        
        return Result.Success();
    }
    
    public Result RemoveItem(PurchaseOrderItem item)
    {
        if (Status != PurchaseOrderStatus.Draft)
        {
            return Result.Failure(ProcurementErrors.CannotModifyNonDraftOrder());
        }

        _items.Remove(item);
        return Result.Success();
    }
    
    public Result Approve(string approvedBy)
    {
        if (Status != PurchaseOrderStatus.Draft)
        {
            return Result.Failure(ProcurementErrors.OrderNotInDraftStatus());
        }

        if (!_items.Any())
        {
            return Result.Failure(ProcurementErrors.OrderHasNoItems());
        }

        Status = PurchaseOrderStatus.Approved;
        ApprovedBy = approvedBy;
        ApprovedDate = DateTime.UtcNow;

        RaiseDomainEvent(new PurchaseOrderApprovedDomainEvent(Id, OrderNumber.ToString(), ApprovedBy));

        return Result.Success();
    }
    
    public Result Cancel(string reason)
    {
        if (Status == PurchaseOrderStatus.Cancelled)
        {
            return Result.Success();
        }

        if (Status == PurchaseOrderStatus.Closed)
        {
            return Result.Failure(ProcurementErrors.CannotCancelClosedOrder());
        }

        Status = PurchaseOrderStatus.Cancelled;
        
        RaiseDomainEvent(new PurchaseOrderCancelledDomainEvent(Id, OrderNumber.ToString(), reason));

        return Result.Success();
    }
    
    public Result Close()
    {
        if (Status == PurchaseOrderStatus.Closed)
        {
            return Result.Success();
        }

        if (Status != PurchaseOrderStatus.Approved)
        {
            return Result.Failure(ProcurementErrors.CanOnlyCloseApprovedOrder());
        }

        // Verify all items are fully received
        if (_items.Any(item => !item.IsFullyReceived))
        {
            return Result.Failure(ProcurementErrors.CannotCloseOrderWithPendingItems());
        }

        Status = PurchaseOrderStatus.Closed;
        
        RaiseDomainEvent(new PurchaseOrderClosedDomainEvent(Id, OrderNumber.ToString()));

        return Result.Success();
    }
    
    public Result UpdateReceivedQuantity(Guid materialId, Measurement receivedQuantity)
    {
        if (Status != PurchaseOrderStatus.Approved)
        {
            return Result.Failure(ProcurementErrors.CanOnlyReceiveApprovedOrder());
        }

        var item = _items.FirstOrDefault(i => i.MaterialId == materialId);
        if (item is null)
        {
            return Result.Failure(ProcurementErrors.OrderItemNotFound());
        }

        var updatedItemResult = item.WithReceivedQuantity(receivedQuantity);
        if (updatedItemResult.IsFailure)
        {
            return Result.Failure(updatedItemResult.Error);
        }

        var itemIndex = _items.IndexOf(item);
        _items[itemIndex] = updatedItemResult.Value;

        RaiseDomainEvent(new PurchaseOrderItemReceivedDomainEvent(
            Id,
            OrderNumber.ToString(),
            materialId,
            receivedQuantity));

        return Result.Success();
    }
}