using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement.Enums;
using Tebipaints.Domain.Procurement.Errors;
using Tebipaints.Domain.Procurement.Events;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Procurement;

public sealed class GoodsReceipt : Entity
{
    private readonly List<GoodsReceiptItem> _items = [];
    
    private GoodsReceipt() { }
    
    private GoodsReceipt(
        Guid id,
        GoodsReceiptNumber receiptNumber,
        Guid purchaseOrderId,
        string purchaseOrderNumber,
        Guid supplierId,
        DateTime receivedDate,
        string receivedBy,
        string? deliveryNote,
        string? supplierReference,
        GoodsReceiptStatus status,
        List<GoodsReceiptItem> items) : base(id)
    {
        ReceiptNumber = receiptNumber;
        PurchaseOrderId = purchaseOrderId;
        PurchaseOrderNumber = purchaseOrderNumber;
        SupplierId = supplierId;
        ReceivedDate = receivedDate;
        ReceivedBy = receivedBy;
        DeliveryNote = deliveryNote;
        SupplierReference = supplierReference;
        Status = status;
        _items.AddRange(items);
    }
    
    public GoodsReceiptNumber ReceiptNumber { get; private set; }
    public Guid PurchaseOrderId { get; private set; }
    public string PurchaseOrderNumber { get; private set; }
    public Guid SupplierId { get; private set; }
    public DateTime ReceivedDate { get; private set; }
    public string ReceivedBy { get; private set; }
    public string? DeliveryNote { get; private set; }
    public string? SupplierReference { get; private set; }
    public GoodsReceiptStatus Status { get; private set; }
    public IReadOnlyCollection<GoodsReceiptItem> Items => _items.AsReadOnly();
    public decimal TotalValue => _items.Sum(item => item.TotalValue.Amount);
    
    public static Result<GoodsReceipt> Create(
        GoodsReceiptNumber receiptNumber,
        Guid purchaseOrderId,
        string purchaseOrderNumber,
        Guid supplierId,
        string receivedBy,
        List<GoodsReceiptItem> items,
        string? deliveryNote = null,
        string? supplierReference = null,
        DateTime? receivedDate = null)
    {
        if (purchaseOrderId == Guid.Empty)
        {
            return Result.Failure<GoodsReceipt>(ProcurementErrors.InvalidPurchaseOrderId());
        }
        
        if (supplierId == Guid.Empty)
        {
            return Result.Failure<GoodsReceipt>(ProcurementErrors.InvalidSupplierId());
        }
        
        if (string.IsNullOrWhiteSpace(receivedBy))
        {
            return Result.Failure<GoodsReceipt>(ProcurementErrors.InvalidReceivedBy());
        }
        
        if (string.IsNullOrWhiteSpace(purchaseOrderNumber))
        {
            return Result.Failure<GoodsReceipt>(ProcurementErrors.InvalidOrderNumber());
        }
        
        if (items == null || !items.Any())
        {
            return Result.Failure<GoodsReceipt>(ProcurementErrors.GoodsReceiptHasNoItems());
        }
        
        var receipt = new GoodsReceipt(
            Guid.NewGuid(),
            receiptNumber,
            purchaseOrderId,
            purchaseOrderNumber,
            supplierId,
            receivedDate ?? DateTime.UtcNow,
            receivedBy,
            deliveryNote,
            supplierReference,
            GoodsReceiptStatus.Draft,
            items);
        
        receipt.RaiseDomainEvent(new GoodsReceiptCreatedDomainEvent(
            receipt.Id,
            receipt.ReceiptNumber.ToString(),
            receipt.PurchaseOrderId,
            receipt.PurchaseOrderNumber,
            receipt.Items.Select(i => new ReceivedItemData(
                i.MaterialId,
                i.ReceivedQuantity,
                i.UnitPrice)).ToList()));
        
        return Result.Success(receipt);
    }
    
    public Result Confirm()
    {
        if (Status != GoodsReceiptStatus.Draft)
        {
            return Result.Failure(ProcurementErrors.CanOnlyConfirmDraftReceipt());
        }
        
        Status = GoodsReceiptStatus.Confirmed;
        
        RaiseDomainEvent(new GoodsReceiptConfirmedDomainEvent(
            Id,
            ReceiptNumber.ToString(),
            PurchaseOrderId,
            PurchaseOrderNumber,
            Items.Select(i => new ReceivedItemData(
                i.MaterialId,
                i.ReceivedQuantity,
                i.UnitPrice)).ToList()));
        
        return Result.Success();
    }
    
    public Result Cancel(string reason)
    {
        if (Status == GoodsReceiptStatus.Cancelled)
        {
            return Result.Success();
        }
        
        if (Status != GoodsReceiptStatus.Draft)
        {
            return Result.Failure(ProcurementErrors.CanOnlyCancelDraftReceipt());
        }
        
        Status = GoodsReceiptStatus.Cancelled;
        
        RaiseDomainEvent(new GoodsReceiptCancelledDomainEvent(
            Id,
            ReceiptNumber.ToString(),
            PurchaseOrderId,
            reason));
        
        return Result.Success();
    }
    
    public GoodsReceiptItem? GetItem(Guid materialId)
    {
        return _items.FirstOrDefault(i => i.MaterialId == materialId);
    }
}
