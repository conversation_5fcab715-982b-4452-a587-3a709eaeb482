using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement.Errors;

namespace Tebipaints.Domain.Procurement;

public sealed record ContactInfo
{
    private ContactInfo(){}
    
    private ContactInfo(
        string contactPerson,
        string email,
        string phone,
        string? address = null)
    {
        ContactPerson = contactPerson;
        Email = email;
        Phone = phone;
        Address = address;
    }
    
    public string ContactPerson { get; }
    public string Email { get; }
    public string Phone { get; }
    public string? Address { get; }
    
    public static Result<ContactInfo> Create(
        string contactPerson,
        string email,
        string phone,
        string? address = null)
    {
        if (string.IsNullOrWhiteSpace(contactPerson))
        {
            return Result.Failure<ContactInfo>(ProcurementErrors.InvalidContactPerson());
        }

        if (string.IsNullOrWhiteSpace(email) || !IsValidEmail(email))
        {
            return Result.Failure<ContactInfo>(ProcurementErrors.InvalidEmail());
        }

        if (string.IsNullOrWhiteSpace(phone))
        {
            return Result.Failure<ContactInfo>(ProcurementErrors.InvalidPhone());
        }

        return Result.Success(new ContactInfo(contactPerson, email, phone, address));
    }
    
    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}