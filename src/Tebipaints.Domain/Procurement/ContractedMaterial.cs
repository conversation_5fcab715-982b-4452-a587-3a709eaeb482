using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement.Errors;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Procurement;

public record ContractedMaterial
{
    private ContractedMaterial(){}
    
    private ContractedMaterial(
        Guid materialId,
        string materialName,
        Money unitPrice,
        Measurement minimumOrder,
        Measurement? maximumOrder = null,
        int leadTimeDays = 0)
    {
        MaterialId = materialId;
        MaterialName = materialName;
        UnitPrice = unitPrice;
        MinimumOrder = minimumOrder;
        MaximumOrder = maximumOrder;
        LeadTimeDays = leadTimeDays;
    }
    
    public Guid MaterialId { get; }
    public string MaterialName { get; }
    public Money UnitPrice { get; }
    public Measurement MinimumOrder { get; }
    public Measurement? MaximumOrder { get; }
    public int LeadTimeDays { get; }
    
    public static Result<ContractedMaterial> Create(
        Guid materialId,
        string materialName,
        Money unitPrice,
        Measurement minimumOrder,
        Measurement? maximumOrder = null,
        int leadTimeDays = 0)
    {
        if (materialId == Guid.Empty)
        {
            return Result.Failure<ContractedMaterial>(ProcurementErrors.InvalidMaterialId());
        }

        if (string.IsNullOrWhiteSpace(materialName))
        {
            return Result.Failure<ContractedMaterial>(ProcurementErrors.InvalidMaterialName());
        }

        if (leadTimeDays < 0)
        {
            return Result.Failure<ContractedMaterial>(ProcurementErrors.InvalidLeadTime());
        }

        if (maximumOrder is not null && maximumOrder < minimumOrder)
        {
            return Result.Failure<ContractedMaterial>(ProcurementErrors.InvalidOrderLimits());
        }

        return Result.Success(new ContractedMaterial(
            materialId,
            materialName,
            unitPrice,
            minimumOrder,
            maximumOrder,
            leadTimeDays));
    }
    
    public Result<ContractedMaterial> WithUpdates(
        Money? newUnitPrice = null,
        Measurement? newMinimumOrder = null,
        Measurement? newMaximumOrder = null,
        int? newLeadTimeDays = null)
    {
        return Create(
            MaterialId,
            MaterialName,
            newUnitPrice ?? UnitPrice,
            newMinimumOrder ?? MinimumOrder,
            newMaximumOrder ?? MaximumOrder,
            newLeadTimeDays ?? LeadTimeDays);
    }
}