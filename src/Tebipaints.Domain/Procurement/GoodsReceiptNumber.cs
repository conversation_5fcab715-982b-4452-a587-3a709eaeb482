namespace Tebipaints.Domain.Procurement;

public record GoodsReceiptNumber
{
    private const string Prefix = "TBP-GR";
    private const int SequenceLength = 6;
    
    public string Value { get; }
    
    private GoodsReceiptNumber(string value) => Value = value;

    public static GoodsReceiptNumber Create(int sequence, DateTime? date = null)
    {
        date ??= DateTime.UtcNow;
        string datePart = date.Value.ToString("yyyyMM");
        string sequencePart = sequence.ToString($"D{SequenceLength}");
        
        return new GoodsReceiptNumber($"{Prefix}-{datePart}-{sequencePart}");
    }
    
    public static bool TryParse(string value, out GoodsReceiptNumber? number)
    {
        number = null;
        if (string.IsNullOrWhiteSpace(value)) return false;
        
        var pattern = $@"^{Prefix}-(\d{{6}})-(\d{{{SequenceLength}}})$";
        var match = System.Text.RegularExpressions.Regex.Match(value, pattern);
        
        if (!match.Success) return false;

        number = new GoodsReceiptNumber(value);
        return true;
    }
    
    public override string ToString() => Value;
}
