using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement.Enums;
using Tebipaints.Domain.Procurement.Errors;
using Tebipaints.Domain.Procurement.Events;

namespace Tebipaints.Domain.Procurement;

public sealed class Supplier : Entity
{
    private readonly List<Contract> _contracts = [];

    private Supplier() { }

    private Supplier(
        Guid id,
        string code,
        string name,
        ContactInfo contactInfo,
        SupplierStatus status) : base(id)
    {
        Code = code;
        Name = name;
        ContactInfo = contactInfo;
        Status = status;
    }

    public string Code { get; private set; }
    public string Name { get; private set; }
    public ContactInfo ContactInfo { get; private set; }
    public SupplierStatus Status { get; private set; }
    public IReadOnlyCollection<Contract> Contracts => _contracts.AsReadOnly();
    public Contract? ActiveContract => _contracts.FirstOrDefault(c => c.IsActive);

    public static Result<Supplier> Create(
        string code,
        string name,
        ContactInfo contactInfo)
    {
        if (string.IsNullOrWhiteSpace(code))
        {
            return Result.Failure<Supplier>(ProcurementErrors.InvalidSupplierCode());
        }

        if (string.IsNullOrWhiteSpace(name))
        {
            return Result.Failure<Supplier>(ProcurementErrors.InvalidSupplierName());
        }

        var supplier = new Supplier(
            Guid.NewGuid(),
            code,
            name,
            contactInfo,
            SupplierStatus.Active);

        return Result.Success(supplier);
    }

    public Result AddContract(Contract contract)
    {
        if (_contracts.Any(c => c.IsActive && contract.IsActive))
        {
            return Result.Failure(ProcurementErrors.ActiveContractAlreadyExists());
        }

        _contracts.Add(contract);

        RaiseDomainEvent(new SupplierContractAddedDomainEvent(Id, contract.Id));

        return Result.Success();
    }

    public Result UpdateContactInfo(ContactInfo newContactInfo)
    {
        ContactInfo = newContactInfo;
        return Result.Success();
    }

    public Result Deactivate(string reason)
    {
        if (Status == SupplierStatus.Inactive)
        {
            return Result.Success();
        }

        Status = SupplierStatus.Inactive;

        RaiseDomainEvent(new SupplierDeactivatedDomainEvent(Id, reason));

        return Result.Success();
    }
}