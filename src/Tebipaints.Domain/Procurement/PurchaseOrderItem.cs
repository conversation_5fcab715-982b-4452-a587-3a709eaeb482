using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement.Errors;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Procurement;

public sealed record PurchaseOrderItem
{
    private PurchaseOrderItem(){}
    
    private PurchaseOrderItem(
        Guid materialId,
        string materialName,
        Measurement quantity,
        Money unitPrice,
        string notes)
    {
        MaterialId = materialId;
        MaterialName = materialName;
        Quantity = quantity;
        UnitPrice = unitPrice;
        Notes = notes;
        ReceivedQuantity = Measurement.Zero(quantity.Unit);
    }
    
    public Guid MaterialId { get; }
    public string MaterialName { get; }
    public Measurement Quantity { get; }
    public Money UnitPrice { get; }
    public string? Notes { get; private set; }
    public Money TotalPrice => new Money(UnitPrice.Amount * Quantity.Value, UnitPrice.Currency);
    public Measurement ReceivedQuantity { get; init; }
    public Measurement RemainingQuantity => Quantity - ReceivedQuantity;

    // Enhanced receipt tracking with 5% tolerance
    public decimal ReceiptProgress => (decimal)ReceivedQuantity.Value / (decimal)Quantity.Value * 100;
    public decimal VariancePercentage => Quantity.Value == 0 ? 0 :
        (decimal)((ReceivedQuantity.Value - Quantity.Value) / Quantity.Value * 100);
    public bool IsWithinTolerance => Math.Abs(VariancePercentage) <= 5.0m;
    public bool IsOverReceived => ReceivedQuantity > Quantity;
    public bool IsUnderReceived => ReceivedQuantity < Quantity;
    public bool IsFullyReceived => ReceivedQuantity >= Quantity ||
        (ReceivedQuantity.Value >= Quantity.Value * 0.95m && IsWithinTolerance); // 95% with tolerance

    public static Result<PurchaseOrderItem> Create(
        Guid materialId,
        string materialName,
        Measurement quantity,
        Money unitPrice,
        string? notes = null)
    {
        if (materialId == Guid.Empty)
        {
            return Result.Failure<PurchaseOrderItem>(ProcurementErrors.InvalidMaterialId());
        }

        if (string.IsNullOrWhiteSpace(materialName))
        {
            return Result.Failure<PurchaseOrderItem>(ProcurementErrors.InvalidMaterialName());
        }

        if (quantity.Value <= 0)
        {
            return Result.Failure<PurchaseOrderItem>(ProcurementErrors.InvalidQuantity());
        }

        if (unitPrice.Amount < 0)
        {
            return Result.Failure<PurchaseOrderItem>(ProcurementErrors.InvalidUnitPrice());
        }

        var item = new PurchaseOrderItem(
            materialId,
            materialName,
            quantity,
            unitPrice,
            notes ?? string.Empty);
            
        return Result.Success(item);
    }
    
    public Result<PurchaseOrderItem> WithReceivedQuantity(Measurement additionalQuantity)
    {
        if (additionalQuantity.Value <= 0)
        {
            return Result.Failure<PurchaseOrderItem>(ProcurementErrors.InvalidQuantity());
        }

        if (additionalQuantity.Unit != Quantity.Unit)
        {
            try
            {
                additionalQuantity = additionalQuantity.ConvertTo(Quantity.Unit);
            }
            catch (InvalidOperationException)
            {
                return Result.Failure<PurchaseOrderItem>(ProcurementErrors.IncompatibleUnits());
            }
        }

        var newTotalReceived = ReceivedQuantity + additionalQuantity;
        
        if (newTotalReceived > Quantity)
        {
            return Result.Failure<PurchaseOrderItem>(ProcurementErrors.ExcessiveReceivedQuantity());
        }
        
        return Result.Success(this with { ReceivedQuantity = newTotalReceived });
    }

    public PurchaseOrderItem WithNotes(string? newNotes)
    {
        return this with { Notes = newNotes };
    }
}