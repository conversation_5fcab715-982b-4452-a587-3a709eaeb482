using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement.Errors;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Procurement;

public sealed record PurchaseInvoiceItem
{
    private PurchaseInvoiceItem() { }
    
    private PurchaseInvoiceItem(
        Guid materialId,
        string materialName,
        Measurement invoicedQuantity,
        Money unitPrice,
        string? notes)
    {
        MaterialId = materialId;
        MaterialName = materialName;
        InvoicedQuantity = invoicedQuantity;
        UnitPrice = unitPrice;
        Notes = notes;
    }
    
    public Guid MaterialId { get; }
    public string MaterialName { get; }
    public Measurement InvoicedQuantity { get; }
    public Money UnitPrice { get; }
    public string? Notes { get; init; }

    // Calculated properties
    public Money TotalAmount => new Money(UnitPrice.Amount * InvoicedQuantity.Value, UnitPrice.Currency);
    
    public static Result<PurchaseInvoiceItem> Create(
        Guid materialId,
        string materialName,
        Measurement invoicedQuantity,
        Money unitPrice,
        string? notes = null)
    {
        if (materialId == Guid.Empty)
        {
            return Result.Failure<PurchaseInvoiceItem>(ProcurementErrors.InvalidMaterialId());
        }
        
        if (string.IsNullOrWhiteSpace(materialName))
        {
            return Result.Failure<PurchaseInvoiceItem>(ProcurementErrors.InvalidMaterialName());
        }
        
        if (invoicedQuantity.Value <= 0)
        {
            return Result.Failure<PurchaseInvoiceItem>(ProcurementErrors.InvalidQuantity());
        }
        
        if (unitPrice.Amount < 0)
        {
            return Result.Failure<PurchaseInvoiceItem>(ProcurementErrors.InvalidUnitPrice());
        }
        
        var item = new PurchaseInvoiceItem(
            materialId,
            materialName,
            invoicedQuantity,
            unitPrice,
            notes);
        
        return Result.Success(item);
    }
    
    public PurchaseInvoiceItem WithNotes(string? newNotes)
    {
        return this with { Notes = newNotes };
    }
}
