using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement.Enums;
using Tebipaints.Domain.Procurement.Errors;
using Tebipaints.Domain.Procurement.Events;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Procurement;

public sealed class PurchaseInvoice : Entity
{
    private readonly List<PurchaseInvoiceItem> _items = [];
    
    private PurchaseInvoice() { }
    
    private PurchaseInvoice(
        Guid id,
        string supplierInvoiceNumber,
        Guid purchaseOrderId,
        string purchaseOrderNumber,
        Guid supplierId,
        DateTime invoiceDate,
        DateTime dueDate,
        Money totalAmount,
        string? notes,
        PurchaseInvoiceStatus status,
        List<PurchaseInvoiceItem> items) : base(id)
    {
        SupplierInvoiceNumber = supplierInvoiceNumber;
        PurchaseOrderId = purchaseOrderId;
        PurchaseOrderNumber = purchaseOrderNumber;
        SupplierId = supplierId;
        InvoiceDate = invoiceDate;
        DueDate = dueDate;
        TotalAmount = totalAmount;
        Notes = notes;
        Status = status;
        _items.AddRange(items);
    }
    
    public string SupplierInvoiceNumber { get; private set; }
    public Guid PurchaseOrderId { get; private set; }
    public string PurchaseOrderNumber { get; private set; }
    public Guid SupplierId { get; private set; }
    public DateTime InvoiceDate { get; private set; }
    public DateTime DueDate { get; private set; }
    public Money TotalAmount { get; private set; }
    public string? Notes { get; private set; }
    public PurchaseInvoiceStatus Status { get; private set; }
    public DateTime? ApprovedDate { get; private set; }
    public string? ApprovedBy { get; private set; }
    public DateTime? RejectedDate { get; private set; }
    public string? RejectedBy { get; private set; }
    public string? RejectionReason { get; private set; }
    public DateTime? LastMatchingDate { get; private set; }
    public bool? LastMatchingResult { get; private set; }
    public decimal? LastMatchingVariancePercentage { get; private set; }
    public string? MatchingNotes { get; private set; }
    public IReadOnlyCollection<PurchaseInvoiceItem> Items => _items.AsReadOnly();
    public decimal CalculatedTotal => _items.Sum(item => item.TotalAmount.Amount);
    
    // 3-way matching properties
    public bool HasMatchingDiscrepancies => Math.Abs(TotalAmount.Amount - CalculatedTotal) > 0.01m;
    public decimal AmountVariance => TotalAmount.Amount - CalculatedTotal;
    public decimal AmountVariancePercentage => CalculatedTotal == 0 ? 0 : 
        (AmountVariance / CalculatedTotal) * 100;
    public bool IsWithinToleranceLimit => Math.Abs(AmountVariancePercentage) <= 5.0m;
    
    public static Result<PurchaseInvoice> Create(
        string supplierInvoiceNumber,
        Guid purchaseOrderId,
        string purchaseOrderNumber,
        Guid supplierId,
        DateTime invoiceDate,
        DateTime dueDate,
        Money totalAmount,
        List<PurchaseInvoiceItem> items,
        string? notes = null)
    {
        if (string.IsNullOrWhiteSpace(supplierInvoiceNumber))
        {
            return Result.Failure<PurchaseInvoice>(ProcurementErrors.InvalidInvoiceNumber());
        }
        
        if (purchaseOrderId == Guid.Empty)
        {
            return Result.Failure<PurchaseInvoice>(ProcurementErrors.InvalidPurchaseOrderId());
        }
        
        if (supplierId == Guid.Empty)
        {
            return Result.Failure<PurchaseInvoice>(ProcurementErrors.InvalidSupplierId());
        }
        
        if (string.IsNullOrWhiteSpace(purchaseOrderNumber))
        {
            return Result.Failure<PurchaseInvoice>(ProcurementErrors.InvalidOrderNumber());
        }
        
        if (invoiceDate == default)
        {
            return Result.Failure<PurchaseInvoice>(ProcurementErrors.InvalidInvoiceDate());
        }
        
        if (dueDate <= invoiceDate)
        {
            return Result.Failure<PurchaseInvoice>(ProcurementErrors.InvalidDueDate());
        }
        
        if (totalAmount.Amount <= 0)
        {
            return Result.Failure<PurchaseInvoice>(ProcurementErrors.InvalidTotalAmount());
        }
        
        if (items == null || !items.Any())
        {
            return Result.Failure<PurchaseInvoice>(ProcurementErrors.PurchaseInvoiceHasNoItems());
        }
        
        var invoice = new PurchaseInvoice(
            Guid.NewGuid(),
            supplierInvoiceNumber,
            purchaseOrderId,
            purchaseOrderNumber,
            supplierId,
            invoiceDate,
            dueDate,
            totalAmount,
            notes,
            PurchaseInvoiceStatus.Draft,
            items);
        
        invoice.RaiseDomainEvent(new PurchaseInvoiceCreatedDomainEvent(
            invoice.Id,
            invoice.SupplierInvoiceNumber,
            invoice.PurchaseOrderId,
            invoice.PurchaseOrderNumber,
            invoice.SupplierId,
            invoice.TotalAmount,
            invoice.Items.Select(i => new InvoicedItemData(
                i.MaterialId,
                i.InvoicedQuantity,
                i.UnitPrice,
                i.TotalAmount)).ToList()));
        
        return Result.Success(invoice);
    }
    
    public Result Approve(string approvedBy)
    {
        if (Status != PurchaseInvoiceStatus.Draft && Status != PurchaseInvoiceStatus.PendingApproval)
        {
            return Result.Failure(ProcurementErrors.CanOnlyApproveDraftInvoice());
        }
        
        Status = PurchaseInvoiceStatus.Approved;
        ApprovedBy = approvedBy;
        ApprovedDate = DateTime.UtcNow;
        
        RaiseDomainEvent(new PurchaseInvoiceApprovedDomainEvent(
            Id,
            SupplierInvoiceNumber,
            PurchaseOrderId,
            PurchaseOrderNumber,
            SupplierId,
            TotalAmount,
            ApprovedBy));
        
        return Result.Success();
    }
    
    public Result Reject(string rejectedBy, string reason)
    {
        if (Status != PurchaseInvoiceStatus.Draft && Status != PurchaseInvoiceStatus.PendingApproval)
        {
            return Result.Failure(ProcurementErrors.CanOnlyRejectDraftOrPendingInvoice());
        }
        
        if (string.IsNullOrWhiteSpace(reason))
        {
            return Result.Failure(ProcurementErrors.RejectionReasonRequired());
        }
        
        Status = PurchaseInvoiceStatus.Rejected;
        RejectedBy = rejectedBy;
        RejectedDate = DateTime.UtcNow;
        RejectionReason = reason;
        
        RaiseDomainEvent(new PurchaseInvoiceRejectedDomainEvent(
            Id,
            SupplierInvoiceNumber,
            PurchaseOrderId,
            PurchaseOrderNumber,
            SupplierId,
            RejectedBy,
            reason));
        
        return Result.Success();
    }
    
    public Result SubmitForApproval()
    {
        if (Status != PurchaseInvoiceStatus.Draft)
        {
            return Result.Failure(ProcurementErrors.CanOnlySubmitDraftInvoice());
        }
        
        Status = PurchaseInvoiceStatus.PendingApproval;
        
        RaiseDomainEvent(new PurchaseInvoiceSubmittedForApprovalDomainEvent(
            Id,
            SupplierInvoiceNumber,
            PurchaseOrderId,
            PurchaseOrderNumber,
            SupplierId,
            TotalAmount,
            HasMatchingDiscrepancies,
            AmountVariancePercentage));
        
        return Result.Success();
    }
    
    public PurchaseInvoiceItem? GetItem(Guid materialId)
    {
        return _items.FirstOrDefault(i => i.MaterialId == materialId);
    }
    
    public Result<List<InvoiceMatchingResult>> PerformThreeWayMatching(
        PurchaseOrder purchaseOrder, 
        List<GoodsReceipt> goodsReceipts)
    {
        var matchingResults = new List<InvoiceMatchingResult>();
        
        foreach (var invoiceItem in _items)
        {
            var poItem = purchaseOrder.Items.FirstOrDefault(i => i.MaterialId == invoiceItem.MaterialId);
            if (poItem == null)
            {
                matchingResults.Add(InvoiceMatchingResult.CreateMismatch(
                    invoiceItem.MaterialId,
                    "Material not found in purchase order",
                    MatchingDiscrepancyType.MaterialNotInPO));
                continue;
            }
            
            // Get total received quantity from all receipts
            var totalReceived = goodsReceipts
                .Where(gr => gr.Status == GoodsReceiptStatus.Confirmed)
                .SelectMany(gr => gr.Items)
                .Where(item => item.MaterialId == invoiceItem.MaterialId)
                .Sum(item => item.ReceivedQuantity.Value);
            
            var result = InvoiceMatchingResult.Create(
                invoiceItem.MaterialId,
                poItem.Quantity.Value,
                (decimal)totalReceived,
                invoiceItem.InvoicedQuantity.Value,
                poItem.UnitPrice.Amount,
                invoiceItem.UnitPrice.Amount);
            
            matchingResults.Add(result);
        }
        
        return Result.Success(matchingResults);
    }

    public Result ProcessAutomaticMatching(
        PurchaseOrder purchaseOrder,
        List<GoodsReceipt> goodsReceipts)
    {
        var matchingResult = PerformThreeWayMatching(purchaseOrder, goodsReceipts);
        if (matchingResult.IsFailure)
        {
            return Result.Failure(matchingResult.Error);
        }

        var results = matchingResult.Value;
        var allMatched = results.All(r => r.IsMatched);
        var withinTolerance = results.All(r => r.IsQuantityWithinTolerance && r.IsPriceWithinTolerance);
        var overallVariance = results.Any() ?
            results.Average(r => Math.Max(Math.Abs(r.QuantityVariancePercentage), Math.Abs(r.PriceVariancePercentage))) : 0;

        // Store matching results
        LastMatchingDate = DateTime.UtcNow;
        LastMatchingResult = allMatched && withinTolerance;
        LastMatchingVariancePercentage = (decimal)overallVariance;

        // Build discrepancy summary
        var discrepancies = results
            .Where(r => !r.IsMatched)
            .Select(r => new MatchingDiscrepancySummary(
                r.MaterialId,
                GetMaterialName(r.MaterialId, purchaseOrder),
                r.DiscrepancyType?.ToString() ?? "Unknown",
                (decimal)Math.Max(Math.Abs(r.QuantityVariancePercentage), Math.Abs(r.PriceVariancePercentage)),
                r.DiscrepancyDescription ?? "No description"))
            .ToList();

        // Determine status based on matching results
        if (allMatched && withinTolerance)
        {
            // Auto-submit for approval
            Status = PurchaseInvoiceStatus.PendingApproval;
            MatchingNotes = $"Auto-submitted: All {results.Count} items matched within tolerance (variance: {overallVariance:F2}%)";
        }
        else
        {
            // Requires manual review
            Status = PurchaseInvoiceStatus.RequiresReview;
            var matchedCount = results.Count(r => r.IsMatched);
            MatchingNotes = $"Requires review: {matchedCount} of {results.Count} items matched. Overall variance: {overallVariance:F2}%";
        }

        // Raise domain event
        RaiseDomainEvent(new PurchaseInvoiceMatchingCompletedDomainEvent(
            Id,
            SupplierInvoiceNumber,
            PurchaseOrderId,
            PurchaseOrderNumber,
            matchingResult.IsSuccess,
            allMatched,
            withinTolerance,
            (decimal)overallVariance,
            results.Count,
            results.Count(r => r.IsMatched),
            discrepancies));

        return Result.Success();
    }

    public Result MarkForReview(string reason)
    {
        if (Status != PurchaseInvoiceStatus.Draft && Status != PurchaseInvoiceStatus.PendingApproval)
        {
            return Result.Failure(ProcurementErrors.CanOnlyMarkDraftOrPendingForReview());
        }

        Status = PurchaseInvoiceStatus.RequiresReview;
        MatchingNotes = reason;

        return Result.Success();
    }

    private static string GetMaterialName(Guid materialId, PurchaseOrder purchaseOrder)
    {
        var item = purchaseOrder.Items.FirstOrDefault(i => i.MaterialId == materialId);
        return item?.MaterialName ?? "Unknown Material";
    }
}
