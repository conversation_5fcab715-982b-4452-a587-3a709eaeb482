namespace Tebipaints.Domain.Procurement;

public sealed class InvoiceMatchingResult
{
    private InvoiceMatchingResult(
        Guid materialId,
        bool isMatched,
        string? discrepancyDescription,
        MatchingDiscrepancyType? discrepancyType,
        decimal orderedQuantity,
        decimal receivedQuantity,
        decimal invoicedQuantity,
        decimal orderedUnitPrice,
        decimal invoicedUnitPrice)
    {
        MaterialId = materialId;
        IsMatched = isMatched;
        DiscrepancyDescription = discrepancyDescription;
        DiscrepancyType = discrepancyType;
        OrderedQuantity = orderedQuantity;
        ReceivedQuantity = receivedQuantity;
        InvoicedQuantity = invoicedQuantity;
        OrderedUnitPrice = orderedUnitPrice;
        InvoicedUnitPrice = invoicedUnitPrice;
    }
    
    public Guid MaterialId { get; }
    public bool IsMatched { get; }
    public string? DiscrepancyDescription { get; }
    public MatchingDiscrepancyType? DiscrepancyType { get; }
    public decimal OrderedQuantity { get; }
    public decimal ReceivedQuantity { get; }
    public decimal InvoicedQuantity { get; }
    public decimal OrderedUnitPrice { get; }
    public decimal InvoicedUnitPrice { get; }
    
    // Calculated properties
    public decimal QuantityVariance => InvoicedQuantity - ReceivedQuantity;
    public decimal QuantityVariancePercentage => ReceivedQuantity == 0 ? 0 : 
        (QuantityVariance / ReceivedQuantity) * 100;
    public decimal PriceVariance => InvoicedUnitPrice - OrderedUnitPrice;
    public decimal PriceVariancePercentage => OrderedUnitPrice == 0 ? 0 : 
        (PriceVariance / OrderedUnitPrice) * 100;
    public bool IsQuantityWithinTolerance => Math.Abs(QuantityVariancePercentage) <= 5.0m;
    public bool IsPriceWithinTolerance => Math.Abs(PriceVariancePercentage) <= 5.0m;
    
    public static InvoiceMatchingResult Create(
        Guid materialId,
        decimal orderedQuantity,
        decimal receivedQuantity,
        decimal invoicedQuantity,
        decimal orderedUnitPrice,
        decimal invoicedUnitPrice)
    {
        var quantityVariancePercentage = receivedQuantity == 0 ? 0 : 
            Math.Abs((invoicedQuantity - receivedQuantity) / receivedQuantity) * 100;
        var priceVariancePercentage = orderedUnitPrice == 0 ? 0 : 
            Math.Abs((invoicedUnitPrice - orderedUnitPrice) / orderedUnitPrice) * 100;
        
        var isQuantityMatched = quantityVariancePercentage <= 5.0m;
        var isPriceMatched = priceVariancePercentage <= 5.0m;
        var isMatched = isQuantityMatched && isPriceMatched;
        
        string? discrepancyDescription = null;
        MatchingDiscrepancyType? discrepancyType = null;
        
        if (!isMatched)
        {
            if (!isQuantityMatched && !isPriceMatched)
            {
                discrepancyDescription = $"Quantity variance: {quantityVariancePercentage:F2}%, Price variance: {priceVariancePercentage:F2}%";
                discrepancyType = MatchingDiscrepancyType.QuantityAndPriceMismatch;
            }
            else if (!isQuantityMatched)
            {
                discrepancyDescription = $"Quantity variance: {quantityVariancePercentage:F2}%";
                discrepancyType = MatchingDiscrepancyType.QuantityMismatch;
            }
            else
            {
                discrepancyDescription = $"Price variance: {priceVariancePercentage:F2}%";
                discrepancyType = MatchingDiscrepancyType.PriceMismatch;
            }
        }
        
        return new InvoiceMatchingResult(
            materialId,
            isMatched,
            discrepancyDescription,
            discrepancyType,
            orderedQuantity,
            receivedQuantity,
            invoicedQuantity,
            orderedUnitPrice,
            invoicedUnitPrice);
    }
    
    public static InvoiceMatchingResult CreateMismatch(
        Guid materialId,
        string discrepancyDescription,
        MatchingDiscrepancyType discrepancyType)
    {
        return new InvoiceMatchingResult(
            materialId,
            false,
            discrepancyDescription,
            discrepancyType,
            0, 0, 0, 0, 0);
    }
}

public enum MatchingDiscrepancyType
{
    QuantityMismatch,
    PriceMismatch,
    QuantityAndPriceMismatch,
    MaterialNotInPO,
    MaterialNotReceived,
    ExcessiveVariance
}
