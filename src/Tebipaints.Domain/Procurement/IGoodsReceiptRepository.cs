namespace Tebipaints.Domain.Procurement;

public interface IGoodsReceiptRepository
{
    Task<GoodsReceipt?> GetByIdAsync(Guid goodsReceiptId, CancellationToken cancellationToken = default);
    Task<List<GoodsReceipt>> GetByPurchaseOrderIdAsync(Guid purchaseOrderId, CancellationToken cancellationToken = default);
    Task<GoodsReceipt?> GetByReceiptNumberAsync(string receiptNumber, CancellationToken cancellationToken = default);
    void Add(GoodsReceipt goodsReceipt);
    void Update(GoodsReceipt goodsReceipt);
    Task<bool> ExistsAsync(string receiptNumber, CancellationToken cancellationToken = default);
}
