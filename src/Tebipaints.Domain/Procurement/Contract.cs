using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement.Enums;
using Tebipaints.Domain.Procurement.Errors;
using Tebipaints.Domain.Procurement.Events;

namespace Tebipaints.Domain.Procurement;

public sealed class Contract : Entity
{
    private readonly List<ContractTerm> _terms = [];
    private readonly List<ContractedMaterial> _materials = [];

    private Contract() { }

    private Contract(
        Guid id,
        ContractNumber contractNumber,
        DateTime startDate,
        DateTime endDate,
        ContractStatus status) : base(id)
    {
        ContractNumber = contractNumber;
        StartDate = startDate;
        EndDate = endDate;
        Status = status;
    }

    public ContractNumber ContractNumber { get; private set; }
    public DateTime StartDate { get; private set; }
    public DateTime EndDate { get; private set; }
    public ContractStatus Status { get; private set; }
    public bool IsActive => Status == ContractStatus.Active &&
                            DateTime.UtcNow >= StartDate &&
                            DateTime.UtcNow <= EndDate;

    public IReadOnlyCollection<ContractTerm> Terms => _terms.AsReadOnly();
    public IReadOnlyCollection<ContractedMaterial> Materials => _materials.AsReadOnly();

    public static Result<Contract> Create(
        ContractNumber contractNumber,
        DateTime startDate,
        DateTime endDate,
        IEnumerable<ContractTerm>? terms = null,
        IEnumerable<ContractedMaterial>? materials = null)
    {
        if (string.IsNullOrWhiteSpace(contractNumber.ToString()))
        {
            return Result.Failure<Contract>(ProcurementErrors.InvalidContractNumber());
        }

        if (startDate >= endDate)
        {
            return Result.Failure<Contract>(ProcurementErrors.InvalidContractDates());
        }

        var contract = new Contract(
            Guid.NewGuid(),
            contractNumber,
            startDate,
            endDate,
            ContractStatus.Draft);

        if (terms is not null)
        {
            contract._terms.AddRange(terms);
        }

        if (materials is not null)
        {
            contract._materials.AddRange(materials);
        }

        return Result.Success(contract);
    }

    public Result<Contract> Renew(
        ContractNumber newContractNumber,
        DateTime newStartDate,
        DateTime newEndDate)
    {
        if (Status != ContractStatus.Active && Status != ContractStatus.Expired)
        {
            return Result.Failure<Contract>(ProcurementErrors.CannotRenewContract());
        }

        if (newStartDate >= newEndDate)
        {
            return Result.Failure<Contract>(ProcurementErrors.InvalidContractDates());
        }

        // Create new contract with existing terms and materials
        var newContract = new Contract(
            Guid.NewGuid(),
            newContractNumber,
            newStartDate,
            newEndDate,
            ContractStatus.Draft);

        // Copy terms (excluding expired ones)
        foreach (var term in _terms.Where(t => !t.ExpirationDate.HasValue || t.ExpirationDate > DateTime.UtcNow))
        {
            newContract._terms.Add(term);
        }

        // Copy materials
        foreach (var material in _materials)
        {
            newContract._materials.Add(material);
        }

        RaiseDomainEvent(new ContractRenewedDomainEvent(
            Id,
            ContractNumber.ToString(),
            newContract.Id,
            newContract.ContractNumber.ToString()));

        return Result.Success(newContract);
    }

    public Result Activate()
    {
        if (Status != ContractStatus.Draft)
        {
            return Result.Failure(ProcurementErrors.ContractNotInDraftStatus());
        }

        if (!_materials.Any())
        {
            return Result.Failure(ProcurementErrors.ContractHasNoMaterials());
        }

        if (!_terms.Any())
        {
            return Result.Failure(ProcurementErrors.ContractHasNoTerms());
        }

        if (DateTime.UtcNow > EndDate)
        {
            return Result.Failure(ProcurementErrors.ContractAlreadyExpired());
        }

        Status = ContractStatus.Active;

        RaiseDomainEvent(new ContractActivatedDomainEvent(Id, ContractNumber.ToString()));

        return Result.Success();
    }

    public Result Terminate(string reason)
    {
        if (Status != ContractStatus.Active)
        {
            return Result.Failure(ProcurementErrors.ContractNotActive());
        }

        Status = ContractStatus.Terminated;
        EndDate = DateTime.UtcNow;

        RaiseDomainEvent(new ContractTerminatedDomainEvent(Id, ContractNumber.ToString(), reason));

        return Result.Success();
    }

    public ContractedMaterial? GetMaterial(Guid materialId)
    {
        return _materials.FirstOrDefault(m => m.MaterialId == materialId);
    }

    public Result UpdateTerm(ContractTerm newTerm)
    {
        if (Status != ContractStatus.Draft)
        {
            return Result.Failure(ProcurementErrors.CannotModifyNonDraftContract());
        }

        // Remove existing term of the same type if it exists
        var existingTerm = _terms.FirstOrDefault(t => t.Type == newTerm.Type);
        if (existingTerm is not null)
        {
            _terms.Remove(existingTerm);
        }

        _terms.Add(newTerm);

        return Result.Success();
    }

    public Result UpdateMaterial(ContractedMaterial updatedMaterial)
    {
        if (Status != ContractStatus.Draft)
        {
            return Result.Failure(ProcurementErrors.CannotModifyNonDraftContract());
        }

        var existingMaterial = GetMaterial(updatedMaterial.MaterialId);
        if (existingMaterial is null)
        {
            return Result.Failure(ProcurementErrors.ContractedMaterialNotFound(updatedMaterial.MaterialId));
        }

        var index = _materials.IndexOf(existingMaterial);
        _materials[index] = updatedMaterial;

        return Result.Success();
    }

    public Result AddMaterial(ContractedMaterial material)
    {
        if (Status != ContractStatus.Draft)
        {
            return Result.Failure(ProcurementErrors.CannotModifyNonDraftContract());
        }

        if (_materials.Any(m => m.MaterialId == material.MaterialId))
        {
            return Result.Failure(ProcurementErrors.MaterialAlreadyInContract(material.MaterialId));
        }

        _materials.Add(material);

        return Result.Success();
    }
}