using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Domain.Procurement.Events;

public record PurchaseInvoiceMatchingCompletedDomainEvent(
    Guid PurchaseInvoiceId,
    string SupplierInvoiceNumber,
    Guid PurchaseOrderId,
    string PurchaseOrderNumber,
    bool MatchingSuccessful,
    bool AllItemsMatched,
    bool WithinTolerance,
    decimal OverallVariancePercentage,
    int TotalItems,
    int MatchedItems,
    List<MatchingDiscrepancySummary> Discrepancies) : IDomainEvent;

public record MatchingDiscrepancySummary(
    Guid MaterialId,
    string MaterialName,
    string DiscrepancyType,
    decimal VariancePercentage,
    string Description);
