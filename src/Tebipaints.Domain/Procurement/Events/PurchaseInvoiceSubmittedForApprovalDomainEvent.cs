using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Procurement.Events;

public record PurchaseInvoiceSubmittedForApprovalDomainEvent(
    Guid PurchaseInvoiceId,
    string SupplierInvoiceNumber,
    Guid PurchaseOrderId,
    string PurchaseOrderNumber,
    Guid SupplierId,
    Money TotalAmount,
    bool HasMatchingDiscrepancies,
    decimal AmountVariancePercentage) : IDomainEvent;
