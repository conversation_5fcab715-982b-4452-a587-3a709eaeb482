using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Procurement.Events;

public record PurchaseInvoiceCreatedDomainEvent(
    Guid PurchaseInvoiceId,
    string SupplierInvoiceNumber,
    Guid PurchaseOrderId,
    string PurchaseOrderNumber,
    Guid SupplierId,
    Money TotalAmount,
    List<InvoicedItemData> Items) : IDomainEvent;

public record InvoicedItemData(
    Guid MaterialId,
    Measurement InvoicedQuantity,
    Money UnitPrice,
    Money TotalAmount);
