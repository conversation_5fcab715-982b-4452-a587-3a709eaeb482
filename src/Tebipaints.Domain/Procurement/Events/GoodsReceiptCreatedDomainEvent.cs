using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Procurement.Events;

public record GoodsReceiptCreatedDomainEvent(
    Guid GoodsReceiptId,
    string ReceiptNumber,
    Guid PurchaseOrderId,
    string PurchaseOrderNumber,
    List<ReceivedItemData> Items) : IDomainEvent;

public record ReceivedItemData(
    Guid MaterialId,
    Measurement ReceivedQuantity,
    Money UnitPrice);
