using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Procurement.Enums;

namespace Tebipaints.Domain.Procurement.Errors;

public class ProcurementErrors
{
    public static Error SupplierNotFound(Guid supplierId) => new(
        "Procurement.SupplierNotFound",
        $"The supplier with id {supplierId} was not found.");

    public static Error InvalidTermType(string type) => new(
        "Procurement.InvalidTermType",
        $"The term type {type} is invalid.");
    
    public static Error MaterialNotFound(Guid materialId) => new(
        "Procurement.MaterialNotFound",
        $"Material with id {materialId} was not found.");
    
    public static Error CannotModifyNonDraftOrder() => new(
        "Procurement.CannotModifyNonDraftOrder",
        "The order needs to be in a draft state for modification to occur. ");
    
    public static Error InvalidOrderNumber() => new(
        "Procurement.InvalidOrderNumber",
        "The order number is invalid. ");
    
    public static Error InvalidSupplierId() => new(
        "Procurement.InvalidSupplierId",
        "The supplier id provided is invalid.");
    
    public static Error InvalidRequestedBy() => new(
        "Procurement.InvalidRequestedBy",
        "The requester must be specified.");
    
    public static Error DuplicateOrderItem() => new(
        "Procurement.InvalidRequestedBy",
        "The requester must be specified.");
    
    public static Error InvalidMaterialId() => new(
        "Procurement.InvalidMaterialId",
        "The material id cannot be empty or invalid");
    
    public static Error InvalidMaterialName() => new(
        "Procurement.InvalidMaterialName",
        "The material name cannot be empty.");
    
    public static Error InvalidQuantity() => new(
        "Procurement.InvalidQuantity",
        "The quantity must be greater than zero.");
    
    public static Error OrderNotInDraftStatus() => new(
        "Procurement.OrderNotInDraftStatus",
        "The order must be in draft state.");
    
    public static Error OrderHasNoItems() => new(
        "Procurement.OrderHasNoItems",
        "The order has no items.");
    
    public static Error CannotCancelClosedOrder() => new(
        "Procurement.CannotCancelClosedOrder",
        "A closed order cannot be cancelled.");
    
    public static Error CanOnlyCloseApprovedOrder() => new(
        "Procurement.CanOnlyCloseApprovedOrder",
        "Only an approved order can be closed.");
    
    public static Error CannotCloseOrderWithPendingItems() => new(
        "Procurement.CannotCloseOrderWithPendingItems",
        "The order has pending items. Cannot be closed.");
    
    public static Error CanOnlyReceiveApprovedOrder() => new(
        "Procurement.CanOnlyReceiveApprovedOrder",
        "Can only receive items for an approved order.");
    
    public static Error OrderItemNotFound() => new(
        "Procurement.OrderItemNotFound",
        "The item specified was not found.");
    
    public static Error InvalidUnitPrice() => new(
        "Procurement.InvalidUnitPrice",
        "The unit price must be greater than zero.");
    
    public static Error IncompatibleUnits() => new(
        "Procurement.IncompatibleUnits",
        "The unit price must be greater than zero.");
    
    public static Error ExcessiveReceivedQuantity() => new(
        "Procurement.ExcessiveReceivedQuantity",
        "The unit price must be greater than zero.");
    
    public static Error InvalidContactPerson() => new(
        "Procurement.InvalidContactPerson",
        "The contact person cannot be empty.");
    
    public static Error InvalidEmail() => new(
        "Procurement.InvalidEmail",
        "The email must be in the right format.");
    
    public static Error InvalidPhone() => new(
        "Procurement.InvalidPhone",
        "The value cannot be empty.");
    
    public static Error InvalidSupplierCode() => new(
        "Procurement.InvalidSupplierCode",
        "The value cannot be empty.");
    
    public static Error InvalidSupplierName() => new(
        "Procurement.InvalidSupplierName",
        "The value cannot be empty.");
    
    public static Error ActiveContractAlreadyExists() => new(
        "Procurement.ActiveContractAlreadyExists",
        "The value cannot be empty.");
    
    public static Error InvalidLeadTime() => new(
        "Procurement.InvalidLeadTime",
        "The value cannot be empty.");
    
    public static Error InvalidOrderLimits() => new(
        "Procurement.InvalidOrderLimits",
        "The value cannot be empty.");
    
    public static Error InvalidContractNumber() => new(
        "Procurement.InvalidContractNumber",
        "The value cannot be empty.");
    
    public static Error InvalidContractDates() => new(
        "Procurement.InvalidContractDates",
        "The value cannot be empty.");
    
    public static Error ContractHasNoMaterials() => new(
        "Procurement.ContractHasNoMaterials",
        "The value cannot be empty.");
    
    public static Error ContractNotActive() => new(
        "Procurement.ContractNotActive",
        "The value cannot be empty.");
    
    public static Error PurchaseOrderNotFound() => new(
        "Procurement.PurchaseOrderNotFound",
        "The specified order id was not found.");

    public static Error SupplierCodeAlreadyExists(string code) => new(
        "Procurement.SupplierCodeAlreadyExists",
        $"A supplier with this code: {code} already exists.");
    
    public static Error ContractNotInDraftStatus() => new(
        "Procurement.ContractNotInDraftStatus",
        "The contract isn't in a valid state");
    
    public static Error ContractNotFound(Guid contractId) => new(
        "Procurement.ContractNotFound",
        $"The contract with id {contractId} could not be found.");
    
    public static Error ContractHasNoTerms() => new(
        "Procurement.ContractHasNoTerms",
        "The contract has no terms specified.");
    
    public static Error ContractAlreadyExpired() => new(
        "Procurement.ContractAlreadyExpired",
        "The contract has already expired.");
    
    public static Error CannotRenewContract() => new(
        "Procurement.CannotRenewContract",
        "The contract cannot be renewed");
    
    public static Error CannotModifyNonDraftContract() => new(
        "Contract.CannotModifyNonDraft",
        "Cannot modify a contract that is not in draft status");

    public static Error ContractedMaterialNotFound(Guid materialId) => new(
        "Contract.MaterialNotFound",
        $"Contracted material with ID {materialId} was not found in the contract");
    
    public static Error MaterialAlreadyInContract(Guid materialId) => new(
        "Procurement.MaterialAlreadyInContract",
        $"Material with ID {materialId} is already in the contract");
    
    public static Error InvalidPercentage() => new(
        "Procurement.InvalidPercentage",
        "The percentage value is invalid");

    // Goods Receipt Errors
    public static Error InvalidPurchaseOrderId() => new(
        "Procurement.InvalidPurchaseOrderId",
        "The purchase order ID is invalid.");

    public static Error InvalidReceivedBy() => new(
        "Procurement.InvalidReceivedBy",
        "The received by field is required.");

    public static Error GoodsReceiptHasNoItems() => new(
        "Procurement.GoodsReceiptHasNoItems",
        "The goods receipt must have at least one item.");

    public static Error CanOnlyConfirmDraftReceipt() => new(
        "Procurement.CanOnlyConfirmDraftReceipt",
        "Only draft receipts can be confirmed.");

    public static Error CanOnlyCancelDraftReceipt() => new(
        "Procurement.CanOnlyCancelDraftReceipt",
        "Only draft receipts can be cancelled.");

    public static Error InvalidReceivedQuantity() => new(
        "Procurement.InvalidReceivedQuantity",
        "The received quantity cannot be negative.");

    // Purchase Invoice Errors
    public static Error InvalidInvoiceNumber() => new(
        "Procurement.InvalidInvoiceNumber",
        "The invoice number is invalid.");

    public static Error InvalidInvoiceDate() => new(
        "Procurement.InvalidInvoiceDate",
        "The invoice date is invalid.");

    public static Error InvalidDueDate() => new(
        "Procurement.InvalidDueDate",
        "The due date must be after the invoice date.");

    public static Error PurchaseInvoiceHasNoItems() => new(
        "Procurement.PurchaseInvoiceHasNoItems",
        "The purchase invoice must have at least one item.");

    public static Error CanOnlyApproveDraftInvoice() => new(
        "Procurement.CanOnlyApproveDraftInvoice",
        "Only draft invoices can be approved.");

    public static Error CanOnlyRejectDraftOrPendingInvoice() => new(
        "Procurement.CanOnlyRejectDraftOrPendingInvoice",
        "Only draft or pending invoices can be rejected.");

    public static Error InvalidTotalAmount() => new(
        "Procurement.InvalidTotalAmount",
        "The total amount must be greater than zero.");

    public static Error RejectionReasonRequired() => new(
        "Procurement.RejectionReasonRequired",
        "A rejection reason is required.");

    public static Error CanOnlySubmitDraftInvoice() => new(
        "Procurement.CanOnlySubmitDraftInvoice",
        "Only draft invoices can be submitted for approval.");

    public static Error PurchaseOrderNotFound(Guid purchaseOrderId) => new(
        "Procurement.PurchaseOrderNotFound",
        $"Purchase order with ID {purchaseOrderId} was not found.");

    public static Error MaterialNotInPurchaseOrder(Guid materialId) => new(
        "Procurement.MaterialNotInPurchaseOrder",
        $"Material with ID {materialId} is not in the purchase order.");

    public static Error GoodsReceiptNotFound(Guid goodsReceiptId) => new(
        "Procurement.GoodsReceiptNotFound",
        $"Goods receipt with ID {goodsReceiptId} was not found.");

    public static Error DuplicateSupplierInvoiceNumber(string invoiceNumber) => new(
        "Procurement.DuplicateSupplierInvoiceNumber",
        $"An invoice with number '{invoiceNumber}' already exists for this supplier.");

    public static Error SupplierMismatch() => new(
        "Procurement.SupplierMismatch",
        "The supplier on the invoice does not match the purchase order supplier.");

    public static Error PurchaseInvoiceNotFound(Guid purchaseInvoiceId) => new(
        "Procurement.PurchaseInvoiceNotFound",
        $"Purchase invoice with ID {purchaseInvoiceId} was not found.");

    public static Error CanOnlyMarkDraftOrPendingForReview() => new(
        "Procurement.CanOnlyMarkDraftOrPendingForReview",
        "Only draft or pending approval invoices can be marked for review.");

    public static Error InvoiceRequiresMatchingReview() => new(
        "Procurement.InvoiceRequiresMatchingReview",
        "Invoice has discrepancies that require review before approval.");

    public static Error InvoiceRequiresRecentMatching() => new(
        "Procurement.InvoiceRequiresRecentMatching",
        "Invoice matching results are outdated. Please re-run 3-way matching before approval.");

    public static Error InvalidCurrency() => new(
        "Procurement.InvalidCurrency",
        "The currency provided is invalid.");
    
    public static Error InvalidCompletionDate() => new (
        "Procurement.InvalidCompletionDate",
        "The completion date must be after the order date.");
}