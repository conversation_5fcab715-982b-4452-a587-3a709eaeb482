using Tebipaints.Domain.Procurement.Enums;

namespace Tebipaints.Domain.Procurement;

public sealed record ContractTerm
{
    private ContractTerm(){}
    
    private ContractTerm(
        string description,
        TermType type,
        IReadOnlyList<TermValue> values,
        DateTime? expirationDate = null)
    {
        Description = description;
        Type = type;
        ExpirationDate = expirationDate;
        Values = values;
    }
    
    public string Description { get; }
    public TermType Type { get; }
    public DateTime? ExpirationDate { get; }
    public IReadOnlyList<TermValue> Values { get; }
    
    public static ContractTerm Create(
        string? description,
        TermType type,
        IEnumerable<TermValue> values,
        DateTime? expirationDate = null)
    {
        var valueList = values.ToList();
        var finalDescription = description ?? string.Join(", ", valueList);
        return new ContractTerm(finalDescription, type, valueList.ToList(), expirationDate);
    }
}