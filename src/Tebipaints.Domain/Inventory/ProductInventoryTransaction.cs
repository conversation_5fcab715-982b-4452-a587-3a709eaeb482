using Tebipaints.Domain.Product;

namespace Tebipaints.Domain.Inventory;

public sealed class ProductInventoryTransaction
{
    private ProductInventoryTransaction() { }
    
    private ProductInventoryTransaction(
        InventoryTransaction<int> baseTransaction,
        Guid variantId,
        PackagingOption packaging)
    {
        BaseTransaction = baseTransaction;
        VariantId = variantId;
        Packaging = packaging;
    }

    public InventoryTransaction<int> BaseTransaction { get; }
    public Guid VariantId { get; }
    public PackagingOption Packaging { get; }

    public static ProductInventoryTransaction Create(
        TransactionType type,
        int quantity,
        string reference,
        Guid variantId,
        PackagingOption packaging)
    {
        var baseTransaction = InventoryTransaction<int>.Create(
            type,
            quantity,
            reference);

        return new ProductInventoryTransaction(
            baseTransaction,
            variantId,
            packaging);
    }
}