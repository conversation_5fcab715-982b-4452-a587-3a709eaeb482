using Tebipaints.Domain.Helpers;

namespace Tebipaints.Domain.Inventory;

public sealed class InventoryTransaction<TQuantity> where TQuantity : struct
{
    private InventoryTransaction(){}
    private InventoryTransaction(
        Guid transactionId,
        TransactionType type,
        TQuantity quantity,
        string reference,
        DateTime timestamp)
    {
        TransactionId = transactionId;
        Type = type;
        Quantity = quantity;
        Reference = reference;
        Timestamp = timestamp;
    }
    
    public Guid TransactionId { get; init; }
    public TransactionType Type { get; init; }
    public TQuantity Quantity { get; init; }
    public string Reference { get; init; }
    public DateTime Timestamp { get; init; }

    public static InventoryTransaction<TQuantity> Create(
        TransactionType type,
        TQuantity quantity,
        string reference) => new (Guid.NewGuid(), type, quantity, reference, DateTime.UtcNow);

    public TQuantity GetStockImpact() => Type switch
    {
        TransactionType.StockIn => Quantity,
        TransactionType.StockOut => NumericOperations<TQuantity>.Negate(Quantity),
        _ => default
    };
}