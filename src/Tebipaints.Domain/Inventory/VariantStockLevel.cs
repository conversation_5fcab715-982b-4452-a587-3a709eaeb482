using Tebipaints.Domain.Product;

namespace Tebipaints.Domain.Inventory;

public class VariantStockLevel
{
    // Required by EF Core
    private VariantStockLevel() { }

    public VariantStockLevel(VariantPackaging variantPackaging, int stockLevel)
    {
        VariantPackaging = variantPackaging;
        StockLevel = stockLevel;
    }

    public VariantPackaging VariantPackaging { get; private set; } = null!;
    public int StockLevel { get; set; }

}