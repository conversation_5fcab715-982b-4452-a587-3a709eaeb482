using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Inventory.Events;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.Product.Events;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Inventory;

public sealed class ProductInventory : Inventory<int>
{
    private readonly List<VariantStockLevel> _variantStockLevels = [];
    private readonly Dictionary<VariantPackaging, int> _variantStockLevelsCache = [];
    private readonly List<ProductInventoryTransaction> _variantTransactions = [];
    
    private ProductInventory(){}
    private ProductInventory(
        Guid id,
        Guid productId,
        InventoryLocation location,
        int minimumStockLevel,
        int reorderPoint) : base(id, location, minimumStockLevel, reorderPoint)
    {
        ProductId = productId;
    }
    
    public Guid ProductId { get; private set; }
    public override int CurrentStock => _transactions.Sum(t => t.GetStockImpact());
    public override bool IsLowStock => CurrentStock <= ReorderPoint;
    public IReadOnlyDictionary<VariantPackaging, int> VariantStockLevels => _variantStockLevelsCache.AsReadOnly();
    public IReadOnlyList<ProductInventoryTransaction> VariantTransactions => _variantTransactions.AsReadOnly();
    
    public int GetMinimumStockLevel() => MinimumStockLevel;
    public int GetReorderPoint() => ReorderPoint;

    /// <summary>
    /// Creates a new inventory record for a product at a specific location.
    /// </summary>
    /// <param name="productId">The ID of the product</param>
    /// <param name="location">The location where the inventory is stored</param>
    /// <param name="minimumStockLevel">The minimum allowed stock level</param>
    /// <param name="reorderPoint">The stock level at which reordering should occur</param>
    /// <returns>A Result containing the new Inventory if successful, or failure details if invalid</returns>
    /// <remarks>
    /// The reorder point must be greater than or equal to the minimum stock level.
    /// Both minimum stock level and reorder point must be non-negative.
    /// </remarks>
    public static Result<ProductInventory> Create(
        Guid productId,
        InventoryLocation location,
        int minimumStockLevel,
        int reorderPoint)
    {
        if (productId == Guid.Empty)
        {
            return Result.Failure<ProductInventory>(InventoryErrors.InvalidProductId());
        }

        if (minimumStockLevel < 0)
        {
            return Result.Failure<ProductInventory>(InventoryErrors.InvalidMinimumStockLevel());
        }

        if (reorderPoint < minimumStockLevel)
        {
            return Result.Failure<ProductInventory>(InventoryErrors.InvalidReorderPoint());
        }

        var inventory = new ProductInventory(
            Guid.NewGuid(),
            productId,
            location,
            minimumStockLevel,
            reorderPoint);
        
        return Result.Success(inventory);
    }

    /// <summary>
    /// Adds stock to the inventory.
    /// </summary>
    /// <param name="quantity">The quantity to add</param>
    /// <param name="reference">A reference note for the transaction (e.g., "Production Batch #123")</param>
    /// <returns>Success if stock was added, or failure if the quantity is invalid</returns>
    /// <remarks>
    /// This operation:
    /// - Creates a StockIn transaction
    /// - Updates the current stock level
    /// - Raises an InventoryStockAdded event
    /// - Checks and raises LowStockLevel event if applicable
    /// </remarks>
    public override Result AddStock(int quantity, string reference)
    {
        if (quantity <= 0)
        {
            return Result.Failure(InventoryErrors.InvalidQuantity());
        }
        
        var transaction = InventoryTransaction<int>.Create(
            TransactionType.StockIn,
            quantity,
            reference);

        _transactions.Add(transaction);
        
        RaiseStockAddedEvent(quantity);

        if (IsLowStock)
        {
            RaiseLowStockEvent();
        }
        
        return Result.Success();
    }
    
    private void SyncVariantStockLevels()
    {
        _variantStockLevelsCache.Clear();
        foreach (var level in _variantStockLevels)
        {
            _variantStockLevelsCache[level.VariantPackaging] = level.StockLevel;
        }
    }

    public Result AddVariantStock(Guid variantId, PackagingOption option, int quantity, string reference)
    {
        if (quantity <= 0)
        {
            return Result.Failure(InventoryErrors.InvalidQuantity());
        }
        
        var variantPackaging = VariantPackaging.Create(variantId, option.Type, option.Capacity);
        
        // Create base transaction for total stock tracking
        var baseTransaction = InventoryTransaction<int>.Create(
            TransactionType.StockIn,
            quantity,
            reference);

        // Create variant-specific transaction
        var variantTransaction = ProductInventoryTransaction.Create(
            TransactionType.StockIn,
            quantity,
            reference,
            variantId,
            option);
        
        _transactions.Add(baseTransaction);
        _variantTransactions.Add(variantTransaction);

        var currentQuantity = _variantStockLevelsCache.GetValueOrDefault(variantPackaging, 0);
        var newQuantity = currentQuantity + quantity;
        UpdateVariantStockLevel(variantPackaging, newQuantity);
        
        RaiseDomainEvent(new VariantStockAddedDomainEvent(
            Id,
            ProductId,
            variantId,
            option,
            quantity,
            newQuantity,
            Location));
        
        if (IsLowStock)
        {
            RaiseLowStockEvent();
        }
        
        return Result.Success();
    }
    
    // Method to update the backing collection
    private void UpdateVariantStockLevel(VariantPackaging packaging, int quantity)
    {
        var existingLevel = _variantStockLevels
            .FirstOrDefault(x => x.VariantPackaging == packaging);

        if (existingLevel != null)
        {
            existingLevel.StockLevel = quantity;
        }
        else
        {
            var newLevel = new VariantStockLevel(packaging, quantity);
            _variantStockLevels.Add(newLevel);
        }

        _variantStockLevelsCache[packaging] = quantity;
    }
    
    /// <summary>
    /// Removes stock from the inventory.
    /// </summary>
    /// <param name="quantity">The quantity to remove</param>
    /// <param name="reference">A reference note for the transaction (e.g., "Sales Order #123")</param>
    /// <returns>Success if stock was removed, or failure if the quantity is invalid or insufficient stock</returns>
    /// <remarks>
    /// This operation:
    /// - Validates sufficient stock exists
    /// - Creates a StockOut transaction
    /// - Updates the current stock level
    /// - Raises an InventoryStockRemoved event
    /// </remarks>
    public override Result RemoveStock(int quantity, string reference)
    {
        if (quantity <= 0)
        {
            return Result.Failure(InventoryErrors.InvalidQuantity());
        }

        if (quantity > CurrentStock)
        {
            return Result.Failure(InventoryErrors.InsufficientStock());
        }

        var transaction = InventoryTransaction<int>.Create(
            TransactionType.StockOut,
            quantity,
            reference);

        _transactions.Add(transaction);
        
        RaiseStockRemovedEvent(quantity);

        if (IsLowStock)
        {
            RaiseLowStockEvent();
        }

        return Result.Success();
    }
    
    public Result RemoveVariantStock(
        Guid variantId,
        PackagingOption packaging,
        int quantity,
        string reference)
    {
        if (quantity <= 0)
        {
            return Result.Failure(InventoryErrors.InvalidQuantity());
        }
        
        var variantPackaging = VariantPackaging.Create(variantId, packaging.Type, packaging.Capacity);
        
        var currentQuantity = _variantStockLevelsCache.GetValueOrDefault(variantPackaging, 0);
        if (currentQuantity < quantity)
        {
            return Result.Failure(InventoryErrors.InsufficientVariantStock(variantId, packaging));
        }

        // Create base transaction for total stock tracking
        var baseTransaction = InventoryTransaction<int>.Create(
            TransactionType.StockOut,
            quantity,
            reference);

        // Create variant-specific transaction
        var variantTransaction = ProductInventoryTransaction.Create(
            TransactionType.StockOut,
            quantity,
            reference,
            variantId,
            packaging);

        _transactions.Add(baseTransaction);
        _variantTransactions.Add(variantTransaction);

        // Update stock levels
        var newQuantity = currentQuantity - quantity;

        RaiseDomainEvent(new VariantStockRemovedDomainEvent(
            Id,
            ProductId,
            variantId,
            packaging,
            quantity,
            newQuantity,
            Location));
        
        if (IsLowStock)
        {
            RaiseLowStockEvent();
        }

        return Result.Success();
    }
    
    /// <summary>
    /// Transfers stock from this inventory to another inventory location.
    /// </summary>
    /// <param name="destinationInventory">The inventory to transfer stock to</param>
    /// <param name="quantity">The quantity to transfer</param>
    /// <param name="reference">A reference note for the transaction</param>
    /// <returns>Success if the transfer was completed, or failure if invalid or insufficient stock</returns>
    /// <remarks>
    /// This operation:
    /// - Validates sufficient stock exists at source
    /// - Removes stock from source inventory
    /// - Adds stock to destination inventory
    /// - Rolls back if either operation fails
    /// - Raises an InventoryTransferred event
    /// 
    /// The transfer is atomic - either both operations succeed or neither does.
    /// </remarks>
    public Result TransferStock(
        ProductInventory destinationInventory, 
        int quantity, 
        string reference)
    {
        var removeResult = RemoveStock(quantity, $"Transfer to {destinationInventory.Location}: {reference}");
        if (removeResult.IsFailure)
        {
            return removeResult;
        }

        var addResult = destinationInventory.AddStock(
            quantity, 
            $"Transfer from {Location}: {reference}");

        if (addResult.IsFailure)
        {
            // Rollback the removal if addition fails
            _transactions.RemoveAt(_transactions.Count - 1);
            return addResult;
        }

        RaiseDomainEvent(new InventoryTransferredDomainEvent(
            Id,
            destinationInventory.Id,
            ProductId,
            quantity,
            Location,
            destinationInventory.Location));

        return Result.Success();
    }
    
    public Result TransferVariantStock(
        ProductInventory destinationInventory,
        Guid variantId,
        PackagingOption packaging,
        int quantity,
        string reference)
    {
        var removeResult = RemoveVariantStock(
            variantId,
            packaging,
            quantity,
            $"Transfer to {destinationInventory.Location}: {reference}");

        if (removeResult.IsFailure)
        {
            return removeResult;
        }

        var addResult = destinationInventory.AddVariantStock(
            variantId,
            packaging,
            quantity,
            $"Transfer from {Location}: {reference}");

        if (addResult.IsFailure)
        {
            // Rollback both the variant stock levels and transactions
            var variantPackaging = VariantPackaging.Create(variantId, packaging.Type, packaging.Capacity);
            var currentQuantity = _variantStockLevelsCache.GetValueOrDefault(variantPackaging, 0);
            UpdateVariantStockLevel(variantPackaging, currentQuantity + quantity);
            
            // Remove the last base transaction and variant transaction
            _transactions.RemoveAt(_transactions.Count - 1);
            _variantTransactions.RemoveAt(_variantTransactions.Count - 1);
            return addResult;
        }

        RaiseDomainEvent(new VariantInventoryTransferredDomainEvent(
            Id,
            destinationInventory.Id,
            ProductId,
            variantId,
            packaging,
            quantity,
            Location,
            destinationInventory.Location));

        return Result.Success();
    }
    
    // Add methods to get transaction history
    public IEnumerable<ProductInventoryTransaction> GetVariantTransactionHistory(
        Guid variantId,
        PackagingOption packaging,
        DateTime? from = null,
        DateTime? to = null)
    {
        return _variantTransactions
            .Where(t => 
                t.VariantId == variantId &&
                t.Packaging.Type == packaging.Type &&
                t.Packaging.Capacity == packaging.Capacity &&
                (!from.HasValue || t.BaseTransaction.Timestamp >= from) &&
                (!to.HasValue || t.BaseTransaction.Timestamp <= to))
            .OrderBy(t => t.BaseTransaction.Timestamp);
    }
    
    public Result UpdateReorderPoints(
        int minimumStockLevel,
        int reorderPoint)
    {
        if (minimumStockLevel < 0)
        {
            return Result.Failure(InventoryErrors.InvalidMinimumStockLevel());
        }

        if (reorderPoint < minimumStockLevel)
        {
            return Result.Failure(InventoryErrors.InvalidReorderPoint());
        }

        var result = UpdateReorderPointsBase(minimumStockLevel, reorderPoint);
        
        if (result.IsSuccess)
        {
            RaiseDomainEvent(new ReorderPointsUpdatedDomainEvent(
                Id,
                ProductId,
                Location,
                MinimumStockLevel,
                ReorderPoint));
        }

        return result;
    }
    
    protected override void RaiseStockAddedEvent(int quantity)
    {
        RaiseDomainEvent(new ProductStockAddedDomainEvent(
            Id,
            ProductId,
            Location,
            quantity,
            CurrentStock));
    }

    protected override void RaiseStockRemovedEvent(int quantity)
    {
        RaiseDomainEvent(new ProductStockRemovedDomainEvent(
            Id,
            ProductId,
            Location,
            quantity,
            CurrentStock));
    }

    protected override void RaiseLowStockEvent()
    {
        RaiseDomainEvent(new ProductLowStockLevelDetectedDomainEvent(
            Id,
            ProductId,
            Location,
            CurrentStock,
            ReorderPoint));
    }
}