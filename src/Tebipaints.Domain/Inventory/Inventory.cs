using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Domain.Inventory;

public abstract class Inventory<TQuantity> : Entity where TQuantity : struct
{
    protected readonly List<InventoryTransaction<TQuantity>> _transactions = new();

    protected Inventory(){}
    
    protected Inventory(
        Guid id,
        InventoryLocation location,
        TQuantity minimumStockLevel,
        TQuantity reorderPoint) : base(id)
    {
        Location = location;
        MinimumStockLevel = minimumStockLevel;
        ReorderPoint = reorderPoint;
    }
    
    public InventoryLocation Location { get; private set; }
    public TQuantity MinimumStockLevel { get; private set; }
    public TQuantity ReorderPoint { get; private set; }
    public IReadOnlyCollection<InventoryTransaction<TQuantity>> Transactions => _transactions.AsReadOnly();
    
    /// <summary>
    /// Gets the current stock level based on all transactions.
    /// </summary>
    public abstract TQuantity CurrentStock { get; }
    
    /// <summary>
    /// Indicates whether the current stock level is at or below the reorder point.
    /// </summary>
    public abstract bool IsLowStock { get; }


    /// <summary>
    /// Adds stock to the inventory.
    /// </summary>
    /// <param name="quantity">The quantity to add</param>
    /// <param name="reference">A reference note for the transaction (e.g., "Production Batch #123")</param>
    /// <returns>Success if stock was added, or failure if the quantity is invalid</returns>
    /// <remarks>
    /// This operation:
    /// - Creates a StockIn transaction
    /// - Updates the current stock level
    /// - Raises an InventoryStockAdded event
    /// - Checks and raises LowStockLevel event if applicable
    /// </remarks>
    public abstract Result AddStock(TQuantity quantity, string reference);

    /// <summary>
    /// Removes stock from the inventory.
    /// </summary>
    /// <param name="quantity">The quantity to remove</param>
    /// <param name="reference">A reference note for the transaction (e.g., "Sales Order #123")</param>
    /// <returns>Success if stock was removed, or failure if the quantity is invalid or insufficient stock</returns>
    /// <remarks>
    /// This operation:
    /// - Validates sufficient stock exists
    /// - Creates a StockOut transaction
    /// - Updates the current stock level
    /// - Raises an InventoryStockRemoved event
    /// </remarks>
    public abstract Result RemoveStock(TQuantity quantity, string reference);
    
    // Add method to update reorder points in base class
    protected Result UpdateReorderPointsBase(TQuantity minimumStockLevel, TQuantity reorderPoint)
    {
        MinimumStockLevel = minimumStockLevel;
        ReorderPoint = reorderPoint;
        return Result.Success();
    }
    
    protected abstract void RaiseStockAddedEvent(TQuantity quantity);
    protected abstract void RaiseStockRemovedEvent(TQuantity quantity);
    protected abstract void RaiseLowStockEvent();
}