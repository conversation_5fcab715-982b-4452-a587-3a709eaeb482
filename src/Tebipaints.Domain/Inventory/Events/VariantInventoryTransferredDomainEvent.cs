using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Product;

namespace Tebipaints.Domain.Inventory.Events;

public sealed record VariantInventoryTransferredDomainEvent(
    Guid SourceInventoryId,
    Guid DestinationInventoryId,
    Guid ProductId,
    Guid VariantId,
    PackagingOption Packaging,
    int Quantity,
    InventoryLocation SourceLocation,
    InventoryLocation DestinationLocation) : IDomainEvent;