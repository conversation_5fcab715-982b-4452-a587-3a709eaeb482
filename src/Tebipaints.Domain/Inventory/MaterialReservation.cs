namespace Tebipaints.Domain.Inventory;

public record MaterialReservation
{
    private MaterialReservation(
        Guid materialId,
        double quantity,
        string purpose,
        DateTime createdAt)
    {
        MaterialId = materialId;
        Quantity = quantity;
        Purpose = purpose;
        CreatedAt = createdAt;
    }

    public Guid MaterialId { get; }
    public double Quantity { get; }
    public string Purpose { get; }
    public DateTime CreatedAt { get; }

    public static MaterialReservation Create(
        Guid materialId,
        double quantity,
        string purpose) =>
        new(
            materialId,
            quantity,
            purpose,
            DateTime.UtcNow);
}