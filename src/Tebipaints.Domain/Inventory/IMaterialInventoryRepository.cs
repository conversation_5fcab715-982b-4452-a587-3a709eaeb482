namespace Tebipaints.Domain.Inventory;

public interface IMaterialInventoryRepository
{
    Task<MaterialInventory?> GetByIdAsync(Guid materialId, CancellationToken cancellationToken);
    Task<List<MaterialInventory>> GetByIdsAsync(List<Guid> materialIds, CancellationToken cancellationToken);
    Task<Dictionary<Guid, MaterialInventory>> GetMaterialInventoriesAsync(List<Guid> materialIds);
    void Add(MaterialInventory materialInventory, CancellationToken cancellationToken);
    Task AddReservationAsync(MaterialReservation materialReservation);
}