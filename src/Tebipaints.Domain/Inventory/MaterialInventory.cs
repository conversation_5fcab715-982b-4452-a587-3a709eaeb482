using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Inventory.Events;

namespace Tebipaints.Domain.Inventory;

public sealed class MaterialInventory : Inventory<double>
{
    private readonly List<MaterialReservation> _reservations = [];
    
    private MaterialInventory(){}
    public MaterialInventory(
        Guid id,
        Guid materialId,
        InventoryLocation location,
        double minimumStockLevel,
        double reorderPoint) : base(id, location, minimumStockLevel, reorderPoint)
    {
        MaterialId = materialId;
    }
    
    public Guid MaterialId { get; private set; }
    public IReadOnlyCollection<MaterialReservation> Reservations => _reservations.AsReadOnly();
    public override double CurrentStock => _transactions.Sum(t => t.GetStockImpact());
    public double ReservedStock => _reservations.Sum(r => r.Quantity);
    public double AvailableStock => CurrentStock - ReservedStock;
    public override bool IsLowStock => CurrentStock <= ReorderPoint;

    public static Result<MaterialInventory> Create(
        Guid materialId,
        InventoryLocation location,
        double minimumStockLevel,
        double reorderPoint)
    {
        if (materialId == Guid.Empty)
        {
            return Result.Failure<MaterialInventory>(InventoryErrors.InvalidMaterialId());
        }

        if (minimumStockLevel < 0)
        {
            return Result.Failure<MaterialInventory>(InventoryErrors.InvalidMinimumStockLevel());
        }

        if (reorderPoint < minimumStockLevel)
        {
            return Result.Failure<MaterialInventory>(InventoryErrors.InvalidReorderPoint());
        }

        var inventory = new MaterialInventory(
            Guid.NewGuid(),
            materialId,
            location,
            minimumStockLevel,
            reorderPoint);
        
        return Result.Success(inventory);
    }
    
    public Result<MaterialReservation> ReserveStock(double quantity, string purpose)
    {
        if (quantity <= 0)
        {
            return Result.Failure<MaterialReservation>(InventoryErrors.InvalidQuantity());
        }

        if (quantity > AvailableStock)
        {
            return Result.Failure<MaterialReservation>(InventoryErrors.InsufficientStock());
        }

        var reservation = MaterialReservation.Create(MaterialId, quantity, purpose);
        _reservations.Add(reservation);

        RaiseDomainEvent(new MaterialStockReservedDomainEvent(
            Id,
            MaterialId,
            Location,
            quantity,
            AvailableStock));

        return Result.Success(reservation);
    }
    
    public Result CancelReservation(double quantity, string reason)
    {
        if (quantity <= 0)
        {
            return Result.Failure(InventoryErrors.InvalidQuantity());
        }

        if (quantity > ReservedStock)
        {
            return Result.Failure(InventoryErrors.InvalidReservationQuantity());
        }

        // Find reservations to cancel
        var remainingQuantity = quantity;
        var reservationsToRemove = new List<MaterialReservation>();

        foreach (var reservation in _reservations)
        {
            if (remainingQuantity <= 0) break;

            if (reservation.Quantity <= remainingQuantity)
            {
                reservationsToRemove.Add(reservation);
                remainingQuantity -= reservation.Quantity;
            }
        }

        if (remainingQuantity > 0)
        {
            return Result.Failure(InventoryErrors.ReservationNotFound());
        }

        foreach (var reservation in reservationsToRemove)
        {
            _reservations.Remove(reservation);
        }

        RaiseDomainEvent(new MaterialReservationCancelledDomainEvent(
            Id,
            MaterialId,
            Location,
            quantity,
            AvailableStock));

        return Result.Success();
    }
    
    /// <summary>
    /// Consumes previously reserved material stock.
    /// </summary>
    public Result ConsumeReservedStock(double quantity, string reference)
    {
        if (quantity <= 0)
        {
            return Result.Failure(InventoryErrors.InvalidQuantity());
        }

        if (quantity > ReservedStock)
        {
            return Result.Failure(InventoryErrors.InsufficientReservedStock());
        }

        // Create stock out transaction
        var transaction = InventoryTransaction<double>.Create(
            TransactionType.StockOut,
            quantity,
            reference);

        _transactions.Add(transaction);

        // Remove corresponding reservations
        var remainingQuantity = quantity;
        var reservationsToRemove = new List<MaterialReservation>();

        foreach (var reservation in _reservations)
        {
            if (remainingQuantity <= 0) break;

            if (reservation.Quantity <= remainingQuantity)
            {
                reservationsToRemove.Add(reservation);
                remainingQuantity -= reservation.Quantity;
            }
        }

        foreach (var reservation in reservationsToRemove)
        {
            _reservations.Remove(reservation);
        }

        RaiseDomainEvent(new MaterialReservedStockConsumedDomainEvent(
            Id,
            MaterialId,
            Location,
            quantity,
            CurrentStock,
            ReservedStock));

        if (IsLowStock)
        {
            RaiseLowStockEvent();
        }

        return Result.Success();
    }

    public override Result AddStock(double quantity, string reference)
    {
        if (quantity <= 0)
        {
            return Result.Failure(InventoryErrors.InvalidQuantity());
        }
        
        var transaction = InventoryTransaction<double>.Create(
            TransactionType.StockIn,
            quantity,
            reference);

        _transactions.Add(transaction);
        
        RaiseStockAddedEvent(quantity);

        if (IsLowStock)
        {
            RaiseLowStockEvent();
        }
        
        return Result.Success();
    }

    public override Result RemoveStock(double quantity, string reference)
    {
        if (quantity <= 0)
        {
            return Result.Failure(InventoryErrors.InvalidQuantity());
        }

        if (quantity > AvailableStock) // Note: Using AvailableStock instead of CurrentStock to account for reservations
        {
            return Result.Failure(InventoryErrors.InsufficientStock());
        }

        var transaction = InventoryTransaction<double>.Create(
            TransactionType.StockOut,
            quantity,
            reference);

        _transactions.Add(transaction);
        
        RaiseStockRemovedEvent(quantity);

        if (IsLowStock)
        {
            RaiseLowStockEvent();
        }

        return Result.Success();
    }

    public Result UpdateReorderPoints(
        double minimumStockLevel,
        double reorderPoint)
    {
        if (minimumStockLevel < 0)
        {
            return Result.Failure(InventoryErrors.InvalidMinimumStockLevel());
        }

        if (reorderPoint < minimumStockLevel)
        {
            return Result.Failure(InventoryErrors.InvalidReorderPoint());
        }

        var result = UpdateReorderPointsBase(minimumStockLevel, reorderPoint);

        if (result.IsSuccess)
        {
            RaiseDomainEvent(new MaterialReorderPointsUpdatedDomainEvent(
                Id,
                MaterialId,
                Location,
                MinimumStockLevel,
                ReorderPoint));
        }

        return result;
    }

    protected override void RaiseStockAddedEvent(double quantity)
    {
        RaiseDomainEvent(new MaterialStockAddedDomainEvent(
            Id,
            MaterialId,
            Location,
            quantity,
            CurrentStock));
    }

    protected override void RaiseStockRemovedEvent(double quantity)
    {
        RaiseDomainEvent(new MaterialStockRemovedDomainEvent(
            Id,
            MaterialId,
            Location,
            quantity,
            CurrentStock));
    }

    protected override void RaiseLowStockEvent()
    {
        RaiseDomainEvent(new MaterialLowStockLevelDetectedDomainEvent(
            Id,
            MaterialId,
            Location,
            CurrentStock,
            ReorderPoint));
    }
}