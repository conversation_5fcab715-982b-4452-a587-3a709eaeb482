namespace Tebipaints.Domain.Inventory;

public interface IProductInventoryRepository
{
    Task<ProductInventory?> GetByIdAsync(Guid productId, CancellationToken cancellationToken);
    Task<ProductInventory?> GetByProductAndLocationAsync(Guid productId, InventoryLocation location, CancellationToken cancellationToken);
    Task<List<ProductInventory>> GetByIdsAsync(List<Guid> productIds, CancellationToken cancellationToken);
    void Add(ProductInventory productInventory);
}