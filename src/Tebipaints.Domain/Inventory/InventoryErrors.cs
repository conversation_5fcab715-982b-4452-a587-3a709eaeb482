using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Product;

namespace Tebipaints.Domain.Inventory;

public class InventoryErrors
{
    public static Error InvalidProductId() => new(
        "Inventory.InvalidProductId",
        "The product id is invalid.");
    
    public static Error InvalidInventoryLocation() => new(
        "Inventory.InvalidInventoryLocation",
        "The inventory location provided is invalid.");
    
    public static Error InvalidMaterialId() => new(
        "Inventory.InvalidMaterialId",
        "The material id is invalid.");
    
    public static Error InvalidMinimumStockLevel() => new(
        "Inventory.InvalidMinimumStockLevel",
        "The minimum stock level cannot be less than or equal to zero.");
    
    public static Error InvalidReorderPoint() => new(
        "Inventory.InvalidReorderPoint",
        "The reorder point cannot be less than the minimum stock level.");
    
    public static Error InvalidQuantity() => new(
        "Inventory.InvalidQuantity",
        "The quantity cannot be less than or equal to zero.");
    
    public static Error InsufficientStock() => new(
        "Inventory.InsufficientStock",
        "The stock level is too low to fulfill request.");
    
    public static Error ReservationNotFound() => new(
        "Inventory.ReservationNotFound",
        "The reservation was not found.");

    public static Error InsufficientVariantStock(Guid variantId, PackagingOption packaging) => new(
        "Inventory.InsufficientVariantStock",
        $"The product variant {variantId} with packaging option {packaging} has insufficient stock.");

    public static Error AlreadyExists(Guid commandProductId, string location) => new(
        "Inventory.AlreadyExists",
        $"The product with id {commandProductId} already exists in inventory at {location}.");

    public static Error InvalidPackagingType() => new(
        "Inventory.InvalidPackagingType",
        "The packaging type is invalid.");

    public static Error NotFound(Guid inventoryId) => new(
        "Inventory.NotFound", "The specified inventory does not exist.");

    public static Error InvalidReservationQuantity() => new(
        "Inventory.InvalidReservationQuantity",
        "The reservation quantity cannot be greater than the sum of reserved stock.");

    public static Error InsufficientReservedStock() => new(
        "Inventory.InsufficientReservedStock",
        "The reservation quantity cannot be more than the sum of reserved stock.");
}