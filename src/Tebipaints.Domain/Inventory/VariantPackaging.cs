using Tebipaints.Domain.Product;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Inventory;

public record VariantPackaging
{
    private VariantPackaging() { }
    
    private VariantPackaging(Guid variantId, PackagingType type, Measurement capacity)
    {
        VariantId = variantId;
        Type = type;
        Capacity = capacity;
    }

    public Guid VariantId { get; }
    public PackagingType Type { get; }
    public Measurement Capacity { get; }

    public static VariantPackaging Create(Guid variantId, PackagingType type, Measurement capacity)
    {
        if (variantId == Guid.Empty)
        {
            throw new ArgumentException("Variant ID cannot be empty", nameof(variantId));
        }

        return new VariantPackaging(variantId, type, capacity);
    }
}