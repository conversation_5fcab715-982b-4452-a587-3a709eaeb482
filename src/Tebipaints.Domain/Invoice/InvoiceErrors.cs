using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Domain.Invoice;

public class InvoiceErrors
{
    public static Error InvoiceNotFound() => new (
        "Invoice.InvoiceNotFound",
        "An invoice with this specified identifier was not found");

    public static Error EmptyLineItems() => new(
        "Invoice.EmptyLineItems",
        "An invoice cannot have an empty list of items");

    public static Error PaymentsNotFound() => new(
        "Invoice.PaymentsNotFound",
        "No payments have been made for this invoice");

    public static Error ReceiptNotIssued() => new(
        "Invoice.ReceiptNotIssued",
        "The receipt could not be issued.");

    public static Error InvalidInvoiceStatus() => new(
        "Invoice.InvalidInvoiceStatus",
        "Invoice is not in a valid state for this action");

    public static Error InvalidPaymentAmount() => new(
        "Invoice.InvalidPaymentAmount",
        "The payment amount specified for the invoice is not valid");

    public static Error PaymentExceedsAmountDue() => new(
        "Invoice.PaymentExceedsAmountDue",
        "The payment exceeds the amount due for this invoice");

    public static Error InvalidDueDate() => new(
        "Invoice.InvalidDueDate",
        "Due date must be in the future.");

    public static Error CannotMarkAsOverdue() => new(
        "Invoice.CannotMarkAsOverdue",
        "Cannot mark invoice as overdue. Invoice is either paid, voided, or written off.");

    public static Error InvoiceNotDueYet() => new(
        "Invoice.InvoiceNotDueYet",
        "Invoice cannot be marked as overdue before its due date.");
    
    public static Error AlreadyOverdue() => new(
        "Invoice.AlreadyOverdue", 
        "Invoice is already marked as overdue.");

    public static Error NoPaymentFound() => new(
        "Invoice.NoPaymentFound",
        "Receipt must contain at least one payment.");
    
    public static Error InvalidPayments() => new(
        "Invoice.InvalidPayments", 
        "One or more payments do not belong to this invoice.");
    
    public static Error PaymentsAlreadyReceipted() => new(
        "Invoice.PaymentsAlreadyReceipted", 
        "One or more payments have already been receipted.");
    
    public static Error ReceiptCreationFailed() => new(
        "Invoice.ReceiptCreationFailed", 
        "Failed to create receipt.");
    
    public static Error CannotRefundInvoice() =>
        new("Invoice.CannotRefund", 
            "Cannot refund invoice. Invoice must be paid or partially paid.");

    public static Error InvalidRefundAmount() =>
        new("Invoice.InvalidRefundAmount", 
            "Refund amount must be greater than zero.");

    public static Error RefundExceedsAmountPaid() =>
        new("Invoice.RefundExceedsAmountPaid", 
            "Refund amount cannot exceed the amount paid.");

    public static Error MissingCustomerInformation() => new(
        "Invoice.MissingCustomerInformation",
        "Either a customer id or customer name should be provided.");

    public static Error AmbiguousCustomerInformation() => new(
        "Invoice.AmbiguousCustomerInformation",
        "Either a customer id or customer name should be provided not both.");

    public static Error NotFound(Guid requestInvoiceId) => new(
        "Invoice.NotFound",
        $"Invoice with id {requestInvoiceId} could not be found.");

    public static Error NotAnAcceptablePaymentMethod() => new(
        "Invoice.NotAnAcceptablePaymentMethod",
        "The supplied payment method is not acceptable.");

    public static Error PaymentRequired() => new(
        "Invoice.PaymentRequired",
        "A payment is needed for a receipt.");
}