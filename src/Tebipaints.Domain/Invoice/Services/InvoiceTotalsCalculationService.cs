using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Invoice.Services;

public class InvoiceTotalsCalculationService
{
    private readonly decimal _taxRate;

    public InvoiceTotalsCalculationService(decimal taxRate = 0.15m)
    {
        _taxRate = taxRate;
    }

    public InvoiceTotals CalculateInvoiceTotals(
        List<LineItem> lineItems,
        Money amountPaid,
        Money orderLevelDiscount)
    {
        var subTotal = CalculateSubTotal(lineItems);
        
        // Sum all line item discounts
        var totalLineItemDiscounts = new Money(
            lineItems.Sum(item => item.LineItemDiscount.Amount),
            Currency.Ghs);

        // Calculate amount after all discounts
        var amountAfterDiscounts = new Money(
            subTotal.Amount - totalLineItemDiscounts.Amount - orderLevelDiscount.Amount,
            Currency.Ghs);

        // Calculate tax on the discounted amount
        var taxAmount = new Money(
            amountAfterDiscounts.Amount * _taxRate,
            Currency.Ghs);

        // Calculate grand total
        var grandTotal = new Money(
            amountAfterDiscounts.Amount + taxAmount.Amount,
            Currency.Ghs);

        // Calculate amount due
        var amountDue = new Money(
            grandTotal.Amount - amountPaid.Amount,
            Currency.Ghs);

        return new InvoiceTotals(
            SubTotal: subTotal,
            LineItemDiscounts: totalLineItemDiscounts,
            OrderLevelDiscounts: orderLevelDiscount,
            TaxAmount: taxAmount,
            GrandTotal: grandTotal,
            AmountDue: amountDue);
    }

    private Money CalculateSubTotal(IEnumerable<LineItem> lineItems)
    {
        return new Money(
            lineItems.Sum(item => item.TotalPrice.Amount),
            Currency.Ghs);
    }
}

public record InvoiceTotals(
    Money SubTotal,
    Money LineItemDiscounts,
    Money OrderLevelDiscounts,
    Money TaxAmount,
    Money GrandTotal,
    Money AmountDue);