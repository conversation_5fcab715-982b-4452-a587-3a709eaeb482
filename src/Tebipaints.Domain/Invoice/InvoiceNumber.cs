namespace Tebipaints.Domain.Invoice;

public record InvoiceNumber
{
    private const string Prefix = "TBP-INV";
    private const int SequenceLength = 6;
    
    public string Value { get; }
    
    private InvoiceNumber(string value) => Value = value;

    public static InvoiceNumber Create(int sequence, DateTime? date = null)
    {
        date ??= DateTime.UtcNow;
        string datePart = date.Value.ToString("yyyyMM");
        string sequencePart = sequence.ToString($"D{SequenceLength}");
        
        return new InvoiceNumber($"{Prefix}-{datePart}-{sequencePart}");
    }

    public static bool TryParse(string value, out InvoiceNumber? number)
    {
        number = null;
        if (string.IsNullOrWhiteSpace(value)) return false;
        
        var pattern = $@"^{Prefix}-(\d{{6}})-(\d{{{SequenceLength}}})$";
        var match = System.Text.RegularExpressions.Regex.Match(value, pattern);
        
        if (!match.Success) return false;

        number = new InvoiceNumber(value);

        return true;
    }
    
    public override string ToString() => Value;
}