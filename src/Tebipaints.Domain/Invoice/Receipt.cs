using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Invoice;

public sealed record Receipt
{
    private Receipt(){}
    
    private Receipt(
        ReceiptNumber receiptNumber,
        Guid invoiceId,
        Guid paymentId,
        DateTime issuedDate,
        Money totalAmount)
    {
        IssuedDate = issuedDate;
        Amount = totalAmount;
        InvoiceId = invoiceId;
        Number = receiptNumber;
        PaymentId = paymentId;
    }
    
    public ReceiptNumber Number { get; private set; }
    public Guid InvoiceId { get; private set; }
    public Guid PaymentId { get; private set; }
    public DateTime IssuedDate { get; private set; }
    public Money Amount { get; private set; }

    public static Result<Receipt> Create(
        ReceiptNumber number,
        Guid invoiceId,
        Payment? payment)
    {
        if (payment is null)
        {
            return Result.Failure<Receipt>(
                InvoiceErrors.PaymentRequired());
        }

        var receipt = new Receipt(
            number,
            invoiceId,
            payment.Id,
            DateTime.UtcNow,
            payment.Amount);

        return Result.Success(receipt);
    }
}