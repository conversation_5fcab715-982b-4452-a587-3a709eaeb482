namespace Tebipaints.Domain.Invoice;

public record ReceiptNumber
{
    private const string Prefix = "TBP-RCP";
    private const int SequenceLength = 6;
    
    public string Value { get; }
    
    private ReceiptNumber(string value) => Value = value;
    
    public static ReceiptNumber Create(int sequence, DateTime? date = null)
    {
        date ??= DateTime.UtcNow;
        string datePart = date.Value.ToString("yyyyMM");
        string sequencePart = sequence.ToString($"D{SequenceLength}");
        
        return new ReceiptNumber($"{Prefix}-{datePart}-{sequencePart}");
    }

    public static bool TryParse(string value, out ReceiptNumber? number)
    {
        number = null;
        if (string.IsNullOrWhiteSpace(value)) return false;
        
        var pattern = $@"^{Prefix}-(\d{{6}})-(\d{{{SequenceLength}}})$";
        var match = System.Text.RegularExpressions.Regex.Match(value, pattern);
        
        if (!match.Success) return false;

        number = new ReceiptNumber(value);

        return true;
    }
    
    public override string ToString() => Value;
}