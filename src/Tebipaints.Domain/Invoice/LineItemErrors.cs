using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Domain.Invoice;

public class LineItemErrors
{
    public static Error InvalidProductId() =>
        new("LineItem.InvalidProductId", 
            "Product ID must be provided.");

    public static Error InvalidDescription() =>
        new("LineItem.InvalidDescription", 
            "Description must not be empty.");

    public static Error InvalidUnitPrice() =>
        new("LineItem.InvalidUnitPrice", 
            "Unit price must be greater than zero.");

    public static Error InvalidQuantity() =>
        new("LineItem.InvalidQuantity", 
            "Quantity must be greater than zero.");
    
    public static Error InvalidSku() =>
        new("LineItem.InvalidSku", 
            "SKU must not be empty.");

    public static Error InvalidDiscountAmount() => new(
        "LineItem.InvalidDiscountAmount",
        "Discount amount must be greater than zero.");
}