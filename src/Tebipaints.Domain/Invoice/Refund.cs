using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Invoice;

public class Refund : Entity
{
    private Refund(){}
    
    internal Refund(
        Guid id,
        Guid invoiceId,
        Money amount,
        string reason,
        DateTime refundDate,
        RefundStatus status = RefundStatus.Pending) : base(id)
    {
        InvoiceId = invoiceId;
        Amount = amount;
        Reason = reason;
        RefundDate = refundDate;
        Status = status;
    }

    public Guid InvoiceId { get; private set; }
    public Money Amount { get; private set; }
    public string Reason { get; private set; }
    public DateTime RefundDate { get; private set; }
    public RefundStatus Status { get; private set; }
    public DateTime? ProcessedDate { get; private set; }

    internal void MarkAsProcessed()
    {
        Status = RefundStatus.Processed;
        ProcessedDate = DateTime.UtcNow;
    }

    internal void MarkAsFailed(string failureReason)
    {
        Status = RefundStatus.Failed;
        Reason = failureReason;
    }
}

public enum RefundStatus
{
    Pending,
    Processed,
    Failed
}