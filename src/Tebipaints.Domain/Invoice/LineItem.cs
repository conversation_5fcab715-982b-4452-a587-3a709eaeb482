using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Invoice.Events;
using Tebipaints.Domain.Invoice.Services;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.Shared;


namespace Tebipaints.Domain.Invoice;
public sealed class LineItem : Entity
{
    private LineItem(){}
    
    private LineItem(
        Guid id,
        Guid productId,
        SKU sku,
        string description,
        int quantity,
        Money unitPrice,
        Money lineItemDiscount) : base(id)
    {
        UnitPrice = unitPrice;
        Quantity = quantity;
        ProductId = productId;
        Description = description;
        Sku = sku;
        LineItemDiscount = lineItemDiscount;
    }
    
    public Guid ProductId { get; private set; }
    public SKU Sku { get; private set; }
    public string Description { get; private set; }
    public Money UnitPrice { get; private set; }
    public Money LineItemDiscount { get; private set; }
    public int Quantity { get; private set; }
    
    public Money SubTotal => new(
        UnitPrice.Amount * Quantity,
        UnitPrice.Currency);
    
    public Money TotalPrice => new(
        UnitPrice.Amount * Quantity,
        UnitPrice.Currency);

    /// <summary>
    /// Creates a new line item for an invoice.
    /// </summary>
    /// <param name="productId">The ID of the product being invoiced</param>
    /// <param name="sku">The Stock Keeping Unit (SKU) of the product</param>
    /// <param name="description">Description of the item</param>
    /// <param name="unitPrice">Price per unit</param>
    /// <param name="quantity">Number of units</param>
    /// <param name="lineItemDiscount"></param>
    /// <returns>A Result containing the new LineItem if successful, or failure details if invalid</returns>
    /// <remarks>
    /// A line item can be created if:
    /// - Product ID is valid
    /// - SKU is not empty
    /// - Description is not empty
    /// - Unit price is greater than zero
    /// - Quantity is greater than zero
    /// </remarks>
    public static Result<LineItem> Create(
        Guid productId,
        SKU sku,
        string description,
        Money unitPrice,
        int quantity,
        Money lineItemDiscount)
    {
        if (productId == Guid.Empty)
        {
            return Result.Failure<LineItem>(LineItemErrors.InvalidProductId());
        }
        
        if (string.IsNullOrWhiteSpace(sku.Value))
        {
            return Result.Failure<LineItem>(LineItemErrors.InvalidSku());
        }

        if (string.IsNullOrWhiteSpace(description))
        {
            return Result.Failure<LineItem>(LineItemErrors.InvalidDescription());
        }

        if (unitPrice.Amount <= 0)
        {
            return Result.Failure<LineItem>(LineItemErrors.InvalidUnitPrice());
        }

        if (quantity <= 0)
        {
            return Result.Failure<LineItem>(LineItemErrors.InvalidQuantity());
        }
        
        if (lineItemDiscount.Amount < 0 || lineItemDiscount.Amount > unitPrice.Amount * quantity)
        {
            return Result.Failure<LineItem>(LineItemErrors.InvalidDiscountAmount());
        }

        var lineItem = new LineItem(
            Guid.NewGuid(),
            productId,
            sku,
            description.Trim(),
            quantity,
            unitPrice,
            lineItemDiscount);
        
        lineItem.RaiseDomainEvent(new LineItemCreatedDomainEvent(lineItem.Id, lineItem.Sku.Value));
        
        return Result.Success(lineItem);
    }
    
    /// <summary>
    /// Updates the quantity of the line item.
    /// </summary>
    /// <param name="newQuantity">The new quantity to set</param>
    /// <returns>Success if the quantity was updated, or failure if the quantity is invalid</returns>
    public Result UpdateQuantity(int newQuantity)
    {
        if (newQuantity <= 0)
        {
            return Result.Failure(LineItemErrors.InvalidQuantity());
        }

        Quantity = newQuantity;
        
        RaiseDomainEvent(new LineItemQuantityUpdatedDomainEvent(Id, newQuantity));
        
        return Result.Success();
    }
}