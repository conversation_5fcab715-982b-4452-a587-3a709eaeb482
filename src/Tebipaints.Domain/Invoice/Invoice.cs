using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Invoice.Events;
using Tebipaints.Domain.Invoice.Services;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Invoice;

public sealed class Invoice : Entity
{
    private readonly List<LineItem> _lineItems = new();
    private readonly List<Payment> _payments = new();
    private readonly List<Refund> _refunds = new();
    
    private Invoice(){}
    
    private Invoice(
        Guid id,
        Guid? customerId,
        string? walkInCustomerName,
        InvoiceNumber invoiceNumber,
        Money subtotal,
        Money tax,
        Money grandTotal,
        Money amountPaid,
        Money amountDue,
        DateTime dueDate,
        DateTime createdOnUtc,
        InvoiceStatus status,
        List<LineItem> lineItems) : base(id)
    {
        CustomerId = customerId;
        InvoiceNumber = invoiceNumber;
        Subtotal = subtotal;
        Tax = tax;
        GrandTotal = grandTotal;
        AmountPaid = amountPaid;
        AmountDue = amountDue;
        DueDate = dueDate;
        CreatedOnUtc = createdOnUtc;
        Status = status;
        _lineItems = lineItems;
    }
    
    public Guid? CustomerId { get; private set; }
    public string? WalkInCustomerName { get; private set; }
    public InvoiceNumber InvoiceNumber { get; private set; }
    public Money Subtotal { get; private set; }
    public Money Tax { get; private set; }
    public Money GrandTotal { get; private set; }
    public Money AmountPaid { get; private set; }
    public Money AmountDue { get; private set; }
    public DateTime DueDate { get; private set; }
    public DateTime CreatedOnUtc { get; private set; }
    public InvoiceStatus Status { get; private set; }
    public IReadOnlyCollection<LineItem> LineItems => _lineItems.AsReadOnly();
    public IReadOnlyCollection<Payment> Payments => _payments.AsReadOnly();
    public IReadOnlyCollection<Refund> Refunds => _refunds.AsReadOnly();

    /// <summary>
    /// Creates a new invoice with the specified details.
    /// </summary>
    /// <param name="invoiceId">The id of the invoice</param>
    /// <param name="customerId">The ID of the customer this invoice is for</param>
    /// <param name="walkInCustomerName"></param>
    /// <param name="invoiceNumber">A unique string of characters</param>
    /// <param name="amountPaid">The initial amount paid, if any</param>
    /// <param name="taxRate"></param>
    /// <param name="dueDate">The date by which the invoice must be paid</param>
    /// <param name="orderLevelDiscount"></param>
    /// <param name="lineItems">The list of items being invoiced</param>
    /// <param name="service">Service to calculate invoice totals</param>
    /// <returns>A Result containing the new Invoice if successful, or failure details if invalid</returns>
    public static Result<Invoice> Create(
        Guid? invoiceId,
        Guid? customerId,
        string? walkInCustomerName,
        InvoiceNumber invoiceNumber,
        decimal amountPaid,
        decimal taxRate,
        DateTime dueDate,
        Money orderLevelDiscount,
        List<LineItem> lineItems,
        InvoiceTotalsCalculationService service)
    {
        if (!customerId.HasValue && string.IsNullOrWhiteSpace(walkInCustomerName))
        {
            return Result.Failure<Invoice>(
                InvoiceErrors.MissingCustomerInformation());
        }

        if (customerId.HasValue && !string.IsNullOrWhiteSpace(walkInCustomerName))
        {
            return Result.Failure<Invoice>(
                InvoiceErrors.AmbiguousCustomerInformation());
        }

        if ( lineItems.Count == 0)
        {
            return Result.Failure<Invoice>(InvoiceErrors.EmptyLineItems());
        }

        var invoiceService = new InvoiceTotalsCalculationService(taxRate);
        var invoiceTotals = invoiceService.CalculateInvoiceTotals(
            lineItems,
            new Money(amountPaid, Currency.Ghs),
            orderLevelDiscount);

        var invoice = new Invoice(
            invoiceId ?? Guid.NewGuid(),
            customerId,
            walkInCustomerName,
            invoiceNumber,
            invoiceTotals.SubTotal,
            invoiceTotals.TaxAmount,
            invoiceTotals.GrandTotal,
            new Money(amountPaid, Currency.Ghs),
            invoiceTotals.AmountDue,
            dueDate,
            DateTime.UtcNow,
            InvoiceStatus.Draft,
            lineItems);
        
        invoice.RaiseDomainEvent(new InvoiceCreatedDomainEvent(invoice.Id));

        return Result.Success(invoice);
    }

    /// <summary>
    /// Issues the invoice to the customer, changing its status from Draft to Issued.
    /// </summary>
    /// <param name="dueDate">The date by which the invoice must be paid</param>
    /// <returns>Success if the invoice was issued, or failure if it cannot be issued</returns>
    /// <remarks>
    /// An invoice can only be issued if:
    /// - It is currently in Draft status
    /// - The due date is in the future
    /// </remarks>
    public Result IssueInvoice(DateTime dueDate)
    {
        if (Status != InvoiceStatus.Draft)
        {
            return Result.Failure(InvoiceErrors.InvalidInvoiceStatus());
        }
        
        if (dueDate <= DateTime.UtcNow)
        {
            return Result.Failure(InvoiceErrors.InvalidDueDate());
        }
        
        Status = InvoiceStatus.Issued;
        
        DueDate = dueDate;
        
        RaiseDomainEvent(new InvoiceIssuedDomainEvent(Id));
        
        return Result.Success();
    }

    /// <summary>
    /// Marks an invoice as overdue if it meets the overdue criteria.
    /// </summary>
    /// <returns>Success if the invoice was marked as overdue, or failure if it cannot be marked overdue</returns>
    /// <remarks>
    /// An invoice can be marked as overdue if:
    /// - It is not already Paid, Voided, or Written Off
    /// - The due date has passed
    /// - It is not already marked as Overdue
    /// </remarks>
    public Result MarkAsOverdue()
    {
        if (Status is InvoiceStatus.Paid or InvoiceStatus.Voided or InvoiceStatus.WrittenOff)
        {
            return Result.Failure(InvoiceErrors.CannotMarkAsOverdue());
        }

        if (DateTime.UtcNow < DueDate)
        {
            return Result.Failure(InvoiceErrors.InvoiceNotDueYet());
        }

        if (Status == InvoiceStatus.Overdue)
        {
            return Result.Failure(InvoiceErrors.AlreadyOverdue());
        }

        Status = InvoiceStatus.Overdue;
        
        RaiseDomainEvent(new InvoiceBecameOverdueDomainEvent(Id, DueDate));
        
        return Result.Success();
    }

    /// <summary>
    /// Records a payment against the invoice.
    /// </summary>
    /// <param name="payment">The payment to be recorded</param>
    /// <returns>Success if the payment was recorded, or failure if the payment cannot be accepted</returns>
    /// <remarks>
    /// A payment can be accepted if:
    /// - The invoice is in Issued or PartiallyPaid status
    /// - The payment amount is greater than zero
    /// - The payment amount does not exceed the remaining amount due
    /// 
    /// After a successful payment:
    /// - The amount paid is increased
    /// - The amount due is decreased
    /// - The status is updated to either Paid or PartiallyPaid
    /// </remarks>
    public Result MakePayment(Payment payment, int sequence)
    {
        if (payment.Amount.Amount <= 0)
        {
            return Result.Failure(InvoiceErrors.InvalidPaymentAmount());
        }

        if (Status is not (InvoiceStatus.Issued or InvoiceStatus.PartiallyPaid))
        {
            return Result.Failure(InvoiceErrors.InvalidInvoiceStatus());
        }

        if (payment.Amount.Amount > AmountDue.Amount)
        {
            return Result.Failure(InvoiceErrors.PaymentExceedsAmountDue());
        }
        
        _payments.Add(payment);
        
        // Create receipt
        var receiptNumberResult = ReceiptNumber.Create(sequence);

        var receiptResult = Receipt.Create(
            receiptNumberResult,
            Id,
            payment);

        if (receiptResult.IsFailure)
        {
            return Result.Failure(receiptResult.Error);
        }

        payment.AssignReceipt(receiptResult.Value);
        
        var updatedAmountDue = AmountDue.Amount - payment.Amount.Amount;
        
        AmountPaid = new Money(AmountPaid.Amount + payment.Amount.Amount, AmountDue.Currency);
        
        AmountDue = new Money(updatedAmountDue, AmountDue.Currency);

        Status = AmountDue.Amount switch
        {
            0 => InvoiceStatus.Paid,
            _ => InvoiceStatus.PartiallyPaid
        };
        
        RaiseDomainEvent(new InvoicePaymentMadeDomainEvent(
            Id, payment.Id, payment.Amount, receiptResult.Value.Number.ToString()));
        
        return Result.Success();
    }

    /// <summary>
    /// Issues a refund for the invoice.
    /// </summary>
    /// <param name="amount">The amount to be refunded</param>
    /// <param name="reason">The reason for the refund</param>
    /// <param name="refundDate">The date when the refund is processed</param>
    /// <returns>Success if the refund was processed, or failure if the refund cannot be issued</returns>
    /// <remarks>
    /// A refund can be issued if:
    /// - The invoice is in Paid or PartiallyPaid status
    /// - The refund amount is greater than zero
    /// - The refund amount does not exceed the amount paid
    /// - The total of all refunds (including this one) does not exceed the amount paid
    /// 
    /// After a successful refund:
    /// - The amount due is increased
    /// - The amount paid is decreased
    /// - The status is updated based on the remaining paid amount
    /// </remarks>
    public Result IssueRefund(Money amount, string reason, DateTime refundDate)
    {
        // validate refund is possible
        if (Status is not (InvoiceStatus.Paid or InvoiceStatus.PartiallyPaid))
        {
            return Result.Failure(InvoiceErrors.CannotRefundInvoice());
        }

        if (amount.Amount <= 0)
        {
            return Result.Failure(InvoiceErrors.InvalidRefundAmount());
        }

        var totalRefunded = new Money(
            _refunds.Sum(r => r.Amount.Amount),
            Currency.Ghs);
        
        var availableForRefund = AmountPaid.Amount - totalRefunded.Amount;

        if (amount.Amount > availableForRefund)
        {
            return Result.Failure(InvoiceErrors.RefundExceedsAmountPaid());
        }

        var refund = new Refund(
            Guid.NewGuid(),
            Id,
            amount,
            reason,
            refundDate);
        
        _refunds.Add(refund);
        
        // update invoice amounts
        AmountPaid = new Money(AmountPaid.Amount - amount.Amount, AmountPaid.Currency);
        AmountDue = new Money(AmountPaid.Amount + amount.Amount, AmountDue.Currency);
        
        // Update status based on remaining paid amount
        Status = AmountPaid.Amount switch
        {
            0 => InvoiceStatus.Refunded,
            _ when AmountPaid.Amount < GrandTotal.Amount => InvoiceStatus.PartiallyPaid,
            _ => Status // Keep current status if fully paid
        };

        RaiseDomainEvent(new InvoiceRefundedDomainEvent(Id, amount, reason));

        return Result.Success();
    }
}