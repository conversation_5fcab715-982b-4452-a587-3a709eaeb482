using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Domain.Invoice;

public class Payment : Entity
{
    private Payment(){}
    
    private Payment(
        Guid id,
        Guid invoiceId,
        Money amount,
        PaymentMethod method,
        string? reference,
        DateTime date) : base(id)
    {
        InvoiceId = invoiceId;
        Amount = amount;
        PaymentMethod = method;
        Reference = reference;
        Date = date;
    }
    
    public Guid InvoiceId { get; private set; }
    public Money Amount { get; private set; }
    public DateTime Date { get; private set; }
    public PaymentMethod PaymentMethod { get; private set; }
    public string? Reference  { get; private set; }
    public Receipt? Receipt { get; private set; }
    
    internal void AssignReceipt(Receipt receipt)
    {
        if (Receipt is not null)
        {
            throw new InvalidOperationException("Payment already has a receipt assigned.");
        }
        Receipt = receipt;
    }

    public static Result<Payment> Create(
        Guid invoiceId,
        Money amount,
        PaymentMethod paymentMethod,
        string? reference,
        DateTime date)
    {
        if (amount.Amount <= 0)
        {
            return Result.Failure<Payment>(
                InvoiceErrors.InvalidPaymentAmount());
        }

        var payment = new Payment(
            Guid.NewGuid(),
            invoiceId,
            amount,
            paymentMethod,
            reference,
            date);
        
        return Result.Success(payment);
    }
}