namespace Tebipaints.Domain.Helpers;

public static class NumericOperations<T> where T : struct
{
    public static T Negate(T value)
    {
        if (typeof(T) == typeof(int))
            return (T)(object)(-(int)(object)value);
        if (typeof(T) == typeof(double))
            return (T)(object)(-(double)(object)value);
        throw new NotSupportedException($"Type {typeof(T)} is not supported for negation.");
    }
}