using System.Text.RegularExpressions;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Product.Errors;

namespace Tebipaints.Domain.Shared;

public record SKU
{
    private const string FactoryCode = "GH01";
    private const int SequenceLength = 5;
    private static readonly Regex SkuPattern = 
        new(@"^[A-Z0-9]{3,5}-PNT-[A-Z]{2,3}-\d+(L|ML)-\d{4}-\d{5}$", RegexOptions.Compiled);
    public string Value { get; }
    
    private SKU() { }
    
    private SKU(string value) => this.Value = value;

    public static Result<SKU> Create( string color, string volume, int year, int month, int sequence)
    {
        // if (string.IsNullOrWhiteSpace(color) || color.Length is < 2 or > 3)
        //     throw new ArgumentException("Color code must be 2 to 3 uppercase letters.", nameof(color));

        if (!Regex.IsMatch(volume, @"^\d+(L|ML)$"))
            throw new ArgumentException("Volume must be in format like '10L' or '500ML'.", nameof(volume));

        if (year is < 2000 or > 2099)
            throw new ArgumentException("Year must be between 2000 and 2099.", nameof(year));

        if (month is < 1 or > 12)
            throw new ArgumentException("Month must be between 1 and 12.", nameof(month));

        if (sequence is < 1 or > 99999)
            throw new ArgumentException("Sequence number must be between 00001 and 99999.", nameof(sequence));

        var colorCode = GetColorCode(color);

        string formattedSku = $"{FactoryCode}-PNT-{colorCode}-{volume}-{year % 100:D2}{month:D2}-{sequence:D5}";

        return Result.Success(new SKU(formattedSku));
    }
    
    public static bool TryParse(string sku, out SKU? result)
    {
        if (!string.IsNullOrWhiteSpace(sku) && SkuPattern.IsMatch(sku))
        {
            result = new SKU(sku);
            return true;
        }

        result = null;
        return false;
    }

    public override string ToString() => Value;
    
    /// <summary>
    /// Converts a full color name into a standardized color code.
    /// </summary>
    private static string GetColorCode(string colorName)
    {
        if (string.IsNullOrWhiteSpace(colorName))
            throw new ArgumentException("Color name cannot be empty.", nameof(colorName));

        string[] words = colorName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        return words.Length == 1
            ? words[0].Substring(0, Math.Min(3, words[0].Length)).ToUpper()
            : string.Concat(words.Select(w => w[0])).ToUpper();
    }
}