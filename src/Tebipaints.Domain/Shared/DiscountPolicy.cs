using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Domain.Shared;

public sealed class DiscountPolicy : Entity
{
    private DiscountPolicy() { }
    
    private DiscountPolicy(
        Guid id,
        string name,
        DiscountType type,
        decimal percentage,
        string? description,
        DateTime? validFrom,
        DateTime? validTo,
        bool isActive) : base(id)
    {
        Type = type;
        Percentage = percentage;
        Description = description;
        ValidFrom = validFrom;
        ValidTo = validTo;
        Name = name;
        IsActive = isActive;
    }
    
    public string Name { get; private set; }
    public DiscountType Type { get; private set; }
    public decimal Percentage { get; private set; }
    public string? Description { get; private set; }
    public DateTime? ValidFrom { get; private set; }
    public DateTime? ValidTo { get; private set; }
    public bool IsActive { get; private set; }

    public bool IsValid(DateTime currentDate) => 
        (!ValidFrom.HasValue || currentDate >= ValidFrom) &&
        (!ValidTo.HasValue || currentDate <= ValidTo);

    public static Result<DiscountPolicy> Create(
        string name,
        DiscountType type,
        decimal percentage,
        string? description = null,
        DateTime? validFrom = null,
        DateTime? validTo = null)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            return Result.Failure<DiscountPolicy>(
                DiscountErrors.InvalidName());
        }

        if (percentage < 0 || percentage > 100)
        {
            return Result.Failure<DiscountPolicy>(
                DiscountErrors.InvalidPercentage());
        }
        
        if (validFrom.HasValue && validTo.HasValue && validFrom > validTo)
        {
            return Result.Failure<DiscountPolicy>(
                DiscountErrors.InvalidDateRange());
        }
        
        return Result.Success(new DiscountPolicy(
            Guid.NewGuid(),
            name,
            type,
            percentage,
            description,
            validFrom,
            validTo,
            true));
    }
}

public class DiscountErrors
{
    public static Error InvalidPercentage() => new(
        "Discount.InvalidPercentage",
        "The provided discount percentage is invalid.");
    
    public static Error InvalidDateRange() => new(
        "Discount.InvalidDateRange",
        "The provided discount date range is invalid.");
    
    public static Error InvalidName() => new(
        "Discount.InvalidName",
        "The discount name cannot be empty.");
}