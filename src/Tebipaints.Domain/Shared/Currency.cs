namespace Tebipaints.Domain.Shared;

public record Currency()
{
    internal static readonly Currency None = new("", "");
    public static readonly Currency Usd = new("USD", "$");
    public static readonly Currency Eur = new("EUR", "€");
    public static readonly Currency Ghs = new("GHS", "GH₵");

    private Currency(string code, string symbol) : this()
    {
        Code = code;
        Symbol = symbol;
    }

    public string Code { get; init; }
    public string Symbol { get; init; }

    public static Currency FromCode(string code)
    {
        return All.FirstOrDefault(c => c.Code == code) ??
               throw new ApplicationException("The currency code is invalid");
    }

    public static readonly IReadOnlyCollection<Currency> All = new[]
    {
        Usd,
        Eur,
        Ghs
    };
}