namespace Tebipaints.Domain.Shared;

public sealed record Measurement
{
    private Measurement() { }
    
    public Measurement(decimal value, UnitOfMeasure unit)
    {
        Value = value;
        Unit = unit;
    }
    
    public decimal Value { get; }
    public UnitOfMeasure Unit { get; }

    public static Measurement Zero(UnitOfMeasure unit) => new(0, unit);

    public static Measurement FromKilograms(decimal value) => new(value, UnitOfMeasure.Kilogram);
    public static Measurement FromTonnes(decimal value) => new(value, UnitOfMeasure.Tonne);
    public static Measurement FromLitres(decimal value) => new(value, UnitOfMeasure.Litre);
    public static Measurement FromMillilitres(decimal value) => new(value, UnitOfMeasure.Millilitre);
    
    public static Measurement operator +(Measurement left, Measurement right)
    {
        var convertedRight = right.ConvertTo(left.Unit);
        return new Measurement(left.Value + convertedRight.Value, left.Unit);
    }
    
    public static Measurement operator -(Measurement left, Measurement right)
    {
        var convertedRight = right.ConvertTo(left.Unit);
        return new Measurement(left.Value - convertedRight.Value, left.Unit);
    }
    
    public static Measurement operator *(Measurement measurement, decimal multiplier)
        => new(measurement.Value * multiplier, measurement.Unit);
    
    public static Measurement operator /(Measurement measurement, decimal divisor)
        => new(measurement.Value / divisor, measurement.Unit);
    
    public static bool operator >(Measurement left, Measurement right)
    {
        var convertedRight = right.ConvertTo(left.Unit);
        return left.Value > convertedRight.Value;
    }
    
    public static bool operator <(Measurement left, Measurement right)
    {
        var convertedRight = right.ConvertTo(left.Unit);
        return left.Value < convertedRight.Value;
    }
    
    public static bool operator >=(Measurement left, Measurement right)
    {
        var convertedRight = right.ConvertTo(left.Unit);
        return left.Value >= convertedRight.Value;
    }
    
    public static bool operator <=(Measurement left, Measurement right)
    {
        var convertedRight = right.ConvertTo(left.Unit);
        return left.Value <= convertedRight.Value;
    }

    public Measurement ConvertTo(UnitOfMeasure targetUnit)
    {
        if (Unit == targetUnit)
        {
            return this;
        }

        if (!Unit.CanConvertTo(targetUnit))
        {
            throw new InvalidOperationException(
                $"Cannot convert from {Unit.Symbol} to {targetUnit.Symbol}");
        }

        var baseValue = Unit.ToBaseUnit(Value);
        var convertedValue = targetUnit.FromBaseUnit(baseValue);
        
        return new Measurement(convertedValue, targetUnit);
    }
    
    public Measurement WithValue(decimal newValue)
    {
        return new Measurement(newValue, Unit);
    }

    public override string ToString() => $"{Value:N3} {Unit.Symbol}";
}

public sealed record UnitOfMeasure
{
    private UnitOfMeasure() { }
    
    private UnitOfMeasure(
        string name,
        string symbol,
        MeasurementType type,
        decimal conversionFactor)
    {
        Name = name;
        Symbol = symbol;
        Type = type;
        ConversionFactor = conversionFactor;
    }
    
    public string Name { get; }
    public string Symbol { get; }
    public MeasurementType Type { get; }
    public decimal ConversionFactor { get; }

    // Volume units (base: Litre)
    public static readonly UnitOfMeasure Millilitre = new(
        "Millilitre",
        "mL",
        MeasurementType.Volume,
        0.001m);
        
    public static readonly UnitOfMeasure Litre = new(
        "Litre",
        "L",
        MeasurementType.Volume,
        1m);

    // Mass units (base: Kilogram)
    public static readonly UnitOfMeasure Kilogram = new(
        "Kilogram",
        "kg",
        MeasurementType.Mass,
        1m);
        
    public static readonly UnitOfMeasure Tonne = new(
        "Tonne",
        "t",
        MeasurementType.Mass,
        1000m);

    public static readonly IReadOnlyList<UnitOfMeasure> All = new[]
    {
        Millilitre,
        Litre,
        Kilogram,
        Tonne
    };

    public bool CanConvertTo(UnitOfMeasure other) => Type == other.Type;

    public decimal ToBaseUnit(decimal value) => value * ConversionFactor;

    public decimal FromBaseUnit(decimal baseValue) => baseValue / ConversionFactor;
}

public enum MeasurementType
{
    Mass,
    Volume
}