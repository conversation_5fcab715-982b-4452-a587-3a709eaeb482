using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Domain.Shared;

public static class UnitOfMeasureConverter
{
    public static Result<UnitOfMeasure> FromString(string unit)
    {
        var normalizedUnit = unit.ToLowerInvariant().Trim();
        
        var unitOfMeasure = normalizedUnit switch
        {
            "kg" or "kilogram" or "kilograms" => UnitOfMeasure.Kilogram,
            "t" or "ton" or "tonne" or "tonnes" => UnitOfMeasure.Tonne,
            "l" or "liter" or "litre" or "litres" => UnitOfMeasure.Litre,
            "ml" or "milliliter" or "millilitre" => UnitOfMeasure.Millilitre,
            _ => null
        };

        if (unitOfMeasure is null)
        {
            return Result.Failure<UnitOfMeasure>(
                new Error(
                    "UnitOfMeasure.Invalid",
                    $"Unsupported unit of measure: {unit}"));
        }

        return Result.Success(unitOfMeasure);
    }
}