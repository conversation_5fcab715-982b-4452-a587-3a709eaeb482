using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Domain.Shared;

public sealed class NumberSequence : Entity
{
    private NumberSequence(){}
    
    private NumberSequence(
        Guid id, 
        string type, 
        int lastUsedNumber, 
        int year, 
        int month) : base(id)
    {
        Type = type;
        LastUsedNumber = lastUsedNumber;
        Year = year;
        Month = month;
    }
    
    public string Type { get; private set; } // e.g. "Invoice", "PurchaseOrder"
    public long LastUsedNumber { get; private set; }
    public int Year { get; private set; }
    public int Month { get; private set; }

    public static NumberSequence Create(string type, int? startFrom = null)
    {
        var now = DateTime.UtcNow;
        return new NumberSequence(
            Guid.NewGuid(),
            type,
            startFrom ?? 0,  // Allow specifying a starting number
            now.Year,
            now.Month);
    }
    
    public static NumberSequence CreateContinuation(
        string type,
        int lastUsedNumber,
        DateTime referenceDate)
    {
        return new NumberSequence(
            Guid.NewGuid(),
            type,
            lastUsedNumber,
            referenceDate.Year,
            referenceDate.Month);
    }

    public long GetNextNumber()
    {
        var now = DateTime.UtcNow;
        
        // Reset sequence if month/year changed
        if (Year != now.Year || Month != now.Month)
        {
            LastUsedNumber = 0;
            Year = now.Year;
            Month = now.Month;
        }

        return ++LastUsedNumber;
    }
}