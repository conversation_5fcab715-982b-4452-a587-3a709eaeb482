using Tebipaints.Domain.Invoice;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Production;
using Tebipaints.Domain.SalesOrder;

namespace Tebipaints.Domain.Shared;

public interface IDocumentNumberGenerator
{
    Task<InvoiceNumber> GetNextInvoiceNumberAsync(CancellationToken cancellationToken = default);
    Task<PurchaseOrderNumber> GetNextPurchaseOrderNumberAsync(CancellationToken cancellationToken = default);
    Task<OrderNumber> GetNextSalesOrderNumberAsync(CancellationToken cancellationToken = default);
    Task<WorkOrderNumber> GetNextWorkOrderNumberAsync(CancellationToken cancellationToken = default);
    Task<ContractNumber> GetNextContractNumberAsync( CancellationToken cancellationToken = default);
    Task<GoodsReceiptNumber> GetNextGoodsReceiptNumberAsync(CancellationToken cancellationToken = default);
    Task<int> GetNextSkuAsync( CancellationToken cancellationToken = default);
}