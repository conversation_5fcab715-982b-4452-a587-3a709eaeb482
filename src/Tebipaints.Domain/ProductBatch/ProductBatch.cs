using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.ProductBatch.Enums;
using Tebipaints.Domain.ProductBatch.Errors;
using Tebipaints.Domain.ProductBatch.Events;

namespace Tebipaints.Domain.ProductBatch;

public sealed class ProductBatch : Entity
{
    private ProductBatch(){}
    
    private ProductBatch(
        Guid id,
        Guid productId,
        string batchNumber,
        int quantity,
        DateTime manufacturingDate,
        DateTime expirationDate,
        QualityStatus qualityStatus) : base(id)
    {
        ProductId = productId;
        BatchNumber = batchNumber;
        Quantity = quantity;
        ManufacturingDate = manufacturingDate;
        ExpirationDate = expirationDate;
        QualityStatus = qualityStatus;
    }
    
    public Guid ProductId { get; private set; }
    public string BatchNumber { get; private set; }
    public int Quantity { get; private set; }
    public DateTime ManufacturingDate { get; private set; }
    public DateTime ExpirationDate { get; private set; }
    public QualityStatus QualityStatus { get; private set; }
    
    /// <summary>
    /// Creates a new product batch
    /// </summary>
    /// <param name="productId"></param>
    /// <param name="batchNumber"></param>
    /// <param name="quantity"></param>
    /// <param name="manufacturingDate"></param>
    /// <param name="shelfLifeInMonths"></param>
    /// <returns></returns>
    public static Result<ProductBatch> Create(
        Guid productId,
        string batchNumber,
        int quantity,
        DateTime manufacturingDate,
        int shelfLifeInMonths)
    {
        if (productId == Guid.Empty)
        {
            return Result.Failure<ProductBatch>(ProductBatchErrors.InvalidProductId());
        }

        if (quantity <= 0)
        {
            return Result.Failure<ProductBatch>(ProductBatchErrors.InvalidQuantity());
        }

        if (manufacturingDate > DateTime.UtcNow)
        {
            return Result.Failure<ProductBatch>(ProductBatchErrors.FutureManufacturingDate());
        }

        var expirationDate = manufacturingDate.AddMonths(shelfLifeInMonths);
        
        var batch = new ProductBatch(
            Guid.NewGuid(),
            productId,
            batchNumber,
            quantity,
            manufacturingDate,
            expirationDate,
            QualityStatus.Pending);
        
        batch.RaiseDomainEvent(new ProductBatchCreatedDomainEvent(
            batch.Id, 
            batch.ProductId, 
            batch.BatchNumber, 
            batch.Quantity));
        
        return Result.Success(batch);
    }
    
    public Result UpdateQualityStatus(QualityStatus newStatus, string notes)
    {
        QualityStatus = newStatus;
        
        RaiseDomainEvent(new ProductBatchQualityUpdatedDomainEvent(
            Id, 
            ProductId, 
            BatchNumber, 
            newStatus,
            notes));
        
        return Result.Success();
    }
    
    /// <summary>
    /// Checks if the batch has expired.
    /// </summary>
    public bool IsExpired() => DateTime.UtcNow > ExpirationDate;
}