using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Domain.ProductBatch.Errors;

public class ProductBatchErrors
{
    public static Error InvalidProductId() => new(
        "ProductBatch.InvalidProductId",
        "The product id must be a valid GUID.");
    
    public static Error InvalidQuantity() => new(
        "ProductBatch.InvalidQuantity",
        "The quantity must be greater than zero.");
    
    public static Error FutureManufacturingDate() => new(
        "ProductBatch.FutureManufacturingDate",
        "The manufacturing date cannot be in the future.");
    
    public static Error ProductNotFound(Guid productId) => new(
        "ProductBatch.ProductNotFound",
        $"Product with id {productId} not found.");
}