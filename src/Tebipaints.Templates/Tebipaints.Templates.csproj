<Project Sdk="Microsoft.NET.Sdk.Razor">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <EnableRazorRuntimeCompilation>true</EnableRazorRuntimeCompilation>
        <EmbedRazorGenerateSources>true</EmbedRazorGenerateSources>
        <AddRazorSupportForMvc>true</AddRazorSupportForMvc>
    </PropertyGroup>


    <ItemGroup>
        <SupportedPlatform Include="browser"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="8.0.5"/>
        <FrameworkReference Include="Microsoft.AspNetCore.App" />
        <PackageReference Include="Razor.Templating.Core" Version="2.1.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Tebipaints.Application\Tebipaints.Application.csproj" />
    </ItemGroup>

</Project>
