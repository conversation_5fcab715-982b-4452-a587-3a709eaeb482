@model Tebipaints.Application.Procurement.PurchaseOrders.GeneratePurchaseOrderPdf.PurchaseOrderDto

@{
    var subtotal = Model.Items.Sum(item => item.Quantity * item.UnitPrice);
    var discountPercent = Model.DiscountPercent ?? 0;
    var discountAmount = subtotal * discountPercent / 100;
    var freight = Model.Freight ?? 0m;
    var taxPercent = Model.TaxPercent ?? 0;
    var taxAmount = ((subtotal - discountAmount) + freight) * taxPercent / 100;
    var total = subtotal - discountAmount + freight + taxAmount;
    var paymentTermsSummary = (Model.PaymentTerms.Any())
        ? string.Join(", ", Model.PaymentTerms.Select(t => string.IsNullOrWhiteSpace(t.TermType) ? t.TermDescription : $"{t.TermType}: {t.TermDescription}"))
        : "—";
}

<script src="https://cdn.tailwindcss.com"></script>
<div class="min-h-screen bg-white p-8 m-8">
    <!-- Header Section -->
    <div class="flex justify-between items-start mb-8">
        <div class="w-1/3 -mt-16">
            @* <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKUAAAA5CAYAAABXj+viAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAABXQSURBVHhe7V0HXBTX1z279F6WpQo2VKzEhg17B3tviTX2qBF7T+yxxN5NrIm9oEZjR8WeT7EbC6IoRVD60nb3u3dmERBIDDb27x5/4857Mzuzb9555977yiBRE6CDDgUIUs2nDjoUGOhIqUOBg46U7wCVWoVLV+/hWehLqNXplKPzeD4mdKT8F6ihwppfj6F9j8WoWm8iftt9TsfJj4wCQkpNLX/UyuaLZ9wgy42yZr9JaDaKAePjE7BizVEo0zgtxYkzdwCJhE/W4SOhQETfKSlpeB7xCoYGEjg72EEq/fBtRalMx8voWERFxyPyZTzCI18hLDIG4WGxiIlRIC4hEckpKVApJVCpVPQbABNTEyQmp+Lq1WBIVeJ1/EY0hd/Q1sRLnZH5WPjspIyNS0S/oauIlK+RmpoGU0NDNKhfHr271YGbq/1/q3wqCvt/CkUa7j8MxaPgMNqicP9BGB6HPIe+1BC21mawlVlA7mAJua0FHOwsIaNPW5klrC3NYGRoIFwqJSUVL4i43w5ah9hYBdQSNezkFpAgFY4uctSpVhrdO9ZEsSLOwvk6fDh8dlKGPotEy65zsGxBX3hVKYUkItTRY1exdN0R1PTywMxJXaGnx8SUkEHNMJsqUjQV4uIVeBEWjRfhsXj4OAK37gTj3t8RCA6OQNlyrqha2R0exZ1Q0t0Zhd3ksLI0FSyvaH35egz+n6+sJ6QywLn+f1zCoOEboFaphTP8RjbD9wNb4Cn95rMX7sKtkA3q164ofkGHD4bPTko1KVvgpXuYOX8nkSyZZTyKEoFkgmpt23MOMitzdGrvhdj4FERFJSLs5WuEh8cg+lU8lOnpcHaUoZCzDIVdZVi78TRe0TmMrh2rYuGcnrTHZPuvPqBI1P7f/wr/A5eFHHNTA1w8PQt2tpZCOuMcXaz44VFgRnS4q+V1bBKCbofgcXAkIqMSkEBBRlhELALO3sXgbxtDLreEo70l7OVWcHCwIYJYQI+cPzbxW7cHYMykbaRqdDHiYJ+etTFzcjcx8Z+hRkhIBGr7TENaMvmjJK3DBzTAhFHtdb7kJ0CBIWVO8M+SID09Fb2HrETDhuXRs2NdIkV2M8tQUKDUyOcHPH4SJXxL30iCU/6TUILMdn5JuWz1Ycz8yV9ImZob4uCusShdwkVI6/BxUYCbvUgmfX0DLPupD+bO20dRc5yQlxXcpnbtPYeHT15qcoBve9aHO/mS+SMkkTwpBbv8RbPNBK1RzR3uRe01aR0+NrTAFklgaWWKoQOaY/3m05TOLuwJCQqs2XCSCkKBC3HQ2ckKPbvVo6/lt2hq3LofiuAMktM1fRqXo8aRU6F1+DjQCgeJI+OvO9XFrn2BSKOoOwMcE58+dwePH0W9SbfwrYjCheT51EgRh49dQ2pyGrBvaW2E1r416Xo6X/JTQWuetIWlCbxrlcG5C7c1ORC6hbbuPgeVSlRPKysj9O/ViPzO/FMyLV2Js+fv0p74aHybVISZiZGwr8OngdaQUkL/6tctjSPHgijFJFThwaNQisxvCceVUjV6dqkLF0dbIZ1fPH4Shtu3n9Me3dFAgi7tawomPP9QE9HTkZKWJnR/6fDv0CqbVLdGOQReuEPVTP4jOZC/7z4PtVL09exlVuhMBHoflWT83/UnULJzSqQv5GKD0iUL0X7+r3njVgiatpqOavUm4oc5O6BSKzVHdMgLWkVKGytzKBRKJFJw8zo2Af6H/oKECMS+ZJ1aJVGsiAOdlX8C8RDlnycylFiC5o2/grm5qXAsP2BlnLFgP+7ej0BEeBxWrj+NU2dvao7qkBe0ipRMN0cnG4RHRCMg8AbCIxPEfH01urb3gvT9RJKInogbN59BKiglEb16CVJe8Vh+wNQOfZ7ZVcXN5/lzMSjTIW9oFSmZla7OcoQ8i8afRyng0VjCch4uqFWtHB1/v+I8ePQcYRExwuQLBydLeNcoQ7n5Z6WUGD2kb2Pyd5WCz+vqZoW63hU0R3XIC9pFSiKIja0JmcMXOH5aDHCYMz261oVU7z0kTYMrfz0UhinZZ21QuzSMjN4/6u7WsQ4O7xiLjasG4MjuKSjsKtcc0SEvaBkpAQsLExwLCEJiotiPKJOZomlDT9rLnZRsQnnjrp7Q55GIio7VBBucmx23774glZSQ+QaaNPpKyBO/T//UvKly+VZu4G+IkJBPUbmiO5o3rASZjTnnAd0yBNaR0qexnblSjDVO1e8BE0bV4CDnbXm6NvgmeOJ2LX/LBr4ToN38+nw9B6LVp3nISIqRnOOCJ4BcPt+qLBvaW0Mz7JFhP7PZ+QDLlpxEG2/WYAu/Zfg1y1HBXL+Ex4+DseeAxeQksrrefIPbghKpQpKVUZjeLcmoe0owBMycsfcJfuwaPERYZ/jke2bBqNuzdz9NJ5NPmnGNuzcdZ5ONgAoIOrdo45A7HSlErMm9XgTyLyKiUP5qmOIiEDN6u503RH4bWcANu84j5pVS+BhSBSOnrhJzUANv+98MPq7VuQz5mzTaSoluvRcjLMX75NCFsGqhX1QuBD3Cvw7VKp0BIeE497DcFy6+hAPiNxxscmk8qkwNTWFI8+ScrZB2RL2qFPLE/Z25vT79TXffn+kpqXDwIAHbD+vVmkdKWfM34XlK4/TnhpFislx+tDUN7PFs+IlKeHAYatx/hKpKkGtBwwZ2BBjh7bGmfO3sWXXBfy6dLBASn4AV67/jdbtF0JJ6dHDfSgoscV2IvPqn/sLfaBKUs0KNf0QHZ0ENZnkfVtGoIaXh3DtTKix++AFDPPbRAonKnnL5hWwju6TF/jxx8bF44+jQcKiNJ7xXs7DDR4l7VGieCFYW/FsdyA2NoECvCgE3Q3FgUNXkBifglo1S8KnmSe+7tyQGhoPL2haWD6got/Rvd88zP2xN9xcPq/fq13mmyswViHu0vNv2awSDA1yTpRgn3HNplMIvPJYkwN4VSlOhGwlTKzYe+gvNPIu/ca9Y/V7Skoo7EtUMKBt2YrDmD21C6mRFWcKa3acHG2EflG6ATb9fjaHGWczu+fAVZDgCYQ0tzRB36/raY6+DTXSSJm27Q5Eu+4Lce7iHUz0a4+D2ybg51m90L+3DxrU8UQlz2KoSFu9OhXQs1sDLJz+DW5dWoTli/oiMVmBcVP2oHx1P8xbvB8xcUnCdfODY6euUWBnAVdnmSbn80GrSMmP+/Y9HgIEjIyN0K6FF/Hl7SKoKTp/hrUbTxB5RNZJKHKZPKo1mSY97Np3AeEvotDKt4pAxgwEh0QKn5Zmprh2MxTfD/GBh7sb5WSqDy+LYHA/5q07T+nbWQmgxt8PXiAgkMfN6VukXP171kd1r1JC+m0EP41Alz7z8eeJ61i/YhCW/NQPNchNMDIi1adGkKvqcRZtRoZ6aNuiGg5sn4gVC7vDwswQC5ceISUfiSmzdiBJkSKe/454Fh6FxasOY/igJnTr/JH6Q0KrSBlLSnDzNpMBKFrYGkWK5JzjyMOPW7YFIjkhXahDNaleS99KKF3KFSt+OY61m05j2qSusLSwoKNixbMD8zhE7ORmMxb9KgpNGorRdwb4nhEUuWcgMYkrnlc+KsmshuN4wA3MnL8HSoVYqUVdbfFtr3p0RnYlZ3MdEHgHQ8esR+f23li3bCCKujlAX4/Py4WI/wA9apDtW3nj4slZmDi6Bfmeaqxdfwoelb7Hr1tPCis4/w28OG7MxI1oUq88PMsVpZycludTQ2tIyap05+5TpKWoBBPbuKEnqUpOJz8+IQknz2bOJGLFuf8oHC07z0XQjWBsXTsM5T0Ka44yVIh+HYdrQaLvmZSQgi4dGsCcFDMTarx4EY1XUeIIEoN9xnlL96F5+1kYOHINDp+4hkSNQqVLVejYsZowLJoVTPjdBwIxbeZOjB/ZBh1a1SIyvn+gIpXq4bsBvrgWMBNt2lahqF+JcdO2oUHLH7Fp+3Gkv9UFxm6Hgkz/mcDb6Nx7CTxKFMLQ/r65Bm6fA1oU6KixZM0hzJ57ULDKu7cOg3c18guzqYsat+4+g2/HeUhVcD+mWliEtmpJP2ElY2FSJA5ssppGjnhHjP8FW/f+BUOKcmR2pjh/bDosLc00ZzBU2Pz7KYyavEMw3SoycS6FbOA3tBmqVy4FNzd7sGs7bMxG7Nx9GXpG5KPtm4DSJV3pu+K9+DEf+vMylq49gsVz+pJr4KI5lPX3vw/EamTCHTsRhKXrjuPy1UfC+iInuTE6tvaGi4sNBUR6CAkJw+mzfyM04hXGfd8KvbrUE/ILCrTIfKsRdF1UMycHa1T+qjjtZa9QrpZwXj8uEFJc8FW7VikyS4XJ3DsKw37ZCEkV+MfRa3j0MBK2GhLWrl6cCJldJVNT03Ey8J5mTJxyiJS9u3mjW4e6KF7UiQjJFSol9RGVskLZIhQ583oe8XwOgDbvCMD85UexcEYvUibnD0xIBl+LSifRQ5NGFbHvdz/yU79G43pl8TIyEcvWHsf4H3Zi7JTf8PvOS3ClRrVvqx+vo36BIiRDa0jJJunx09fCvldlNxjn0g3EY4RJiZlOPldTPYqyc1tsxrh+6wnmLN6H4YObE0PJ/yK3wNfHi45kGg8m7mJS6NNn7mlyADMTQ4qMyzMFNDkiTI0Nhc8KZZ3JLIuPliPslev+xBZS2tULe6FsaZ4Kx8c+JCHfBnmyVObO7byxdc1g3L66EBtW9sHy+d2JiCNw/NAEbFz1HcqWciswJjsrtIaUsXEJpILiKEyzJpWJaLlUKj1gU1NjTQIwNJCiTNms/mMG1AgJjcSsuTsxfUInlClTVCC9mZUJqWoROi4+FsHkHrmGNb+cJn9RVF9G3ZolSSEdNakMSODmaifs+ZRwpTtwwPQa0+fsECLy9cuHohT5bp86kJASOWXWFvBp7EUmvDZqeJVBIUd+88jHbBTvB60hZWRUvLAu3MhUH1W+KkY5OR8q5xQvTmbaUCponQ2RrJDT2zPR1QiLfI1BI9agQ5tapKTlSc2UwuZezAHOmpnr/P0nIRGYs8gfTeuTKmrE09TCABNGdaAgS1TFrGjSwBP6xnpISknCvoNX0WvQcjjQ/X+hCNtV6JAuuEQoSNAaUr4IfyUMATo7mQsvI8gdpFYudvAo5ShUv4GhPvT1sxfxSXAkBgxbgzY+ldGxLc9Ul5LPmErXVsPLs8ib2Ubch7l641EM7lNPeEECT9IwszDG0jk9yV90ouM5CVaujBumjm2DP/64gaCbwVi9eDAG920KC4v8TxT+EqE1pAx/ESMQpVa1sjDmDuY8wMNtfkObg93IpKRkJCvEvjr27XbvD0SPwcvwTdc66NerKZ0rFp/fssbaWMKdySySjfsk7t8PI7OuwgMKhKTkCowf2QKNG3kSkYVTcoAJ3u+bhvDfOQZTx3UUVLcgm8mCCq0gJXdzhIRGCUN8FSuwj/hPFS0hc1sZU8a1hIKI2GvICoyeugVN2v2IPf5XsHHlYLRvXSPb6wZ5pIa5U6oEv8BAKdr1y6DBcsOoXKVYti+aSh6dW8AA31uEP90f6ngx+le75J/aEU/pRpKDB+9ATv3XMahvaNRqYK75kje4GLFJyooag6Ckkro4V6ISOeSjYwZuPt3KBq2mo77V37OZmq5McSQH2tubkxk5ABFp3qfAlrSnCWIikqCpYURRbbv9j4fNpuW5qZo5VMNbX2rCcOMeb2MVSrll7Xa5PD9WO1srM2JkDzqoiPkp4J2kJKULvp1PNzcHGBinNnl827gIv5zMeUyS/TsVkeT0uFzQ2uUMiYxEcWK2QkK+KFha2OB7wb4aFI6fG5oByklaigUCvILda9y/hKgFaTkoCUlWYkSWaJjHf53oTXmW6lWwsEhr07z/0Vwp0iB7xj5KNAKUrIXWcJNBgcZT8x9P8TEJtKWgMSkZE1OTiQnpwrvVOfZQbmBZ3aLk3yzg/P5LRsZUNB1klNSNSkRyclpiImh3xCTIHzy62eUyuzvF+J5ly+jYmmLE0aavjRozXxKrigm5/sEOjwT27fzHDwLfS1cp0Orqpg4qh0MDLKOEKnhf+gSxkzbhi5ta2Lq+I457jn75z149CQcaxcPpN8ktmuegDFn4V78tv0clszvjbreZbB2wzGEhEZj1uTuwjmMW3efokvvn8klkQqz4rlc29cPhWc5se+V+0ZnL9iDbXsvE/GT0K2DN36c0PmjBHgFFVpivumHUqXkXTFECapcYQOvkU4XKpdT2SFBbHwS/Ib6YtnCPvhl6xk8BSmOZYBNY6euomqVYvgtz1nkZomThbOCvZvo6JJEd+6fHJKGgyM9DFuylak0n5qqua8LOC3uF08OQeN6ldAzWoeuHpqNsp68DIEESyM/keCMKR/Q1wJmIUh3/K6mbfLzWXl8ilpo09N2XP8IC1FgVBKNl+sCgoFm7s0YZw6JSUd6WkqcQZPulKoYH7HYwqnyazyXylj85pK5pH3k9P5GKcp0UFY2MpRg9rA1MTE81dxPvU8pmCpkQI/oNOS1Ydxsn9k4U/IpWBuPgE1Go2DRuWD0SfIasxe0on+DStQkcyiTF19k4E3X6CvZv9iDCZSjlt1g7EJyhw4cojDOhZH3GJClLGUKxZNEA4JytGTd5CZVZg5YK+lMrUBibZ+Glbsdv/KkYMaoLePRpRGXhGUub9ExKThKUYqSlqGBnrwdBQH0ak9kb6tBkZwJAahqFhxr4e5evDgNK88pMXz/HoFE/r4/N4ebKhoR7MTI1hQs+qIEz4LRBKyQ/C3MwM1rZWkNvZwNFeBidHW9g72EDuYA07uSVsZeaQ2dEmM4OtrZnwKae0vb0VHOxt4GRvCxcHO7g628PNRQa53CbPN1ScCLiDgHN3MGNSJ7i+9W6fM+fvIyYuGZt2nKXrWmLPwSuU++7t1tjQGFPGtMHsRf54HsYLzf6b2eUJIVPGdsT8Wd2xYv0xjJy0McfdeWTKyUlGZRXL7CQnf5vKy7On5HILzXMyhY2tKT0renZC2hxye2vhT704ONIzdpTB0UEGO5kNPXdr4WUHBWUGuhat0Xl/sFJ6+0zGoN5N8U2XuprcTKSrUzFw+HohGPH0LIzQZ9HY438B964uISXJVNxppJTX81BKVvEZU7ugQasf8OJ5HOrVKf2flJKXVBw/dQ2VKrpj/eZTOHX2Ho7vmyj8vaAvBV9OSQnc+tycrKGnn/u7gJKTlHgSEobO7apj9JDW+GFiJ7gVkeP8xTuaM0TYWBvAzsY4hwja2hgSeY3ooUqxhoIgW2sTWOUxl9LR3gLWlsb0m7JfhJdR7Np/GU1azhDWDw0f2PCLIiTji1JKpiWrJa8Nz+1PkHDwwMdZ/XiuJaufil+/Qk8o6/m8EIz/CABPIM46RY2jew5UeAIH+4b8cip+vOJ0t+zg9eLc3SNeNysxxfspFCnUeMgfJH8xB/v/x/GFkVIHbcCXZRd00AroSKlDgYOOlDoUMAD/D1lEhPYIzLJjAAAAAElFTkSuQmCC" *@
            @*      class="h-48 object-contain" alt="Logo"/> *@
            <svg xmlns="http://www.w3.org/2000/svg" height="200" width="200" viewBox="0 0 1728 1728" fill="none"><g fill="#475283"><path d="M645.15 579.22c-59.1 3.81-116.29 11.05-171.95 21.73 7.24 8.01 7.62 17.92 2.29 30.12-37.74 65.96-71.3 142.21-100.65 228.76-29.36 86.55-48.42 170.81-57.19 252.78-1.14-.38-2.29-.38-3.43-.38s-1.91 0-2.67-.38h-6.86c-.76-89.22 9.53-176.53 32.03-262.7 3.43-38.89 1.91-78.54-4.96-118.96-6.86-40.79-22.87-75.49-47.66-104.47-11.44.38-25.55-.38-42.32-1.91q-25.74-2.865-45.75-10.29c-13.34-4.96-19.83-12.58-19.83-22.88 1.14-7.62 8.01-12.2 20.21-14.11s27.07 0 44.22 6.1c16.78 5.72 33.17 16.01 48.42 30.88 50.33-2.29 105.23-9.53 164.33-21.35 39.27-7.24 79.68-13.73 121.24-19.06 41.18-5.34 82.73-7.62 124.68-6.48 41.94 1.14 83.12 8.39 123.91 20.97l-3.81 11.44c-56.82-16.77-115.15-23.63-174.25-19.81m-456.76 15.25c1.91 3.81 10.3 8.01 24.78 11.82 14.49 4.19 33.93 6.48 57.95 7.24-19.45-16.78-40.8-24.78-64.44-24.78-14.09 0-20.2 1.9-18.29 5.72m244.01 13.72c-46.51 8.77-90.74 14.49-132.68 17.16 18.3 24.02 31.65 51.47 39.27 81.97 8.01 30.88 11.82 61.77 11.82 92.27 24.78-75.49 51.86-139.17 81.59-191.4"/><path d="M466.72 984.89c-10.3-15.25-15.63-33.93-15.63-56.05 0-22.49 4.95-44.61 15.25-66.34 9.91-21.73 25.16-39.27 45.37-52.62 15.63-9.53 29.36-14.11 40.8-14.11 1.91 0 6.1.76 13.34 1.53 7.24 1.14 14.11 6.1 20.21 14.87 2.29 4.96 3.81 9.91 3.81 14.87 0 7.24-3.05 16.39-9.53 27.83-6.1 11.06-16.01 21.35-29.74 30.88-10.3 7.24-20.21 10.68-29.36 10.68-3.43 0-6.48-.38-9.53-1.14-11.06 30.12-16.4 54.52-16.4 73.2 0 2.67.38 8.01 1.15 15.25.38 7.24 6.1 11.82 16.77 12.96 16.39-1.14 33.17-9.15 51.09-24.4 17.54-15.25 33.93-32.79 49.18-53 14.87-19.83 27.07-36.98 36.22-51.09l9.91 6.86c-9.91 15.63-22.88 33.93-38.51 54.52-16.01 20.59-33.17 38.89-51.85 54.52s-37.36 24.02-56.05 24.78c-20.57-.75-36.21-8.75-46.5-24m80.07-111.72c9.53-7.24 17.92-16.01 24.4-27.07 4.19-7.24 6.48-13.34 6.48-19.06 0-2.67-.76-5.34-1.91-8.01-2.29-2.67-4.57-4.19-6.1-4.19-6.48 0-12.96 3.81-19.83 11.44-12.58 14.49-23.64 33.55-33.93 57.57 1.53.38 3.05.38 4.96.38 7.62 0 16.39-3.81 25.93-11.06"/><path d="M634.09 1038.26c-.76-14.49-.76-28.98-.76-43.08 0-152.51 34.31-300.82 102.94-446.09 6.48-11.44 13.73-17.16 22.11-17.16 8.77 0 15.63 3.43 20.97 10.68 2.67 3.81 4.19 8.01 4.19 12.96 0 3.81-1.14 8.39-3.05 12.96-45.37 90.36-81.21 194.07-108.28 310.35 15.25-36.6 34.31-59.86 57.19-70.53 12.2-5.34 22.11-8.01 29.74-8.01 15.63 0 28.98 7.62 40.41 22.88 9.53 12.58 14.49 30.12 14.49 52.23l-.76 13.73c16.39-4.96 32.79-9.53 49.94-13.73 11.82-3.05 24.02-6.48 36.22-9.91l3.43 11.82c-12.58 3.43-24.78 6.86-36.98 9.91-19.07 4.58-37.37 9.53-54.91 15.25-5.33 24.02-14.87 44.99-28.59 62.15-13.73 17.54-27.45 29.36-41.56 35.84-5.72 2.67-10.68 3.81-15.25 3.81-6.48 0-11.82-2.67-15.63-8.01-1.91-3.81-2.67-8.01-2.67-12.2 0-8.39 4.2-19.06 12.58-31.65 8.39-12.58 18.3-23.64 29.74-32.41 9.15-28.6 13.73-51.85 13.73-69.39 0-4.19-.38-10.29-1.53-18.3-1.14-8.01-6.86-12.96-17.92-14.87-12.96 0-26.69 9.91-40.8 30.12-17.54 27.45-30.12 56.81-37.74 88.07-8.01 31.65-13.73 61.38-17.54 89.6l-.76 6.48c-.38 3.05-.76 5.72-.76 7.62zm99.13-64.43c3.81-9.91 7.24-20.59 9.91-31.65-3.05 3.43-6.86 8.01-11.06 14.11-8.77 12.2-12.96 21.73-12.96 28.59 0 1.91.38 3.43 1.14 4.96.76.76 1.53 1.52 2.29 1.52 2.3.01 5.73-6.09 10.68-17.53"/><path d="M888.78 966.59c-8.77-11.82-12.96-28.98-12.96-50.71 0-24.78 3.43-46.51 10.3-65.2 7.24-19.06 14.87-33.55 22.87-43.08 1.53-1.53 3.05-1.91 5.34-1.91 5.34 0 12.2 2.67 20.21 8.39 4.96 3.43 9.15 7.24 12.58 10.68 2.29 3.05 3.43 4.96 3.43 6.1 0 1.52-1.52 4.19-4.57 8.01-3.05 4.2-7.62 11.06-13.73 20.97-10.67 18.68-18.3 36.98-22.88 54.9-4.19 17.54-6.48 30.88-6.48 39.27 0 4.96 2.29 7.24 6.48 7.24 2.67 0 8.01-1.91 15.63-5.72s18.68-11.82 33.55-24.4c14.49-12.58 28.6-25.55 41.94-39.27l.38-.38c39.27-39.65 85.02-64.05 136.88-73.58 14.49-3.05 28.59-4.57 42.7-4.57 9.15 0 21.35 1.14 36.6 3.05 15.63 2.29 30.5 5.72 44.61 10.68s21.73 8.01 22.11 8.39c29.74 11.82 54.52 24.02 74.35 36.6 16.02 9.53 31.65 17.54 46.52 23.64 10.68 4.19 22.49 6.48 35.46 6.48 4.96 0 10.29-.38 15.63-1.15 40.04-8.77 68.25-32.79 84.26-72.06.77-2.67 2.67-3.81 5.72-3.81l1.91.38c2.67 1.14 4.19 3.43 4.19 5.72l-.76 2.67c-8.77 19.44-19.06 35.08-30.88 47.28s-22.5 21.35-32.03 27.07q-14.295 9.15-17.16 9.15c-13.73 5.72-27.07 8.77-40.41 8.77-27.07 0-63.29-15.25-109.04-45.37-18.3-11.82-37.37-22.5-57.19-32.41-27.83-14.11-56.81-20.97-86.17-20.97-63.67 0-120.1 24.4-169.28 73.58h-.38c-32.03 34.69-61 57.19-87.31 68.25-6.1 2.67-11.44 3.81-16.4 3.81-6.49-.01-11.82-1.92-16.02-6.49m61.77-200.55c-6.1-6.1-8.77-12.96-8.77-21.35 0-8.01 2.67-15.25 8.77-20.97 5.72-5.72 12.58-8.77 20.97-8.77s15.25 3.05 20.97 8.77c6.1 5.72 8.77 12.96 8.77 20.97 0 8.39-2.67 15.25-8.77 21.35-5.72 5.72-12.58 8.39-20.97 8.39s-15.25-2.67-20.97-8.39M610.61 1139.4c0 8.69-2.71 15.34-8.15 19.94-5.43 4.6-13.15 6.9-23.17 6.9h-7.35v28.71h-17.12v-80.73h25.79c9.79 0 17.24 2.11 22.34 6.32 5.11 4.22 7.66 10.51 7.66 18.86m-38.66 12.82h5.63c5.27 0 9.2-1.04 11.82-3.12 2.61-2.08 3.92-5.11 3.92-9.08 0-4.01-1.1-6.98-3.29-8.89-2.19-1.92-5.62-2.87-10.3-2.87h-7.79v23.96zm148.67 42.74-5.85-19.22h-29.43l-5.86 19.22h-18.44l28.5-81.07h20.93l28.6 81.07zm-9.94-33.57c-5.41-17.41-8.46-27.26-9.14-29.55-.69-2.28-1.17-4.09-1.46-5.41q-1.83 7.065-10.44 34.95h21.04zm83.9-47.17h17.12v80.73h-17.12zm154.41 80.74h-21.76l-35.12-61.08h-.5c.7 10.79 1.04 18.48 1.04 23.08v37.99h-15.29v-80.73h21.59l35.07 60.47h.39c-.55-10.49-.83-17.91-.83-22.25v-38.21h15.41zm96.82 0h-17.12v-66.49h-21.92v-14.24h60.96v14.24h-21.92zm127.36-22.42c0 7.29-2.62 13.03-7.87 17.23s-12.54 6.29-21.9 6.29q-12.915 0-22.86-4.86v-15.9c5.45 2.43 10.06 4.14 13.83 5.14 3.77.99 7.23 1.49 10.35 1.49 3.76 0 6.63-.72 8.64-2.15 2.01-1.44 3.01-3.57 3.01-6.4 0-1.58-.44-2.99-1.33-4.22-.88-1.23-2.18-2.42-3.89-3.56s-5.2-2.96-10.47-5.47c-4.93-2.32-8.63-4.54-11.1-6.68s-4.44-4.62-5.91-7.46c-1.47-2.83-2.21-6.15-2.21-9.94 0-7.14 2.42-12.76 7.26-16.84 4.84-4.09 11.53-6.13 20.07-6.13 4.2 0 8.2.49 12.01 1.49 3.81.99 7.79 2.39 11.95 4.2l-5.52 13.31c-4.31-1.77-7.87-3-10.68-3.7s-5.59-1.05-8.31-1.05c-3.24 0-5.72.75-7.46 2.26-1.73 1.51-2.59 3.48-2.59 5.91 0 1.51.35 2.82 1.05 3.95.7 1.12 1.81 2.21 3.34 3.26s5.14 2.94 10.85 5.66c7.55 3.61 12.72 7.23 15.52 10.85 2.82 3.61 4.22 8.05 4.22 13.32m-987.08-17.98 296.89-7.42v14.85zm1058.93-7.42 296.89 7.42-296.89 7.43z"/></g></svg>
        </div>
        <div class="w-2/3">
            <h2 class="text-lg font-bold text-gray-800 mb-4">PURCHASE ORDER # @Model.PurchaseOrderNumber</h2>
            <div class="grid grid-cols-2 gap-4">
                <div class="flex justify-between">
                    <span class="text-gray-600 text-sm">P.O. Date</span>
                    <span class="font-medium text-xs">@Model.IssueDate?.ToString("MMM dd, yyyy")</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600 text-sm">Promise Date</span>
                    <span class="font-medium text-xs">@Model.PromiseDate?.ToString("MMM dd, yyyy")</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Address Section -->
    <div class="grid grid-cols-3 gap-8 mb-8">
        <div class="space-y-2">
            <h3 class="font-bold text-gray-800 text-sm">BILL TO</h3>
            <div class="text-gray-600 text-xs">
                Tebi Paints <br/>
                Agbogba School Junction, GE-192-3315 <br/>
                Greater-Accra - Ghana <br/>
                +233 55 587 9661 <br/>
                <EMAIL>
            </div>
        </div>
        <div class="space-y-2">
            <h3 class="font-bold text-gray-800 text-sm">VENDOR</h3>
            <div class="text-gray-600 text-xs">
                @Model.SupplierName <br/>
                @Model.SupplierEmail <br/>
                @Model.SupplierAddress <br/>
                @Model.SupplierPhoneNumber
            </div>
        </div>
        <div class="space-y-2">
            <h3 class="font-bold text-gray-800 text-sm">SHIP TO</h3>
            <div class="text-gray-600 text-xs">
                Tebi Paints <br/>
                Agbogba School Junction, GE-192-3315 <br/>
                Greater-Accra - Ghana <br/>
                +233 55 587 9661
            </div>
        </div>
    </div>

    <!-- Items Table -->
    <div class="mb-8">
        <table class="w-full border-collapse">
            <thead>
                <tr class="bg-gray-100 text-sm">
                    <th class="border border-gray-300 p-3 text-left">ITEM DESCRIPTION</th>
                    <th class="border border-gray-300 p-3 text-center">QTY</th>
                    <th class="border border-gray-300 p-3 text-center text-sm">UNIT</th>
                    <th class="border border-gray-300 p-3 text-right">UNIT PRICE</th>
                    <th class="border border-gray-300 p-3 text-right">TOTAL COST (@Model.OrderCurrency)</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.Items)
                {
                    <tr class="text-xs">
                        <td class="border border-gray-300 p-2">
                            @item.Name
                            @if (!string.IsNullOrEmpty(item.Description))
                            {
                                <div class="text-sm text-gray-500">SKU: @item.Description</div>
                            }
                        </td>
                        <td class="border border-gray-300 p-2 text-center">@item.Quantity</td>
                        <td class="border border-gray-300 p-2 text-center">@item.Unit</td>
                        <td class="border border-gray-300 p-2 text-right">@(item.UnitPrice.ToString("F2"))</td>
                        <td class="border border-gray-300 p-2 text-right">@((item.Quantity * item.UnitPrice).ToString("F2"))</td>
                    </tr>
                }
            </tbody>
        </table>
    </div>

    <!-- Summary Section -->
    <div class="flex justify-end mb-8">
        <div class="w-1/3">
            <table class="w-full text-xs">
                <tr>
                    <td class="pt-1 text-sm">Subtotal</td>
                    <td class="pt-1 text-right">@(subtotal.ToString("F2"))</td>
                </tr>
                <tr>
                    <td class="pt-1 text-sm">Discount (@((Model.DiscountPercent ?? 0).ToString("F2")))%</td>
                    <td class="pt-1 text-right">@(discountAmount.ToString("F2"))</td>
                </tr>
                <tr>
                    <td class="pt-1 text-sm">Freight</td>
                    <td class="pt-1 text-right">@(freight.ToString("F2"))</td>
                </tr>
                <tr>
                    <td class="pt-1 text-sm">Tax (@(Model.TaxPercent ?? 0))%</td>
                    <td class="pt-1 text-right">@(taxAmount.ToString("F2"))</td>
                </tr>
                <tr class="border-t border-gray-300">
                    <td class="pt-1 font-bold">Total Cost</td>
                    <td class="pt-1 text-right font-bold">@(total.ToString("F2")) @Model.OrderCurrency</td>
                </tr>
            </table>
        </div>
    </div>
    
    @if (Model.PaymentTerms?.Any() == true)
    {
        <div class="mb-8 text-xs border border-gray-300 p-4 bg-gray-100">
            <h3 class="font-bold text-gray-800 mb-2">Terms</h3>
            <ul class="list-disc pl-5 text-gray-700">
                @foreach (var term in Model.PaymentTerms)
                {
                    <li>
                        @if (!string.IsNullOrWhiteSpace(term.TermType))
                        {
                            <span class="font-medium">@term.TermType</span>
                        }
                        @if (!string.IsNullOrWhiteSpace(term.TermDescription))
                        {
                            <span>: @term.TermDescription</span>
                        }
                        @if (!string.IsNullOrWhiteSpace(term.TermExpiration))
                        {
                            @if (DateTime.TryParse(term.TermExpiration, out var expirationDate))
                            {
                                <span> (Due: @expirationDate.ToString("MMM dd, yyyy"))</span>
                            }
                            else
                            {
                                <span> (Due: @term.TermExpiration)</span>
                            }
                        }
                    </li>
                }
            </ul>
        </div>
    }

    <!-- Footer -->
    <div class="fixed bottom-0 text-center text-gray-200 text-xs flex justify-between">
        <span class="px-1">Purchase Order # @Model.PurchaseOrderNumber</span>
        <span class="px-1">Page 1 of 1</span>
        <span class="px-1">Powered by Tebi Paints™</span>
    </div>
</div>