@model Tebipaints.Application.Procurement.GoodsReceipts.GenerateGoodsReceiptPdf.GoodsReceiptDto

@{
    // Ensure we have a valid model
    var model = Model ?? new Tebipaints.Application.Procurement.GoodsReceipts.GenerateGoodsReceiptPdf.GoodsReceiptDto();
    var items = model.Items ?? new List<Tebipaints.Application.Procurement.GoodsReceipts.GenerateGoodsReceiptPdf.GoodsReceiptItemDto>();
}

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>Goods Receipt - @(model.ReceiptNumber ?? "Unknown")</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom styles for PDF generation */
        @@page {
            size: A4 landscape;
            margin: 15mm;
        }
        
        @@media print {
            body {
                margin: 0;
                padding: 0;
            }
        }
        
        .company-logo {
            width: 160px;
            height: auto;
        }
        
        .quantity-mismatch {
            background-color: #fef3c7 !important;
            color: #92400e !important;
        }
        
        /* Ensure proper table borders for PDF */
        .items-table td, .items-table th {
            border: 1px solid #d1d5db !important;
        }
    </style>
</head>
<body class="bg-white font-sans text-sm leading-relaxed text-gray-800">
    <div class="max-w-full mx-auto p-5">
        <!-- Header -->
        <div class="flex justify-between items-start mb-6 pb-3 border-b-2 border-gray-700">
            <div class="flex items-center space-x-4">
                <div class="company-logo">
                    <svg xmlns="http://www.w3.org/2000/svg" height="80" width="180" viewBox="0 0 1728 1728" fill="none">
                        <g fill="#475283">
                            <path d="M645.15 579.22c-59.1 3.81-116.29 11.05-171.95 21.73 7.24 8.01 7.62 17.92 2.29 30.12-37.74 65.96-71.3 142.21-100.65 228.76-29.36 86.55-48.42 170.81-57.19 252.78-1.14-.38-2.29-.38-3.43-.38s-1.91 0-2.67-.38h-6.86c-.76-89.22 9.53-176.53 32.03-262.7 3.43-38.89 1.91-78.54-4.96-118.96-6.86-40.79-22.87-75.49-47.66-104.47-11.44.38-25.55-.38-42.32-1.91q-25.74-2.865-45.75-10.29c-13.34-4.96-19.83-12.58-19.83-22.88 1.14-7.62 8.01-12.2 20.21-14.11s27.07 0 44.22 6.1c16.78 5.72 33.17 16.01 48.42 30.88 50.33-2.29 105.23-9.53 164.33-21.35 39.27-7.24 79.68-13.73 121.24-19.06 41.18-5.34 82.73-7.62 124.68-6.48 41.94 1.14 83.12 8.39 123.91 20.97l-3.81 11.44c-56.82-16.77-115.15-23.63-174.25-19.81m-456.76 15.25c1.91 3.81 10.3 8.01 24.78 11.82 14.49 4.19 33.93 6.48 57.95 7.24-19.45-16.78-40.8-24.78-64.44-24.78-14.09 0-20.2 1.9-18.29 5.72m244.01 13.72c-46.51 8.77-90.74 14.49-132.68 17.16 18.3 24.02 31.65 51.47 39.27 81.97 8.01 30.88 11.82 61.77 11.82 92.27 24.78-75.49 51.86-139.17 81.59-191.4"/>
                            <path d="M466.72 984.89c-10.3-15.25-15.63-33.93-15.63-56.05 0-22.49 4.95-44.61 15.25-66.34 9.91-21.73 25.16-39.27 45.37-52.62 15.63-9.53 29.36-14.11 40.8-14.11 1.91 0 6.1.76 13.34 1.53 7.24 1.14 14.11 6.1 20.21 14.87 2.29 4.96 3.81 9.91 3.81 14.87 0 7.24-3.05 16.39-9.53 27.83-6.1 11.06-16.01 21.35-29.74 30.88-10.3 7.24-20.21 10.68-29.36 10.68-3.43 0-6.48-.38-9.53-1.14-11.06 30.12-16.4 54.52-16.4 73.2 0 2.67.38 8.01 1.15 15.25.38 7.24 6.1 11.82 16.77 12.96 16.39-1.14 33.17-9.15 51.09-24.4 17.54-15.25 33.93-32.79 49.18-53 14.87-19.83 27.07-36.98 36.22-51.09l9.91 6.86c-9.91 15.63-22.88 33.93-38.51 54.52-16.01 20.59-33.17 38.89-51.85 54.52s-37.36 24.02-56.05 24.78c-20.57-.75-36.21-8.75-46.5-24m80.07-111.72c9.53-7.24 17.92-16.01 24.4-27.07 4.19-7.24 6.48-13.34 6.48-19.06 0-2.67-.76-5.34-1.91-8.01-2.29-2.67-4.57-4.19-6.1-4.19-6.48 0-12.96 3.81-19.83 11.44-12.58 14.49-23.64 33.55-33.93 57.57 1.53.38 3.05.38 4.96.38 7.62 0 16.39-3.81 25.93-11.06"/>
                            <path d="M634.09 1038.26c-.76-14.49-.76-28.98-.76-43.08 0-152.51 34.31-300.82 102.94-446.09 6.48-11.44 13.73-17.16 22.11-17.16 8.77 0 15.63 3.43 20.97 10.68 2.67 3.81 4.19 8.01 4.19 12.96 0 3.81-1.14 8.39-3.05 12.96-45.37 90.36-81.21 194.07-108.28 310.35 15.25-36.6 34.31-59.86 57.19-70.53 12.2-5.34 22.11-8.01 29.74-8.01 15.63 0 28.98 7.62 40.41 22.88 9.53 12.58 14.49 30.12 14.49 52.23l-.76 13.73c16.39-4.96 32.79-9.53 49.94-13.73 11.82-3.05 24.02-6.48 36.22-9.91l3.43 11.82c-12.58 3.43-24.78 6.86-36.98 9.91-19.07 4.58-37.37 9.53-54.91 15.25-5.33 24.02-14.87 44.99-28.59 62.15-13.73 17.54-27.45 29.36-41.56 35.84-5.72 2.67-10.68 3.81-15.25 3.81-6.48 0-11.82-2.67-15.63-8.01-1.91-3.81-2.67-8.01-2.67-12.2 0-8.39 4.2-19.06 12.58-31.65 8.39-12.58 18.3-23.64 29.74-32.41 9.15-28.6 13.73-51.85 13.73-69.39 0-4.19-.38-10.29-1.53-18.3-1.14-8.01-6.86-12.96-17.92-14.87-12.96 0-26.69 9.91-40.8 30.12-17.54 27.45-30.12 56.81-37.74 88.07-8.01 31.65-13.73 61.38-17.54 89.6l-.76 6.48c-.38 3.05-.76 5.72-.76 7.62zm99.13-64.43c3.81-9.91 7.24-20.59 9.91-31.65-3.05 3.43-6.86 8.01-11.06 14.11-8.77 12.2-12.96 21.73-12.96 28.59 0 1.91.38 3.43 1.14 4.96.76.76 1.53 1.52 2.29 1.52 2.3.01 5.73-6.09 10.68-17.53"/>
                            <path d="M888.78 966.59c-8.77-11.82-12.96-28.98-12.96-50.71 0-24.78 3.43-46.51 10.3-65.2 7.24-19.06 14.87-33.55 22.87-43.08 1.53-1.53 3.05-1.91 5.34-1.91 5.34 0 12.2 2.67 20.21 8.39 4.96 3.43 9.15 7.24 12.58 10.68 2.29 3.05 3.43 4.96 3.43 6.1 0 1.52-1.52 4.19-4.57 8.01-3.05 4.2-7.62 11.06-13.73 20.97-10.67 18.68-18.3 36.98-22.88 54.9-4.19 17.54-6.48 30.88-6.48 39.27 0 4.96 2.29 7.24 6.48 7.24 2.67 0 8.01-1.91 15.63-5.72s18.68-11.82 33.55-24.4c14.49-12.58 28.6-25.55 41.94-39.27l.38-.38c39.27-39.65 85.02-64.05 136.88-73.58 14.49-3.05 28.59-4.57 42.7-4.57 9.15 0 21.35 1.14 36.6 3.05 15.63 2.29 30.5 5.72 44.61 10.68s21.73 8.01 22.11 8.39c29.74 11.82 54.52 24.02 74.35 36.6 16.02 9.53 31.65 17.54 46.52 23.64 10.68 4.19 22.49 6.48 35.46 6.48 4.96 0 10.29-.38 15.63-1.15 40.04-8.77 68.25-32.79 84.26-72.06.77-2.67 2.67-3.81 5.72-3.81l1.91.38c2.67 1.14 4.19 3.43 4.19 5.72l-.76 2.67c-8.77 19.44-19.06 35.08-30.88 47.28s-22.5 21.35-32.03 27.07q-14.295 9.15-17.16 9.15c-13.73 5.72-27.07 8.77-40.41 8.77-27.07 0-63.29-15.25-109.04-45.37-18.3-11.82-37.37-22.5-57.19-32.41-27.83-14.11-56.81-20.97-86.17-20.97-63.67 0-120.1 24.4-169.28 73.58h-.38c-32.03 34.69-61 57.19-87.31 68.25-6.1 2.67-11.44 3.81-16.4 3.81-6.49-.01-11.82-1.92-16.02-6.49m61.77-200.55c-6.1-6.1-8.77-12.96-8.77-21.35 0-8.01 2.67-15.25 8.77-20.97 5.72-5.72 12.58-8.77 20.97-8.77s15.25 3.05 20.97 8.77c6.1 5.72 8.77 12.96 8.77 20.97 0 8.39-2.67 15.25-8.77 21.35-5.72 5.72-12.58 8.39-20.97 8.39s-15.25-2.67-20.97-8.39M610.61 1139.4c0 8.69-2.71 15.34-8.15 19.94-5.43 4.6-13.15 6.9-23.17 6.9h-7.35v28.71h-17.12v-80.73h25.79c9.79 0 17.24 2.11 22.34 6.32 5.11 4.22 7.66 10.51 7.66 18.86m-38.66 12.82h5.63c5.27 0 9.2-1.04 11.82-3.12 2.61-2.08 3.92-5.11 3.92-9.08 0-4.01-1.1-6.98-3.29-8.89-2.19-1.92-5.62-2.87-10.3-2.87h-7.79v23.96zm148.67 42.74-5.85-19.22h-29.43l-5.86 19.22h-18.44l28.5-81.07h20.93l28.6 81.07zm-9.94-33.57c-5.41-17.41-8.46-27.26-9.14-29.55-.69-2.28-1.17-4.09-1.46-5.41q-1.83 7.065-10.44 34.95h21.04zm83.9-47.17h17.12v80.73h-17.12zm154.41 80.74h-21.76l-35.12-61.08h-.5c.7 10.79 1.04 18.48 1.04 23.08v37.99h-15.29v-80.73h21.59l35.07 60.47h.39c-.55-10.49-.83-17.91-.83-22.25v-38.21h15.41zm96.82 0h-17.12v-66.49h-21.92v-14.24h60.96v14.24h-21.92zm127.36-22.42c0 7.29-2.62 13.03-7.87 17.23s-12.54 6.29-21.9 6.29q-12.915 0-22.86-4.86v-15.9c5.45 2.43 10.06 4.14 13.83 5.14 3.77.99 7.23 1.49 10.35 1.49 3.76 0 6.63-.72 8.64-2.15 2.01-1.44 3.01-3.57 3.01-6.4 0-1.58-.44-2.99-1.33-4.22-.88-1.23-2.18-2.42-3.89-3.56s-5.2-2.96-10.47-5.47c-4.93-2.32-8.63-4.54-11.1-6.68s-4.44-4.62-5.91-7.46c-1.47-2.83-2.21-6.15-2.21-9.94 0-7.14 2.42-12.76 7.26-16.84 4.84-4.09 11.53-6.13 20.07-6.13 4.2 0 8.2.49 12.01 1.49 3.81.99 7.79 2.39 11.95 4.2l-5.52 13.31c-4.31-1.77-7.87-3-10.68-3.7s-5.59-1.05-8.31-1.05c-3.24 0-5.72.75-7.46 2.26-1.73 1.51-2.59 3.48-2.59 5.91 0 1.51.35 2.82 1.05 3.95.7 1.12 1.81 2.21 3.34 3.26s5.14 2.94 10.85 5.66c7.55 3.61 12.72 7.23 15.52 10.85 2.82 3.61 4.22 8.05 4.22 13.32m-987.08-17.98 296.89-7.42v14.85zm1058.93-7.42 296.89 7.42-296.89 7.43z"/>
                        </g>
                    </svg>
                </div>
                <div>
                    <div class="text-2xl font-bold text-gray-700">Tebipaints</div>
                    <div class="text-xs text-gray-600 italic">Quality Meets Style</div>
                </div>
            </div>
            
            <div class="text-center">
                <h1 class="text-xl font-bold border-2 border-gray-700 px-4 py-2 inline-block">
                    Goods Receipt
                </h1>
            </div>
            
            <div class="text-right">
                <span class="inline-block bg-gray-100 text-xs font-bold px-3 py-1 rounded-full uppercase">
                    Received
                </span>
            </div>
        </div>

        <!-- Receipt and Supplier Information -->
        <div class="grid grid-cols-2 gap-8 mb-4">
            <div class="border border-gray-300 p-4 rounded bg-gray-50">
                <div class="text-base font-bold mb-3 pb-2 border-b border-gray-300">
                    Supplier Information
                </div>
                <div class="space-y-2">
                    <div class="flex">
                        <span class="font-semibold w-32 text-gray-700">Name:</span>
                        <span class="flex-1">@(model.SupplierName ?? "N/A")</span>
                    </div>
                    <div class="flex">
                        <span class="font-semibold w-32 text-gray-700">Address:</span>
                        <span class="flex-1">@(model.SupplierAddress ?? "N/A")</span>
                    </div>
                    <div class="flex">
                        <span class="font-semibold w-32 text-gray-700">Email:</span>
                        <span class="flex-1">@(model.SupplierEmail ?? "N/A")</span>
                    </div>
                    <div class="flex">
                        <span class="font-semibold w-32 text-gray-700">Phone:</span>
                        <span class="flex-1">@(model.SupplierPhone ?? "N/A")</span>
                    </div>
                </div>
            </div>
            
            <div class="border border-gray-300 p-4 rounded bg-gray-50">
                <div class="text-base font-bold mb-3 pb-2 border-b border-gray-300">
                    Receipt Details
                </div>
                <div class="space-y-2">
                    <div class="flex">
                        <span class="font-semibold w-36 text-gray-700">Receipt Number:</span>
                        <span class="flex-1 font-bold">@(model.ReceiptNumber ?? "N/A")</span>
                    </div>
                    <div class="flex">
                        <span class="font-semibold w-36 text-gray-700">Purchase Order:</span>
                        <span class="flex-1">@(model.PurchaseOrderNumber ?? "N/A")</span>
                    </div>
                    <div class="flex">
                        <span class="font-semibold w-36 text-gray-700">Delivery Note:</span>
                        <span class="flex-1">@(model.DeliveryNoteNumber ?? "N/A")</span>
                    </div>
                    <div class="flex">
                        <span class="font-semibold w-36 text-gray-700">Received Date:</span>
                        <span class="flex-1">@(model.ReceivedDate != default(DateTime) ? model.ReceivedDate.ToString("dd/MM/yyyy HH:mm") : "N/A")</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Items Section -->
        <div class="mt-3">
            <div class="text-base font-bold mb-3">Received Items</div>
            
            @if (items.Any())
            {
                <table class="items-table w-full border-collapse mt-2">
                    <thead>
                        <tr class="bg-gray-100 text-sm">
                            <th class="p-3 text-left text-xs font-bold border border-gray-300 w-10">#</th>
                            <th class="p-3 text-left text-xs font-bold border border-gray-300">Item Name</th>
                            <th class="p-3 text-right text-xs font-bold border border-gray-300 w-20">Ordered Qty</th>
                            <th class="p-3 text-right text-xs font-bold border border-gray-300 w-20">Received Qty</th>
                            <th class="p-3 text-center text-xs font-bold border border-gray-300 w-16">Unit</th>
                            <th class="p-3 text-right text-xs font-bold border border-gray-300 w-20">Unit Price</th>
                            <th class="p-3 text-center text-xs font-bold border border-gray-300 w-12">Currency</th>
                            <th class="p-3 text-left text-xs font-bold border border-gray-300 w-64">Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        @for (int i = 0; i < items.Count; i++)
                        {
                            var item = items[i];
                            var isQuantityMismatch = item?.ItemOrderedQuantityValue.HasValue == true && 
                                                   item?.ItemReceivedQuantityValue.HasValue == true && 
                                                   item.ItemOrderedQuantityValue != item.ItemReceivedQuantityValue;
                            
                            <tr class="@(isQuantityMismatch ? "quantity-mismatch" : "") @(i % 2 == 0 ? "bg-gray-50" : "bg-white") text-xs">
                                <td class="p-2 border border-gray-300 text-xs">@(i + 1)</td>
                                <td class="p-2 border border-gray-300 text-xs font-medium">@(item?.ItemName ?? "N/A")</td>
                                <td class="p-2 border border-gray-300 text-xs text-right">
                                    @(item?.ItemOrderedQuantityValue?.ToString() ?? "-")
                                </td>
                                <td class="p-2 border border-gray-300 text-xs text-right font-bold">
                                    @(item?.ItemReceivedQuantityValue?.ToString() ?? "-")
                                </td>
                                <td class="p-2 border border-gray-300 text-xs text-center">@(item?.ItemReceivedQuantityUnit ?? "-")</td>
                                <td class="p-2 border border-gray-300 text-xs text-right">
                                    @if (item?.ItemUnitPrice.HasValue == true)
                                    {
                                        @item.ItemUnitPrice.Value.ToString("N2")
                                    }
                                    else
                                    {
                                        <text>-</text>
                                    }
                                </td>
                                <td class="p-2 border border-gray-300 text-xs text-center">@(item?.ItemUnitPriceCurrency ?? "-")</td>
                                <td class="p-2 border border-gray-300 text-xs">@(item?.ItemNotes ?? "")</td>
                            </tr>
                        }
                    </tbody>
                </table>
                
                <div class="mt-3 text-xs text-gray-600 flex justify-between items-center">
                    <div>
                        <strong>Total Items:</strong> @items.Count items received
                    </div>
                    @if (items.Any(i => i?.ItemOrderedQuantityValue.HasValue == true && i?.ItemReceivedQuantityValue.HasValue == true && i.ItemOrderedQuantityValue != i.ItemReceivedQuantityValue))
                    {
                        <div class="text-yellow-700 flex items-center">
                            <span class="mr-1">⚠️</span>
                            <span>Some items have quantity discrepancies (highlighted in yellow)</span>
                        </div>
                    }
                    <div class="text-right">
                        @{
                            var grandTotal = items
                                .Where(i => i?.ItemReceivedQuantityValue.HasValue == true && i?.ItemUnitPrice.HasValue == true)
                                .Sum(i => i.ItemReceivedQuantityValue.Value * i.ItemUnitPrice.Value);
                        }
                        <strong>Grand Total:</strong> @grandTotal.ToString("N2") @(items.FirstOrDefault()?.ItemUnitPriceCurrency ?? "")
                    </div>
                </div>
            }
            else
            {
                <div class="text-center py-10 text-gray-500 italic border border-gray-200 rounded bg-gray-50">
                    No items recorded for this goods receipt.
                </div>
            }
        </div>

        <!-- Footer with Signatures -->
        <div class="flex justify-between items-end mt-6">
            <div class="text-center w-48">
                <div class="border-t border-gray-800 mt-12 pt-1 text-xs font-bold">
                    Received By
                </div>
            </div>
            
            <div class="text-center w-48">
                <div class="border-t border-gray-800 mt-12 pt-1 text-xs font-bold">
                    Authorized By
                </div>
            </div>
            
            <div class="text-right text-xs text-gray-600">
                Generated on: @DateTime.Now.ToString("dd/MM/yyyy HH:mm")<br/>
                Document ID: @(model.ReceiptNumber ?? "Unknown")
            </div>
        </div>
    </div>
</body>
</html>