using Tebipaints.Domain.Invoice;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Production;
using Tebipaints.Domain.SalesOrder;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Infrastructure.Generators;

internal sealed class DocumentNumberGenerator : IDocumentNumberGenerator
{
    private readonly INumberSequenceRepository _numberSequenceRepository;

    public DocumentNumberGenerator(INumberSequenceRepository numberSequenceRepository)
    {
        _numberSequenceRepository = numberSequenceRepository;
    }

    public async Task<InvoiceNumber> GetNextInvoiceNumberAsync(CancellationToken cancellationToken = default)
    {
        var sequence = await _numberSequenceRepository.GetByTypeAsync("Invoice", cancellationToken);
        var nextNumber = sequence.GetNextNumber();
        await _numberSequenceRepository.UpdateAsync(sequence, cancellationToken);
        
        return InvoiceNumber.Create((int)nextNumber);
    }

    public async Task<PurchaseOrderNumber> GetNextPurchaseOrderNumberAsync(CancellationToken cancellationToken = default)
    {
        var sequence = await _numberSequenceRepository.GetByTypeAsync("PurchaseOrder", cancellationToken);
        var nextNumber = sequence.GetNextNumber();
        await _numberSequenceRepository.UpdateAsync(sequence, cancellationToken);
        
        return PurchaseOrderNumber.Create((int)nextNumber);
    }

    public async Task<OrderNumber> GetNextSalesOrderNumberAsync(CancellationToken cancellationToken = default)
    {
        var sequence = await _numberSequenceRepository.GetByTypeAsync("SalesOrder", cancellationToken);
        var nextNumber = sequence.GetNextNumber();
        await _numberSequenceRepository.UpdateAsync(sequence, cancellationToken);
        
        return OrderNumber.Create((int)nextNumber);
    }

    public async Task<WorkOrderNumber> GetNextWorkOrderNumberAsync(CancellationToken cancellationToken = default)
    {
        var sequence = await _numberSequenceRepository.GetByTypeAsync("SalesOrder", cancellationToken);
        var nextNumber = sequence.GetNextNumber();
        await _numberSequenceRepository.UpdateAsync(sequence, cancellationToken);
        
        return WorkOrderNumber.Create((int)nextNumber);
    }

    public async Task<ContractNumber> GetNextContractNumberAsync(CancellationToken cancellationToken = default)
    {
        var sequence = await _numberSequenceRepository.GetByTypeAsync("Contract", cancellationToken);
        var nextNumber = sequence.GetNextNumber();
        await _numberSequenceRepository.UpdateAsync(sequence, cancellationToken);

        return ContractNumber.Create((int)nextNumber);
    }

    public async Task<GoodsReceiptNumber> GetNextGoodsReceiptNumberAsync(CancellationToken cancellationToken = default)
    {
        var sequence = await _numberSequenceRepository.GetByTypeAsync("GoodsReceipt", cancellationToken);
        var nextNumber = sequence.GetNextNumber();
        await _numberSequenceRepository.UpdateAsync(sequence, cancellationToken);

        return GoodsReceiptNumber.Create((int)nextNumber);
    }

    public async Task<int> GetNextSkuAsync(CancellationToken cancellationToken = default)
    {
        var sequence = await _numberSequenceRepository.GetByTypeAsync("Sku", cancellationToken);
        var nextNumber = sequence.GetNextNumber();
        await _numberSequenceRepository.UpdateAsync(sequence, cancellationToken);

        return (int)nextNumber;
    }
}