using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PuppeteerSharp;
using PuppeteerSharp.Media;
using Razor.Templating.Core;
using Tebipaints.Application.Abstractions.Reporting;
using Tebipaints.Application.Procurement.GoodsReceipts.GenerateGoodsReceiptPdf;
using Tebipaints.Application.Procurement.PurchaseOrders.GeneratePurchaseOrderPdf;

namespace Tebipaints.Infrastructure.Generators;

public class PdfGenerator : IPdfGenerator
{
    private readonly PuppeteerSettings _puppeteerSettings;
    private readonly ILogger<PdfGenerator> _logger;

    public PdfGenerator(IOptions<PuppeteerSettings> options, ILogger<PdfGenerator> logger)
    {
        _logger = logger;
        _puppeteerSettings = options.Value;
    }

    public async Task<byte[]> GeneratePurchaseOrderPdf(PurchaseOrderDto purchaseOrder)
    {
        try
        {
            // Render razor html
            var html = await RazorTemplateEngine.RenderAsync(
                "/Views/PurchaseOrders/PurchaseOrderTemplate.cshtml", purchaseOrder);
            
            var browser = await Puppeteer.ConnectAsync(new ConnectOptions
            {
                BrowserWSEndpoint = _puppeteerSettings.WebSocketEndpoint
            });

            using var page = await browser.NewPageAsync();
            await page.SetContentAsync(html);
            var pdf = await page.PdfDataAsync(new PdfOptions
            {
                Format = PaperFormat.A4,
                PrintBackground = true,
            });

            return pdf;
        }
        catch (Exception ex)
        {
            // Log the full exception details
            _logger.LogError("PDF Generation failed: {Exception}", ex);
            throw;
        }
    }

    public async Task<byte[]> GenerateGoodsReceiptPdf(GoodsReceiptDto goodsReceipt)
    {
        try
        {
            _logger.LogInformation("Starting PDF generation for goods receipt {ReceiptNumber}", goodsReceipt?.ReceiptNumber ?? "Unknown");
            
            if (goodsReceipt == null)
            {
                throw new ArgumentNullException(nameof(goodsReceipt), "GoodsReceiptDto cannot be null");
            }
            
            var html = await RazorTemplateEngine.RenderAsync(
                "/Views/PurchaseOrders/GoodsReceiptTemplate.cshtml", goodsReceipt);
                
            _logger.LogInformation("Razor template rendered successfully, HTML length: {HtmlLength}", html?.Length ?? 0);
            
            var browser = await Puppeteer.ConnectAsync(new ConnectOptions
            {
                BrowserWSEndpoint = _puppeteerSettings.WebSocketEndpoint
            });

            using var page = await browser.NewPageAsync();
            await page.SetContentAsync(html);
            var pdf = await page.PdfDataAsync(new PdfOptions
            {
                Format = PaperFormat.A4,
                PrintBackground = true
            });
            
            _logger.LogInformation("PDF generated successfully, size: {PdfSize} bytes", pdf?.Length ?? 0);

            return pdf;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PDF Generation failed for goods receipt {ReceiptNumber}: {Message}", 
                goodsReceipt?.ReceiptNumber ?? "Unknown", ex.Message);
            throw;
        }
    }
}