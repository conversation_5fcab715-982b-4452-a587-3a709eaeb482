using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Formulation.ListFormulations;
using Tebipaints.Application.Formulation.Services;

namespace Tebipaints.Infrastructure.Formulation;

public class FormulationEfficiencyService : IFormulationEfficiencyService
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;

    public FormulationEfficiencyService(ISqlConnectionFactory sqlConnectionFactory)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
    }

    public async Task<FormulationEfficiencyMetricsResponse?> CalculateBasicEfficiencyMetricsAsync(
        Guid formulationId,
        CancellationToken cancellationToken = default)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();

        // Get production data for completed batches of this formulation
        const string productionDataQuery = """
            SELECT
                -- Batch data (return TimeSpan as string, we'll parse in C#)
                b.estimated_duration as EstimatedDuration,
                b.actual_duration as ActualDuration,
                b.target_quantity_value as TargetQuantity,
                b.actual_yield as ActualYield,
                b.completion_date as CompletionDate,

                -- Production losses (waste)
                COALESCE(SUM(pl.quantity), 0) as TotalLoss,

                -- Formulation estimated cost data
                f.total_cost_amount as EstimatedTotalCost,
                f.cost_per_unit_amount as EstimatedCostPerUnit

            FROM work_order_batches b
            INNER JOIN work_orders wo ON wo.id = b.work_order_id
            INNER JOIN formulations f ON f.id = wo.formulation_id
            LEFT JOIN batch_production_losses pl ON pl.batch_id = b.id
            WHERE wo.formulation_id = @FormulationId
              AND b.status = 'Completed'
              AND b.actual_yield IS NOT NULL
              AND b.completion_date IS NOT NULL
              AND b.actual_duration IS NOT NULL
            GROUP BY b.id, b.estimated_duration, b.actual_duration, b.target_quantity_value,
                     b.actual_yield, b.completion_date, f.total_cost_amount, f.cost_per_unit_amount
            ORDER BY b.completion_date DESC
            """;

        var productionData = await connection.QueryAsync<ProductionDataDto>(
            productionDataQuery,
            new { FormulationId = formulationId });

        if (!productionData.Any())
        {
            return null; // No production data available
        }

        // Calculate basic efficiency metrics
        var batches = productionData.ToList();
        var totalBatches = batches.Count;
        var lastProductionDate = batches.First().CompletionDate;

        // Time Efficiency: Average of (Estimated Time / Actual Time) * 100
        var timeEfficiencies = batches
            .Where(b => b.ActualDuration.HasValue && b.EstimatedDuration.TotalMilliseconds > 0)
            .Select(b => b.EstimatedDuration.TotalMilliseconds / b.ActualDuration!.Value.TotalMilliseconds * 100)
            .ToList();

        var avgTimeEfficiency = timeEfficiencies.Count > 0 ? (decimal)timeEfficiencies.Average() : (decimal?)null;

        // Yield Efficiency: Average of (Actual Yield / Target Quantity) * 100
        var yieldEfficiencies = batches
            .Where(b => b.ActualYield.HasValue && b.TargetQuantity > 0)
            .Select(b => (double)b.ActualYield!.Value / (double)b.TargetQuantity * 100)
            .ToList();

        var avgYieldEfficiency = yieldEfficiencies.Count > 0 ? (decimal)yieldEfficiencies.Average() : (decimal?)null;

        // Average Actual Yield
        var avgActualYield = batches
            .Where(b => b.ActualYield.HasValue)
            .Select(b => (decimal)b.ActualYield!.Value)
            .DefaultIfEmpty(0)
            .Average();

        // Waste Percentage: Average of (Total Loss / Target Quantity) * 100
        var wastePercentages = batches
            .Where(b => b.TargetQuantity > 0)
            .Select(b => (double)b.TotalLoss / (double)b.TargetQuantity * 100)
            .ToList();

        var avgWastePercentage = wastePercentages.Count > 0 ? (decimal)wastePercentages.Average() : 0m;

        // Cost Efficiency: If we have estimated cost data, calculate variance
        decimal? costVariancePercentage = null;
        decimal? costEfficiencyScore = null;

        var batchesWithCost = batches.Where(b => b.EstimatedCostPerUnit.HasValue).ToList();
        if (batchesWithCost.Count > 0)
        {
            // For simplicity, assume actual cost per unit equals estimated (since we don't track actual costs yet)
            // This can be enhanced later when actual cost tracking is implemented
            costVariancePercentage = 0m; // Placeholder - no variance since we're using estimated
            costEfficiencyScore = 100m; // Perfect score since we're using estimated costs
        }

        // Production Efficiency Score: Average of time and yield efficiency
        decimal? productionEfficiencyScore = null;
        if (avgTimeEfficiency.HasValue && avgYieldEfficiency.HasValue)
        {
            productionEfficiencyScore = (avgTimeEfficiency.Value + avgYieldEfficiency.Value) / 2;
        }
        else if (avgTimeEfficiency.HasValue)
        {
            productionEfficiencyScore = avgTimeEfficiency.Value;
        }
        else if (avgYieldEfficiency.HasValue)
        {
            productionEfficiencyScore = avgYieldEfficiency.Value;
        }

        // Performance Grade: Based on production efficiency score
        string? performanceGrade = productionEfficiencyScore switch
        {
            >= 90 => "A",
            >= 80 => "B",
            >= 70 => "C",
            >= 60 => "D",
            < 60 => "F",
            _ => null
        };

        return new FormulationEfficiencyMetricsResponse
        {
            CostVariancePercentage = costVariancePercentage,
            CostEfficiencyScore = costEfficiencyScore,
            TimeEfficiencyPercentage = avgTimeEfficiency,
            YieldEfficiencyPercentage = avgYieldEfficiency,
            ProductionEfficiencyScore = productionEfficiencyScore,
            AverageYield = avgActualYield,
            WastePercentage = avgWastePercentage,
            TotalBatchesProduced = totalBatches,
            LastProductionDate = lastProductionDate,
            PerformanceGrade = performanceGrade
        };
    }

    public async Task<Dictionary<Guid, FormulationEfficiencyMetricsResponse?>> CalculateBatchEfficiencyMetricsAsync(
        IEnumerable<Guid> formulationIds,
        CancellationToken cancellationToken = default)
    {
        var results = new Dictionary<Guid, FormulationEfficiencyMetricsResponse?>();

        // For small-medium facilities, we can calculate these sequentially
        // For larger scale, this could be optimized with a single query
        foreach (var formulationId in formulationIds)
        {
            var metrics = await CalculateBasicEfficiencyMetricsAsync(formulationId, cancellationToken);
            results[formulationId] = metrics;
        }

        return results;
    }

    private class ProductionDataDto
    {
        public TimeSpan EstimatedDuration { get; set; }
        public TimeSpan? ActualDuration { get; set; }
        public decimal TargetQuantity { get; set; }
        public double? ActualYield { get; set; }
        public DateTime CompletionDate { get; set; }
        public double TotalLoss { get; set; }
        public decimal? EstimatedTotalCost { get; set; }
        public decimal? EstimatedCostPerUnit { get; set; }
    }
}
