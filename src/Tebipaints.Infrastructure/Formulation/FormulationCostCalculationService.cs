using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation;
using Tebipaints.Domain.Formulation.Errors;
using Tebipaints.Domain.Formulation.Services;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Infrastructure.Formulation;

public class FormulationCostCalculationService : IFormulationCostCalculationService
{
    private readonly ISqlConnectionFactory _sqlConnectionFactory;
    private readonly CostCalculationSettings _settings;

    public FormulationCostCalculationService(
        ISqlConnectionFactory sqlConnectionFactory,
        CostCalculationSettings settings)
    {
        _sqlConnectionFactory = sqlConnectionFactory;
        _settings = settings;
    }

    public async Task<Result<FormulationCost>> CalculateCostAsync(
        IReadOnlyList<Ingredient> ingredients,
        Measurement productionQuantity,
        TimeSpan estimatedProductionTime,
        Currency currency,
        CancellationToken cancellationToken = default)
    {
        using var connection = _sqlConnectionFactory.CreateConnection();

        // Get current material costs from active contracts
        const string materialCostSql = """
            SELECT 
                cm.material_id as MaterialId,
                AVG(cm.unit_price_amount) as AverageUnitPrice,
                cm.unit_price_currency as Currency,
                m.name as MaterialName
            FROM contract_materials cm
            JOIN contracts c ON c.id = cm.contract_id
            JOIN materials m ON m.id = cm.material_id
            WHERE c.status = 'Active' 
              AND cm.material_id = ANY(@MaterialIds)
              AND c.end_date > @CurrentDate
            GROUP BY cm.material_id, cm.unit_price_currency, m.name
            """;

        var materialIds = ingredients.Select(i => i.MaterialId).ToArray();
        var materialCosts = await connection.QueryAsync<MaterialCostInfo>(
            materialCostSql,
            new { MaterialIds = materialIds, CurrentDate = DateTime.UtcNow });

        // Calculate total material cost
        decimal totalMaterialCost = 0;
        var missingMaterials = new List<string>();

        foreach (var ingredient in ingredients)
        {
            var materialCost = materialCosts.FirstOrDefault(mc => mc.MaterialId == ingredient.MaterialId);
            if (materialCost != null)
            {
                // Convert ingredient quantity to decimal for cost calculation
                var ingredientCostAmount = (decimal)ingredient.Quantity.Value * materialCost.AverageUnitPrice;
                totalMaterialCost += ingredientCostAmount;
            }
            else
            {
                missingMaterials.Add(ingredient.MaterialId.ToString());
            }
        }

        // If we have missing material costs, we can't calculate accurately
        if (missingMaterials.Any())
        {
            return Result.Failure<FormulationCost>(
                FormulationErrors.InvalidCost($"Missing cost data for materials: {string.Join(", ", missingMaterials)}"));
        }

        // Calculate labor cost
        var totalLaborCost = (decimal)estimatedProductionTime.TotalHours * _settings.LaborCostPerHour;

        // Calculate overhead cost (percentage of material + labor)
        var totalOverheadCost = (totalMaterialCost + totalLaborCost) * _settings.OverheadPercentage;

        // Create the FormulationCost result
        var materialCostMoney = new Money(totalMaterialCost, currency);
        var laborCostMoney = new Money(totalLaborCost, currency);
        var overheadCostMoney = new Money(totalOverheadCost, currency);

        var calculationBasis = $"Production quantity: {productionQuantity.Value} {productionQuantity.Unit}, " +
                              $"Materials: {ingredients.Count}, " +
                              $"Production time: {estimatedProductionTime.TotalHours:F1} hours, " +
                              $"Calculated at: {DateTime.UtcNow:yyyy-MM-dd HH:mm}";

        return FormulationCost.Create(
            materialCostMoney,
            laborCostMoney,
            overheadCostMoney,
            (decimal)productionQuantity.Value,
            calculationBasis);
    }

    public async Task<Result<FormulationCost>> RecalculateCostAsync(
        Domain.Formulation.Formulation formulation,
        Currency currency,
        CancellationToken cancellationToken = default)
    {
        // Use the minimum production quantity from the latest version
        var latestVersion = formulation.Versions.OrderByDescending(v => v.TimeStamp).First();
        var productionQuantity = latestVersion.Snapshot.MinimumProductionQuantity;

        return await CalculateCostAsync(
            formulation.Ingredients,
            productionQuantity,
            formulation.EstimatedProductionTime,
            currency,
            cancellationToken);
    }

    public Result ValidateCostThresholds(FormulationCost cost, string productType)
    {
        var thresholds = _settings.GetThresholdsForProductType(productType);
        
        if (cost.CostPerUnit.Amount > thresholds.MaxCostPerUnit)
        {
            return Result.Failure(FormulationErrors.InvalidCost(
                $"Cost per unit ({cost.CostPerUnit.Amount:C}) exceeds maximum threshold ({thresholds.MaxCostPerUnit:C}) for {productType}"));
        }

        if (cost.MaterialCost.Amount / cost.TotalCost.Amount > thresholds.MaxMaterialCostPercentage)
        {
            return Result.Failure(FormulationErrors.InvalidCost(
                $"Material cost percentage exceeds maximum threshold for {productType}"));
        }

        return Result.Success();
    }

    private class MaterialCostInfo
    {
        public Guid MaterialId { get; set; }
        public decimal AverageUnitPrice { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string MaterialName { get; set; } = string.Empty;
    }
}

public class CostCalculationSettings
{
    public decimal LaborCostPerHour { get; set; } = 15.00m; // GHS per hour
    public decimal OverheadPercentage { get; set; } = 0.20m; // 20%
    
    private readonly Dictionary<string, CostThresholds> _thresholds = new()
    {
        { "Paint", new CostThresholds { MaxCostPerUnit = 50.00m, MaxMaterialCostPercentage = 0.70m } },
        { "Primer", new CostThresholds { MaxCostPerUnit = 40.00m, MaxMaterialCostPercentage = 0.75m } },
        { "Varnish", new CostThresholds { MaxCostPerUnit = 60.00m, MaxMaterialCostPercentage = 0.65m } }
    };

    public CostThresholds GetThresholdsForProductType(string productType)
    {
        return _thresholds.GetValueOrDefault(productType, 
            new CostThresholds { MaxCostPerUnit = 100.00m, MaxMaterialCostPercentage = 0.80m });
    }
}

public class CostThresholds
{
    public decimal MaxCostPerUnit { get; set; }
    public decimal MaxMaterialCostPercentage { get; set; }
}
