<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\Tebipaints.Templates\Tebipaints.Templates.csproj" />
      <ProjectReference Include="..\Tebipaints.Application\Tebipaints.Application.csproj" />
      <ProjectReference Include="..\Tebipaints.Domain\Tebipaints.Domain.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="EFCore.NamingConventions" Version="9.0.0" />
      <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0" />
      <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.0" />
      <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
      <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.2" />
      <PackageReference Include="PuppeteerSharp" Version="20.1.3" />
      <PackageReference Include="Quartz.Extensions.Hosting" Version="3.14.0" />
    </ItemGroup>

</Project>
