using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tebipaints.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class updateFormulationConfigurationTableNames : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_change_log_entry_change_log_change_log_id",
                table: "change_log_entry");

            migrationBuilder.DropTable(
                name: "ChangeLog");

            migrationBuilder.RenameTable(
                name: "Versions",
                newName: "versions");

            migrationBuilder.RenameTable(
                name: "Ingredients",
                newName: "ingredients");

            migrationBuilder.RenameTable(
                name: "Formulations",
                newName: "formulations");

            migrationBuilder.CreateTable(
                name: "change_logs",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    version_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_change_logs", x => x.id);
                    table.ForeignKey(
                        name: "fk_change_logs_versions_version_id",
                        column: x => x.version_id,
                        principalTable: "versions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "ix_change_logs_version_id",
                table: "change_logs",
                column: "version_id",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "fk_change_log_entry_change_logs_change_log_id",
                table: "change_log_entry",
                column: "change_log_id",
                principalTable: "change_logs",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_change_log_entry_change_logs_change_log_id",
                table: "change_log_entry");

            migrationBuilder.DropTable(
                name: "change_logs");

            migrationBuilder.RenameTable(
                name: "versions",
                newName: "Versions");

            migrationBuilder.RenameTable(
                name: "ingredients",
                newName: "Ingredients");

            migrationBuilder.RenameTable(
                name: "formulations",
                newName: "Formulations");

            migrationBuilder.CreateTable(
                name: "ChangeLog",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    version_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_change_log", x => x.id);
                    table.ForeignKey(
                        name: "fk_change_log_versions_version_id",
                        column: x => x.version_id,
                        principalTable: "Versions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "ix_change_log_version_id",
                table: "ChangeLog",
                column: "version_id",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "fk_change_log_entry_change_log_change_log_id",
                table: "change_log_entry",
                column: "change_log_id",
                principalTable: "ChangeLog",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
