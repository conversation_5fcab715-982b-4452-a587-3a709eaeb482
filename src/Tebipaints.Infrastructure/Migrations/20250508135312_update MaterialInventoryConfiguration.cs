using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tebipaints.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class updateMaterialInventoryConfiguration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_material_inventory_reservations_material_inventory_inventory",
                table: "material_inventory_reservations");

            migrationBuilder.DropForeignKey(
                name: "fk_material_inventory_inventory_double_id",
                table: "MaterialInventory");

            migrationBuilder.DropTable(
                name: "MaterialInventoryTransactions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_MaterialInventory",
                table: "MaterialInventory");

            migrationBuilder.RenameTable(
                name: "MaterialInventory",
                newName: "material_inventories");

            migrationBuilder.RenameIndex(
                name: "ix_material_inventory_material_id",
                table: "material_inventories",
                newName: "ix_material_inventories_material_id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_material_inventories",
                table: "material_inventories",
                column: "id");

            migrationBuilder.CreateTable(
                name: "material_inventory_transactions",
                columns: table => new
                {
                    transaction_id = table.Column<Guid>(type: "uuid", nullable: false),
                    inventory_double_id = table.Column<Guid>(type: "uuid", nullable: false),
                    type = table.Column<string>(type: "text", nullable: false),
                    quantity = table.Column<double>(type: "double precision", nullable: false),
                    reference = table.Column<string>(type: "text", nullable: false),
                    timestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_material_inventory_transactions", x => new { x.transaction_id, x.inventory_double_id });
                    table.ForeignKey(
                        name: "fk_material_inventory_transactions_inventory_double_inventory_",
                        column: x => x.inventory_double_id,
                        principalTable: "inventory_double",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "ix_material_inventory_transactions_inventory_double_id",
                table: "material_inventory_transactions",
                column: "inventory_double_id");

            migrationBuilder.AddForeignKey(
                name: "fk_material_inventories_inventory_double_id",
                table: "material_inventories",
                column: "id",
                principalTable: "inventory_double",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_material_inventory_reservations_material_inventories_invent",
                table: "material_inventory_reservations",
                column: "inventory_id",
                principalTable: "material_inventories",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_material_inventories_inventory_double_id",
                table: "material_inventories");

            migrationBuilder.DropForeignKey(
                name: "fk_material_inventory_reservations_material_inventories_invent",
                table: "material_inventory_reservations");

            migrationBuilder.DropTable(
                name: "material_inventory_transactions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_material_inventories",
                table: "material_inventories");

            migrationBuilder.RenameTable(
                name: "material_inventories",
                newName: "MaterialInventory");

            migrationBuilder.RenameIndex(
                name: "ix_material_inventories_material_id",
                table: "MaterialInventory",
                newName: "ix_material_inventory_material_id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_MaterialInventory",
                table: "MaterialInventory",
                column: "id");

            migrationBuilder.CreateTable(
                name: "MaterialInventoryTransactions",
                columns: table => new
                {
                    transaction_id = table.Column<Guid>(type: "uuid", nullable: false),
                    inventory_double_id = table.Column<Guid>(type: "uuid", nullable: false),
                    quantity = table.Column<double>(type: "double precision", nullable: false),
                    reference = table.Column<string>(type: "text", nullable: false),
                    timestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    type = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_material_inventory_transactions", x => new { x.transaction_id, x.inventory_double_id });
                    table.ForeignKey(
                        name: "fk_material_inventory_transactions_inventory_double_inventory_do",
                        column: x => x.inventory_double_id,
                        principalTable: "inventory_double",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "ix_material_inventory_transactions_inventory_double_id",
                table: "MaterialInventoryTransactions",
                column: "inventory_double_id");

            migrationBuilder.AddForeignKey(
                name: "fk_material_inventory_reservations_material_inventory_inventory",
                table: "material_inventory_reservations",
                column: "inventory_id",
                principalTable: "MaterialInventory",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "fk_material_inventory_inventory_double_id",
                table: "MaterialInventory",
                column: "id",
                principalTable: "inventory_double",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
