using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tebipaints.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class updateProductInventoryConfiguration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "VariantId",
                table: "product_variant_stock_levels",
                newName: "variant_packaging_variant_id");

            migrationBuilder.RenameColumn(
                name: "PackagingType",
                table: "product_variant_stock_levels",
                newName: "variant_packaging_type");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "variant_packaging_variant_id",
                table: "product_variant_stock_levels",
                newName: "VariantId");

            migrationBuilder.RenameColumn(
                name: "variant_packaging_type",
                table: "product_variant_stock_levels",
                newName: "PackagingType");
        }
    }
}
