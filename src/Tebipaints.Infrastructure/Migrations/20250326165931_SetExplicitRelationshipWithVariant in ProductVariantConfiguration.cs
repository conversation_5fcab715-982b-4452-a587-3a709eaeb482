using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tebipaints.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class SetExplicitRelationshipWithVariantinProductVariantConfiguration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_product_variants_products_product_id1",
                table: "product_variants");

            migrationBuilder.DropIndex(
                name: "ix_product_variants_product_id1",
                table: "product_variants");

            migrationBuilder.DropColumn(
                name: "product_id1",
                table: "product_variants");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "product_id1",
                table: "product_variants",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "ix_product_variants_product_id1",
                table: "product_variants",
                column: "product_id1");

            migrationBuilder.AddForeignKey(
                name: "fk_product_variants_products_product_id1",
                table: "product_variants",
                column: "product_id1",
                principalTable: "products",
                principalColumn: "id");
        }
    }
}
