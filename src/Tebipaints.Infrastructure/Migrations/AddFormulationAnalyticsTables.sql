-- Migration: Add Formulation Analytics and Metrics Tables
-- Date: 2025-01-08
-- Description: Creates tables for storing formulation performance metrics and analytics

-- 1. Formulation Metrics Table (Main metrics storage)
CREATE TABLE formulation_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    formulation_id UUID NOT NULL REFERENCES formulations(id),
    production_batch_id UUID NULL, -- NULL for estimated metrics
    calculated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    -- Cost Efficiency Metrics
    estimated_total_cost_amount DECIMAL(18,2) NULL,
    estimated_total_cost_currency VARCHAR(3) NULL,
    actual_total_cost_amount DECIMAL(18,2) NULL,
    actual_total_cost_currency VARCHAR(3) NULL,
    cost_per_unit DECIMAL(18,4) NULL,
    actual_cost_per_unit DECIMAL(18,4) NULL,
    
    -- Production Efficiency Metrics
    estimated_production_time INTERVAL NULL,
    actual_production_time INTERVAL NULL,
    expected_yield DECIMAL(10,4) NULL,
    actual_yield DECIMAL(10,4) NULL,
    setup_time_hours DECIMAL(8,2) NULL,
    cleanup_time_hours DECIMAL(8,2) NULL,
    downtime_hours DECIMAL(8,2) NULL,
    time_efficiency DECIMAL(5,2) NULL,
    yield_efficiency DECIMAL(5,2) NULL,
    
    -- Quality Metrics
    first_pass_yield DECIMAL(5,2) NULL,
    rework_rate DECIMAL(5,2) NULL,
    defect_rate DECIMAL(5,2) NULL,
    customer_complaint_rate DECIMAL(5,2) NULL,
    quality_score DECIMAL(5,2) NULL,
    
    -- Resource Utilization Metrics
    material_utilization_rate DECIMAL(5,2) NULL,
    material_waste_rate DECIMAL(5,2) NULL,
    labor_utilization_rate DECIMAL(5,2) NULL,
    equipment_utilization_rate DECIMAL(5,2) NULL,
    resource_utilization_score DECIMAL(5,2) NULL,
    
    -- Overall Performance Score
    overall_performance_score DECIMAL(5,2) NULL,
    performance_grade VARCHAR(2) NULL,
    
    -- Metadata
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 2. Cost Variance Analysis Table
CREATE TABLE cost_variance_analysis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    formulation_metrics_id UUID NOT NULL REFERENCES formulation_metrics(id),
    
    -- Overall Variance
    variance_amount DECIMAL(18,2) NOT NULL,
    variance_percentage DECIMAL(8,4) NOT NULL,
    variance_currency VARCHAR(3) NOT NULL,
    
    -- Component Variances
    material_variance_amount DECIMAL(18,2) NULL,
    material_variance_percentage DECIMAL(8,4) NULL,
    labor_variance_amount DECIMAL(18,2) NULL,
    labor_variance_percentage DECIMAL(8,4) NULL,
    overhead_variance_amount DECIMAL(18,2) NULL,
    overhead_variance_percentage DECIMAL(8,4) NULL,
    
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 3. Cost Drivers Table
CREATE TABLE cost_drivers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    formulation_metrics_id UUID NOT NULL REFERENCES formulation_metrics(id),
    
    driver_name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL, -- Material, Labor, Overhead, Process
    impact_percentage DECIMAL(5,2) NOT NULL,
    impact_amount DECIMAL(18,2) NOT NULL,
    impact_currency VARCHAR(3) NOT NULL,
    
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 4. Quality Issues Table
CREATE TABLE quality_issues (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    formulation_metrics_id UUID NOT NULL REFERENCES formulation_metrics(id),
    production_batch_id UUID NULL,
    
    issue_type VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    impact_percentage DECIMAL(5,2) NOT NULL,
    root_cause TEXT NULL,
    resolution TEXT NULL,
    severity VARCHAR(20) NOT NULL, -- Low, Medium, High, Critical
    
    detected_at TIMESTAMP NOT NULL DEFAULT NOW(),
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 5. Material Waste Analysis Table
CREATE TABLE material_waste_analysis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    formulation_metrics_id UUID NOT NULL REFERENCES formulation_metrics(id),
    material_id UUID NOT NULL,
    material_name VARCHAR(200) NOT NULL,
    
    planned_quantity DECIMAL(12,4) NOT NULL,
    actual_quantity DECIMAL(12,4) NOT NULL,
    waste_quantity DECIMAL(12,4) NOT NULL,
    waste_percentage DECIMAL(5,2) NOT NULL,
    
    planned_cost_amount DECIMAL(18,2) NOT NULL,
    actual_cost_amount DECIMAL(18,2) NOT NULL,
    waste_cost_amount DECIMAL(18,2) NOT NULL,
    cost_currency VARCHAR(3) NOT NULL,
    
    waste_reason VARCHAR(500) NULL,
    waste_category VARCHAR(100) NULL, -- Spillage, Contamination, Overuse, etc.
    
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 6. Production Issues Table
CREATE TABLE production_issues (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    formulation_metrics_id UUID NOT NULL REFERENCES formulation_metrics(id),
    production_batch_id UUID NULL,
    
    issue_type VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    downtime_impact INTERVAL NOT NULL,
    cost_impact_amount DECIMAL(18,2) NOT NULL,
    cost_impact_currency VARCHAR(3) NOT NULL,
    
    root_cause TEXT NULL,
    resolution TEXT NULL,
    preventable BOOLEAN NOT NULL DEFAULT TRUE,
    
    occurred_at TIMESTAMP NOT NULL DEFAULT NOW(),
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 7. Optimization Opportunities Table
CREATE TABLE optimization_opportunities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    formulation_id UUID NOT NULL REFERENCES formulations(id),
    
    opportunity_type VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL, -- Material, Process, Labor, Quality
    description TEXT NOT NULL,
    
    potential_savings_amount DECIMAL(18,2) NOT NULL,
    potential_savings_currency VARCHAR(3) NOT NULL,
    
    implementation_difficulty DECIMAL(3,1) NOT NULL, -- 1-10 scale
    estimated_implementation_time INTERVAL NOT NULL,
    risk_level DECIMAL(3,1) NOT NULL, -- 1-10 scale
    
    status VARCHAR(50) NOT NULL DEFAULT 'Identified', -- Identified, InProgress, Implemented, Rejected
    priority VARCHAR(20) NOT NULL, -- Low, Medium, High, Critical
    
    identified_at TIMESTAMP NOT NULL DEFAULT NOW(),
    implemented_at TIMESTAMP NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 8. Benchmarks Table
CREATE TABLE formulation_benchmarks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_type VARCHAR(100) NOT NULL,
    cost_range_min DECIMAL(18,2) NOT NULL,
    cost_range_max DECIMAL(18,2) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    
    -- Industry Benchmarks
    industry_avg_cost_per_unit DECIMAL(18,4) NULL,
    industry_avg_production_efficiency DECIMAL(5,2) NULL,
    industry_avg_yield_efficiency DECIMAL(5,2) NULL,
    industry_avg_quality_score DECIMAL(5,2) NULL,
    
    -- Company Benchmarks
    company_avg_cost_per_unit DECIMAL(18,4) NULL,
    company_avg_production_efficiency DECIMAL(5,2) NULL,
    company_avg_yield_efficiency DECIMAL(5,2) NULL,
    company_avg_quality_score DECIMAL(5,2) NULL,
    
    -- Top Performer Benchmarks
    top_performer_avg_cost_per_unit DECIMAL(18,4) NULL,
    top_performer_avg_production_efficiency DECIMAL(5,2) NULL,
    top_performer_avg_yield_efficiency DECIMAL(5,2) NULL,
    top_performer_avg_quality_score DECIMAL(5,2) NULL,
    
    benchmark_date DATE NOT NULL,
    data_source VARCHAR(100) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create Indexes for Performance
CREATE INDEX idx_formulation_metrics_formulation_id ON formulation_metrics(formulation_id);
CREATE INDEX idx_formulation_metrics_batch_id ON formulation_metrics(production_batch_id) WHERE production_batch_id IS NOT NULL;
CREATE INDEX idx_formulation_metrics_calculated_at ON formulation_metrics(calculated_at);
CREATE INDEX idx_formulation_metrics_performance_score ON formulation_metrics(overall_performance_score) WHERE overall_performance_score IS NOT NULL;

CREATE INDEX idx_cost_variance_metrics_id ON cost_variance_analysis(formulation_metrics_id);
CREATE INDEX idx_cost_drivers_metrics_id ON cost_drivers(formulation_metrics_id);
CREATE INDEX idx_quality_issues_metrics_id ON quality_issues(formulation_metrics_id);
CREATE INDEX idx_quality_issues_severity ON quality_issues(severity);

CREATE INDEX idx_material_waste_metrics_id ON material_waste_analysis(formulation_metrics_id);
CREATE INDEX idx_material_waste_material_id ON material_waste_analysis(material_id);
CREATE INDEX idx_production_issues_metrics_id ON production_issues(formulation_metrics_id);

CREATE INDEX idx_optimization_opportunities_formulation_id ON optimization_opportunities(formulation_id);
CREATE INDEX idx_optimization_opportunities_status ON optimization_opportunities(status);
CREATE INDEX idx_optimization_opportunities_priority ON optimization_opportunities(priority);

CREATE INDEX idx_benchmarks_product_type ON formulation_benchmarks(product_type);
CREATE INDEX idx_benchmarks_cost_range ON formulation_benchmarks(cost_range_min, cost_range_max);

-- Create Views for Common Queries

-- 1. Formulation Performance Summary View
CREATE VIEW formulation_performance_summary AS
SELECT 
    f.id as formulation_id,
    f.name as formulation_name,
    f.status as formulation_status,
    fm.overall_performance_score,
    fm.performance_grade,
    fm.cost_per_unit,
    fm.actual_cost_per_unit,
    fm.time_efficiency,
    fm.yield_efficiency,
    fm.quality_score,
    fm.resource_utilization_score,
    fm.calculated_at as last_calculated
FROM formulations f
LEFT JOIN LATERAL (
    SELECT * FROM formulation_metrics 
    WHERE formulation_id = f.id 
    AND production_batch_id IS NOT NULL
    ORDER BY calculated_at DESC 
    LIMIT 1
) fm ON true;

-- 2. Cost Trend Analysis View
CREATE VIEW cost_trend_analysis AS
SELECT 
    fm.formulation_id,
    DATE_TRUNC('month', fm.calculated_at) as month,
    AVG(fm.actual_total_cost_amount) as avg_total_cost,
    AVG(fm.actual_cost_per_unit) as avg_cost_per_unit,
    AVG(fm.material_utilization_rate) as avg_material_utilization,
    COUNT(*) as production_count
FROM formulation_metrics fm
WHERE fm.production_batch_id IS NOT NULL
GROUP BY fm.formulation_id, DATE_TRUNC('month', fm.calculated_at);

-- 3. Quality Issues Summary View
CREATE VIEW quality_issues_summary AS
SELECT 
    qi.formulation_metrics_id,
    fm.formulation_id,
    COUNT(*) as total_issues,
    COUNT(*) FILTER (WHERE qi.severity = 'Critical') as critical_issues,
    COUNT(*) FILTER (WHERE qi.severity = 'High') as high_issues,
    AVG(qi.impact_percentage) as avg_impact_percentage,
    COUNT(*) FILTER (WHERE qi.resolved_at IS NOT NULL) as resolved_issues
FROM quality_issues qi
JOIN formulation_metrics fm ON fm.id = qi.formulation_metrics_id
GROUP BY qi.formulation_metrics_id, fm.formulation_id;

-- Add Comments for Documentation
COMMENT ON TABLE formulation_metrics IS 'Stores comprehensive performance metrics for formulations';
COMMENT ON TABLE cost_variance_analysis IS 'Tracks cost variances between estimated and actual costs';
COMMENT ON TABLE cost_drivers IS 'Identifies and quantifies factors driving formulation costs';
COMMENT ON TABLE quality_issues IS 'Records quality issues encountered during production';
COMMENT ON TABLE material_waste_analysis IS 'Analyzes material waste patterns and costs';
COMMENT ON TABLE production_issues IS 'Tracks production issues and their impact';
COMMENT ON TABLE optimization_opportunities IS 'Identifies and tracks cost optimization opportunities';
COMMENT ON TABLE formulation_benchmarks IS 'Stores industry and company benchmarks for comparison';

-- Add Triggers for Updated Timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_formulation_metrics_updated_at 
    BEFORE UPDATE ON formulation_metrics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
