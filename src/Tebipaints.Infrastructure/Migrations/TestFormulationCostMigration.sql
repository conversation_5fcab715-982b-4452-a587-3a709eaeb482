-- Test script to verify FormulationCost migration works correctly
-- This script can be run after the migration to test the new columns

-- Test 1: Insert a formulation with cost data
INSERT INTO formulations (
    id, name, description, shelf_life, estimated_production_time, status,
    material_cost_amount, material_cost_currency,
    labor_cost_amount, labor_cost_currency,
    overhead_cost_amount, overhead_cost_currency,
    total_cost_amount, total_cost_currency,
    cost_per_unit_amount, cost_per_unit_currency,
    cost_calculated_at, cost_calculation_basis
) VALUES (
    gen_random_uuid(), 
    'Test Paint Formula', 
    'Test formulation with cost data', 
    365, 
    '02:30:00', 
    'Draft',
    60.00, 'GHS',
    25.00, 'GHS', 
    15.00, 'GHS',
    100.00, 'GHS',
    0.10, 'GHS',
    NOW(),
    'Test calculation: 1000 units production'
);

-- Test 2: Insert a formulation without cost data (should work with NULLs)
INSERT INTO formulations (
    id, name, description, shelf_life, estimated_production_time, status
) VALUES (
    gen_random_uuid(), 
    'Test Paint Formula No Cost', 
    'Test formulation without cost data', 
    365, 
    '02:30:00', 
    'Draft'
);

-- Test 3: Query formulations with cost data
SELECT 
    name,
    total_cost_amount,
    total_cost_currency,
    cost_per_unit_amount,
    cost_calculated_at,
    cost_calculation_basis
FROM formulations 
WHERE total_cost_amount IS NOT NULL;

-- Test 4: Query formulations without cost data
SELECT 
    name,
    total_cost_amount,
    cost_calculated_at
FROM formulations 
WHERE total_cost_amount IS NULL;

-- Test 5: Test the indexes
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM formulations 
WHERE total_cost_amount > 50.00;

EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM formulations 
WHERE cost_calculated_at > NOW() - INTERVAL '1 day';

-- Cleanup test data
DELETE FROM formulations WHERE name LIKE 'Test Paint Formula%';
