using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tebipaints.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class updatePurchaseOrderConfiguration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "discount_percentage",
                table: "purchase_orders",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "freight_charge_amount",
                table: "purchase_orders",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "freight_charge_currency",
                table: "purchase_orders",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "promise_date",
                table: "purchase_orders",
                type: "timestamp with time zone",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "discount_percentage",
                table: "purchase_orders");

            migrationBuilder.DropColumn(
                name: "freight_charge_amount",
                table: "purchase_orders");

            migrationBuilder.DropColumn(
                name: "freight_charge_currency",
                table: "purchase_orders");

            migrationBuilder.DropColumn(
                name: "promise_date",
                table: "purchase_orders");
        }
    }
}
