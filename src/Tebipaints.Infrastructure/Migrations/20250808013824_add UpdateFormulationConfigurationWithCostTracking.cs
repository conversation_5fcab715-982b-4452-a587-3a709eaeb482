using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tebipaints.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class addUpdateFormulationConfigurationWithCostTracking : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "cost_calculated_at",
                table: "formulations",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "cost_calculation_basis",
                table: "formulations",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "cost_per_unit_amount",
                table: "formulations",
                type: "numeric(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "cost_per_unit_currency",
                table: "formulations",
                type: "character varying(3)",
                maxLength: 3,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "labor_cost_amount",
                table: "formulations",
                type: "numeric(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "labor_cost_currency",
                table: "formulations",
                type: "character varying(3)",
                maxLength: 3,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "material_cost_amount",
                table: "formulations",
                type: "numeric(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "material_cost_currency",
                table: "formulations",
                type: "character varying(3)",
                maxLength: 3,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "overhead_cost_amount",
                table: "formulations",
                type: "numeric(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "overhead_cost_currency",
                table: "formulations",
                type: "character varying(3)",
                maxLength: 3,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "total_cost_amount",
                table: "formulations",
                type: "numeric(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "total_cost_currency",
                table: "formulations",
                type: "character varying(3)",
                maxLength: 3,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "cost_calculated_at",
                table: "formulations");

            migrationBuilder.DropColumn(
                name: "cost_calculation_basis",
                table: "formulations");

            migrationBuilder.DropColumn(
                name: "cost_per_unit_amount",
                table: "formulations");

            migrationBuilder.DropColumn(
                name: "cost_per_unit_currency",
                table: "formulations");

            migrationBuilder.DropColumn(
                name: "labor_cost_amount",
                table: "formulations");

            migrationBuilder.DropColumn(
                name: "labor_cost_currency",
                table: "formulations");

            migrationBuilder.DropColumn(
                name: "material_cost_amount",
                table: "formulations");

            migrationBuilder.DropColumn(
                name: "material_cost_currency",
                table: "formulations");

            migrationBuilder.DropColumn(
                name: "overhead_cost_amount",
                table: "formulations");

            migrationBuilder.DropColumn(
                name: "overhead_cost_currency",
                table: "formulations");

            migrationBuilder.DropColumn(
                name: "total_cost_amount",
                table: "formulations");

            migrationBuilder.DropColumn(
                name: "total_cost_currency",
                table: "formulations");
        }
    }
}
