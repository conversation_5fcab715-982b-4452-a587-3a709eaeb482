-- Migration: Add FormulationCost columns to formulations table
-- Date: 2025-01-08
-- Description: Adds cost tracking columns to support formulation cost calculation

-- Add cost columns to formulations table
ALTER TABLE formulations 
ADD COLUMN material_cost_amount DECIMAL(18,2) NULL,
ADD COLUMN material_cost_currency VARCHAR(3) NULL,
ADD COLUMN labor_cost_amount DECIMAL(18,2) NULL,
ADD COLUMN labor_cost_currency VARCHAR(3) NULL,
ADD COLUMN overhead_cost_amount DECIMAL(18,2) NULL,
ADD COLUMN overhead_cost_currency VARCHAR(3) NULL,
ADD COLUMN total_cost_amount DECIMAL(18,2) NULL,
ADD COLUMN total_cost_currency VARCHAR(3) NULL,
ADD COLUMN cost_per_unit_amount DECIMAL(18,2) NULL,
ADD COLUMN cost_per_unit_currency VARCHAR(3) NULL,
ADD COLUMN cost_calculated_at TIMESTAMP NULL,
ADD COLUMN cost_calculation_basis VARCHAR(500) NULL;

-- Add indexes for performance
CREATE INDEX idx_formulations_total_cost ON formulations(total_cost_amount) WHERE total_cost_amount IS NOT NULL;
CREATE INDEX idx_formulations_cost_calculated_at ON formulations(cost_calculated_at) WHERE cost_calculated_at IS NOT NULL;

-- Add comments for documentation
COMMENT ON COLUMN formulations.material_cost_amount IS 'Total material cost for the formulation';
COMMENT ON COLUMN formulations.material_cost_currency IS 'Currency code for material cost (e.g., GHS, USD)';
COMMENT ON COLUMN formulations.labor_cost_amount IS 'Total labor cost for the formulation';
COMMENT ON COLUMN formulations.labor_cost_currency IS 'Currency code for labor cost';
COMMENT ON COLUMN formulations.overhead_cost_amount IS 'Total overhead cost for the formulation';
COMMENT ON COLUMN formulations.overhead_cost_currency IS 'Currency code for overhead cost';
COMMENT ON COLUMN formulations.total_cost_amount IS 'Total cost (material + labor + overhead)';
COMMENT ON COLUMN formulations.total_cost_currency IS 'Currency code for total cost';
COMMENT ON COLUMN formulations.cost_per_unit_amount IS 'Cost per unit of production';
COMMENT ON COLUMN formulations.cost_per_unit_currency IS 'Currency code for cost per unit';
COMMENT ON COLUMN formulations.cost_calculated_at IS 'Timestamp when cost was last calculated';
COMMENT ON COLUMN formulations.cost_calculation_basis IS 'Description of how the cost was calculated';
