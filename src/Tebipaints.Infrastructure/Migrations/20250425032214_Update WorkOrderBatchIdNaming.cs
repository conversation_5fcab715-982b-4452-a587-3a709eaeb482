using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tebipaints.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateWorkOrderBatchIdNaming : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_work_order_batches_work_orders_work_order_id",
                table: "work_order_batches");

            migrationBuilder.DropIndex(
                name: "ix_work_order_batches_work_order_id",
                table: "work_order_batches");

            migrationBuilder.DropColumn(
                name: "work_order_id1",
                table: "work_order_batches");

            migrationBuilder.CreateIndex(
                name: "ix_work_order_batches_work_order_id",
                table: "work_order_batches",
                column: "work_order_id");

            migrationBuilder.AddForeignKey(
                name: "fk_work_order_batches_work_orders_work_order_id",
                table: "work_order_batches",
                column: "work_order_id",
                principalTable: "work_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_work_order_batches_work_orders_work_order_id",
                table: "work_order_batches");

            migrationBuilder.DropIndex(
                name: "ix_work_order_batches_work_order_id",
                table: "work_order_batches");

            migrationBuilder.AddColumn<Guid>(
                name: "work_order_id1",
                table: "work_order_batches",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "ix_work_order_batches_work_order_id",
                table: "work_order_batches",
                column: "work_order_id1");

            migrationBuilder.AddForeignKey(
                name: "fk_work_order_batches_work_orders_work_order_id",
                table: "work_order_batches",
                column: "work_order_id1",
                principalTable: "work_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
