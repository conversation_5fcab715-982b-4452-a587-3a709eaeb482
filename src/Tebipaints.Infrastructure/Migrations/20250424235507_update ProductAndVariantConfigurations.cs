using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tebipaints.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class updateProductAndVariantConfigurations : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "ProductSku",
                table: "products",
                newName: "sku_value");

            migrationBuilder.RenameColumn(
                name: "VariantSku",
                table: "product_variants",
                newName: "sku_value");

            migrationBuilder.RenameIndex(
                name: "ix_product_variants_variant_sku",
                table: "product_variants",
                newName: "ix_product_variants_sku_value");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "sku_value",
                table: "products",
                newName: "ProductSku");

            migrationBuilder.RenameColumn(
                name: "sku_value",
                table: "product_variants",
                newName: "VariantSku");

            migrationBuilder.RenameIndex(
                name: "ix_product_variants_sku_value",
                table: "product_variants",
                newName: "ix_product_variants_variant_sku");
        }
    }
}
