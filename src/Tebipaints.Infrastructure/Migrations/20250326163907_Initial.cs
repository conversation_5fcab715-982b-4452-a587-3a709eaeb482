using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Tebipaints.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class Initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "inventory_double",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    location = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_inventory_double", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "inventory_int",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    location = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_inventory_int", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "invoices",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    customer_id = table.Column<Guid>(type: "uuid", nullable: true),
                    walk_in_customer_name = table.Column<string>(type: "text", nullable: true),
                    invoice_number_value = table.Column<string>(type: "text", nullable: false),
                    subtotal_amount = table.Column<decimal>(type: "numeric", nullable: false),
                    subtotal_currency = table.Column<string>(type: "text", nullable: false),
                    tax_amount = table.Column<decimal>(type: "numeric", nullable: false),
                    tax_currency = table.Column<string>(type: "text", nullable: false),
                    grand_total_amount = table.Column<decimal>(type: "numeric", nullable: false),
                    grand_total_currency = table.Column<string>(type: "text", nullable: false),
                    amount_paid_amount = table.Column<decimal>(type: "numeric", nullable: false),
                    amount_paid_currency = table.Column<string>(type: "text", nullable: false),
                    amount_due_amount = table.Column<decimal>(type: "numeric", nullable: false),
                    amount_due_currency = table.Column<string>(type: "text", nullable: false),
                    due_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    created_on_utc = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    status = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_invoices", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "materials",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    code = table.Column<string>(type: "text", nullable: false),
                    name = table.Column<string>(type: "text", nullable: false),
                    description = table.Column<string>(type: "text", nullable: false),
                    type = table.Column<string>(type: "text", nullable: false),
                    default_unit = table.Column<string>(type: "text", nullable: false),
                    status = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_materials", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "outbox_messages",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    occurred_on_utc = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    type = table.Column<string>(type: "text", nullable: false),
                    content = table.Column<string>(type: "jsonb", nullable: false),
                    processed_on_utc = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    error = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_outbox_messages", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "production_lines",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "text", nullable: false),
                    description = table.Column<string>(type: "text", nullable: false),
                    status = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_production_lines", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "products",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    formulation_id = table.Column<Guid>(type: "uuid", nullable: true),
                    name_value = table.Column<string>(type: "text", nullable: false),
                    ProductSku = table.Column<string>(type: "text", nullable: false),
                    color_value = table.Column<string>(type: "text", nullable: false),
                    volume_value = table.Column<decimal>(type: "numeric", nullable: false),
                    volume_unit = table.Column<string>(type: "text", nullable: false),
                    type = table.Column<string>(type: "text", nullable: false),
                    price_amount = table.Column<decimal>(type: "numeric", nullable: true),
                    price_currency = table.Column<string>(type: "text", nullable: true),
                    created_on_utc = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    status = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_products", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "purchase_orders",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    order_number_value = table.Column<string>(type: "text", nullable: false),
                    supplier_id = table.Column<Guid>(type: "uuid", nullable: false),
                    order_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    requested_by = table.Column<string>(type: "text", nullable: true),
                    approved_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    approved_by = table.Column<string>(type: "text", nullable: true),
                    status = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_purchase_orders", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "sales_orders",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    order_number_value = table.Column<string>(type: "text", nullable: false),
                    customer_id = table.Column<Guid>(type: "uuid", nullable: true),
                    walk_in_customer_name = table.Column<string>(type: "text", nullable: true),
                    status = table.Column<int>(type: "integer", nullable: false),
                    created_on_utc = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    fulfilled_on_utc = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    invoice_id = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_sales_orders", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "suppliers",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    code = table.Column<string>(type: "text", nullable: false),
                    name = table.Column<string>(type: "text", nullable: false),
                    status = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_suppliers", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "work_orders",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    work_order_number = table.Column<string>(type: "text", nullable: false),
                    product_id = table.Column<Guid>(type: "uuid", nullable: false),
                    variant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    packaging_type = table.Column<string>(type: "text", nullable: false),
                    packaging_capacity_value = table.Column<decimal>(type: "numeric", nullable: false),
                    packaging_capacity_unit = table.Column<string>(type: "text", nullable: false),
                    formulation_id = table.Column<Guid>(type: "uuid", nullable: false),
                    total_quantity_value = table.Column<decimal>(type: "numeric", nullable: false),
                    total_quantity_unit = table.Column<string>(type: "text", nullable: false),
                    start_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    due_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    status = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_work_orders", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "MaterialInventory",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    material_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MaterialInventory", x => x.id);
                    table.ForeignKey(
                        name: "fk_material_inventory_inventory_double_id",
                        column: x => x.id,
                        principalTable: "inventory_double",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MaterialInventoryTransactions",
                columns: table => new
                {
                    transaction_id = table.Column<Guid>(type: "uuid", nullable: false),
                    inventory_double_id = table.Column<Guid>(type: "uuid", nullable: false),
                    type = table.Column<string>(type: "text", nullable: false),
                    quantity = table.Column<double>(type: "double precision", nullable: false),
                    reference = table.Column<string>(type: "text", nullable: false),
                    timestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_material_inventory_transactions", x => new { x.transaction_id, x.inventory_double_id });
                    table.ForeignKey(
                        name: "fk_material_inventory_transactions_inventory_double_inventory_do",
                        column: x => x.inventory_double_id,
                        principalTable: "inventory_double",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "product_inventories",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    product_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_product_inventories", x => x.id);
                    table.ForeignKey(
                        name: "fk_product_inventories_inventory_int_id",
                        column: x => x.id,
                        principalTable: "inventory_int",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ProductInventoryTransactions",
                columns: table => new
                {
                    transaction_id = table.Column<Guid>(type: "uuid", nullable: false),
                    inventory_int_id = table.Column<Guid>(type: "uuid", nullable: false),
                    type = table.Column<string>(type: "text", nullable: false),
                    quantity = table.Column<int>(type: "integer", nullable: false),
                    reference = table.Column<string>(type: "text", nullable: false),
                    timestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_product_inventory_transactions", x => new { x.transaction_id, x.inventory_int_id });
                    table.ForeignKey(
                        name: "fk_product_inventory_transactions_inventory_int_inventory_int_id",
                        column: x => x.inventory_int_id,
                        principalTable: "inventory_int",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "invoice_lines",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    product_id = table.Column<Guid>(type: "uuid", nullable: false),
                    sku_value = table.Column<string>(type: "text", nullable: false),
                    description = table.Column<string>(type: "text", nullable: false),
                    unit_price_amount = table.Column<decimal>(type: "numeric", nullable: false),
                    unit_price_currency = table.Column<string>(type: "text", nullable: false),
                    line_item_discount_amount = table.Column<decimal>(type: "numeric", nullable: false),
                    line_item_discount_currency = table.Column<string>(type: "text", nullable: false),
                    quantity = table.Column<int>(type: "integer", nullable: false),
                    invoice_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_invoice_lines", x => x.id);
                    table.ForeignKey(
                        name: "fk_invoice_lines_invoices_invoice_id",
                        column: x => x.invoice_id,
                        principalTable: "invoices",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "invoice_payments",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    invoice_id = table.Column<Guid>(type: "uuid", nullable: false),
                    amount_amount = table.Column<decimal>(type: "numeric", nullable: false),
                    amount_currency = table.Column<string>(type: "text", nullable: false),
                    date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    payment_method = table.Column<string>(type: "text", nullable: false),
                    reference = table.Column<string>(type: "text", nullable: true),
                    receipt_number_value = table.Column<string>(type: "text", nullable: true),
                    receipt_invoice_id = table.Column<Guid>(type: "uuid", nullable: true),
                    receipt_issued_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    receipt_amount_amount = table.Column<decimal>(type: "numeric", nullable: true),
                    receipt_amount_currency = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_invoice_payments", x => x.id);
                    table.ForeignKey(
                        name: "fk_invoice_payments_invoices_invoice_id",
                        column: x => x.invoice_id,
                        principalTable: "invoices",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "invoice_refunds",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    invoice_id = table.Column<Guid>(type: "uuid", nullable: false),
                    amount_amount = table.Column<decimal>(type: "numeric", nullable: false),
                    amount_currency = table.Column<string>(type: "text", nullable: false),
                    reason = table.Column<string>(type: "text", nullable: false),
                    refund_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    status = table.Column<int>(type: "integer", nullable: false),
                    processed_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_invoice_refunds", x => x.id);
                    table.ForeignKey(
                        name: "fk_invoice_refunds_invoices_invoice_id",
                        column: x => x.invoice_id,
                        principalTable: "invoices",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "capabilities",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    product_type = table.Column<string>(type: "text", nullable: false),
                    min_batch_size_value = table.Column<decimal>(type: "numeric", nullable: false),
                    min_batch_size_unit = table.Column<string>(type: "text", nullable: false),
                    max_batch_size_value = table.Column<decimal>(type: "numeric", nullable: false),
                    max_batch_size_unit = table.Column<string>(type: "text", nullable: false),
                    production_line_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_capabilities", x => x.id);
                    table.ForeignKey(
                        name: "fk_capabilities_production_lines_production_line_id",
                        column: x => x.production_line_id,
                        principalTable: "production_lines",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "production_line_schedules",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    batch_id = table.Column<Guid>(type: "uuid", nullable: false),
                    production_line_id = table.Column<Guid>(type: "uuid", nullable: false),
                    start_time = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    end_time = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_production_line_schedules", x => x.id);
                    table.ForeignKey(
                        name: "fk_production_line_schedules_production_lines_production_line_",
                        column: x => x.production_line_id,
                        principalTable: "production_lines",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "product_variants",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    product_id = table.Column<Guid>(type: "uuid", nullable: false),
                    VariantSku = table.Column<string>(type: "text", nullable: false),
                    volume_value = table.Column<decimal>(type: "numeric", nullable: false),
                    volume_unit = table.Column<string>(type: "text", nullable: false),
                    color_value = table.Column<string>(type: "text", nullable: true),
                    status = table.Column<string>(type: "text", nullable: false),
                    product_id1 = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_product_variants", x => x.id);
                    table.ForeignKey(
                        name: "fk_product_variants_products_product_id",
                        column: x => x.product_id,
                        principalTable: "products",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_product_variants_products_product_id1",
                        column: x => x.product_id1,
                        principalTable: "products",
                        principalColumn: "id");
                });

            migrationBuilder.CreateTable(
                name: "purchase_order_item",
                columns: table => new
                {
                    purchase_order_id = table.Column<Guid>(type: "uuid", nullable: false),
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    material_id = table.Column<Guid>(type: "uuid", nullable: false),
                    material_name = table.Column<string>(type: "text", nullable: false),
                    quantity_value = table.Column<decimal>(type: "numeric", nullable: false),
                    quantity_unit = table.Column<string>(type: "text", nullable: false),
                    unit_price_amount = table.Column<decimal>(type: "numeric", nullable: false),
                    unit_price_currency = table.Column<string>(type: "text", nullable: false),
                    notes = table.Column<string>(type: "text", nullable: true),
                    received_quantity_value = table.Column<decimal>(type: "numeric", nullable: false),
                    received_quantity_unit = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_purchase_order_item", x => new { x.purchase_order_id, x.id });
                    table.ForeignKey(
                        name: "fk_purchase_order_item_purchase_orders_purchase_order_id",
                        column: x => x.purchase_order_id,
                        principalTable: "purchase_orders",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "sales_order_line_item",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    sales_order_id = table.Column<Guid>(type: "uuid", nullable: false),
                    product_id = table.Column<Guid>(type: "uuid", nullable: false),
                    description = table.Column<string>(type: "text", nullable: false),
                    quantity = table.Column<int>(type: "integer", nullable: false),
                    unit_price_amount = table.Column<decimal>(type: "numeric", nullable: false),
                    unit_price_currency = table.Column<string>(type: "text", nullable: false),
                    discount_percentage = table.Column<decimal>(type: "numeric", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_sales_order_line_item", x => new { x.sales_order_id, x.id });
                    table.ForeignKey(
                        name: "fk_sales_order_line_item_sales_orders_sales_order_id",
                        column: x => x.sales_order_id,
                        principalTable: "sales_orders",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SalesOrderAppliedDiscounts",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "text", nullable: false),
                    type = table.Column<string>(type: "text", nullable: false),
                    percentage = table.Column<decimal>(type: "numeric", nullable: false),
                    description = table.Column<string>(type: "text", nullable: true),
                    valid_from = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    valid_to = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_active = table.Column<bool>(type: "boolean", nullable: false),
                    sales_order_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_sales_order_applied_discounts", x => x.id);
                    table.ForeignKey(
                        name: "fk_sales_order_applied_discounts_sales_orders_sales_order_id",
                        column: x => x.sales_order_id,
                        principalTable: "sales_orders",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "contracts",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    contract_number_value = table.Column<string>(type: "text", nullable: false),
                    start_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    end_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    status = table.Column<string>(type: "text", nullable: false),
                    supplier_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_contracts", x => x.id);
                    table.ForeignKey(
                        name: "fk_contracts_suppliers_supplier_id",
                        column: x => x.supplier_id,
                        principalTable: "suppliers",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "bill_of_materials",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    material_id = table.Column<Guid>(type: "uuid", nullable: false),
                    required_quantity_value = table.Column<decimal>(type: "numeric", nullable: false),
                    required_quantity_unit = table.Column<string>(type: "text", nullable: false),
                    batch_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_bill_of_materials", x => x.id);
                    table.ForeignKey(
                        name: "fk_bill_of_materials_work_orders_batch_id",
                        column: x => x.batch_id,
                        principalTable: "work_orders",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "work_order_batches",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    batch_number = table.Column<string>(type: "text", nullable: false),
                    formulation_id = table.Column<Guid>(type: "uuid", nullable: false),
                    work_order_id = table.Column<Guid>(type: "uuid", nullable: false),
                    target_quantity_value = table.Column<decimal>(type: "numeric", nullable: false),
                    target_quantity_unit = table.Column<string>(type: "text", nullable: false),
                    status = table.Column<string>(type: "text", nullable: false),
                    planned_start_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    actual_start_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    completion_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    estimated_duration = table.Column<TimeSpan>(type: "interval", nullable: false),
                    actual_duration = table.Column<TimeSpan>(type: "interval", nullable: true),
                    actual_yield = table.Column<double>(type: "double precision", nullable: true),
                    notes = table.Column<string>(type: "text", nullable: true),
                    work_order_id1 = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_work_order_batches", x => x.id);
                    table.ForeignKey(
                        name: "fk_work_order_batches_work_orders_work_order_id",
                        column: x => x.work_order_id1,
                        principalTable: "work_orders",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "material_inventory_reservations",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    material_id = table.Column<Guid>(type: "uuid", nullable: false),
                    quantity = table.Column<double>(type: "double precision", nullable: false),
                    purpose = table.Column<string>(type: "text", nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    inventory_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_material_inventory_reservations", x => x.id);
                    table.ForeignKey(
                        name: "fk_material_inventory_reservations_material_inventory_inventory",
                        column: x => x.inventory_id,
                        principalTable: "MaterialInventory",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "product_variant_stock_levels",
                columns: table => new
                {
                    inventory_id = table.Column<Guid>(type: "uuid", nullable: false),
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    VariantId = table.Column<Guid>(type: "uuid", nullable: false),
                    PackagingType = table.Column<string>(type: "text", nullable: false),
                    variant_packaging_capacity_value = table.Column<decimal>(type: "numeric", nullable: false),
                    variant_packaging_capacity_unit = table.Column<string>(type: "text", nullable: false),
                    stock_level = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_product_variant_stock_levels", x => new { x.inventory_id, x.id });
                    table.ForeignKey(
                        name: "fk_product_variant_stock_levels_product_inventories_inventory_",
                        column: x => x.inventory_id,
                        principalTable: "product_inventories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "product_variant_transactions",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    base_transaction_transaction_id = table.Column<Guid>(type: "uuid", nullable: false),
                    base_transaction_type = table.Column<string>(type: "text", nullable: false),
                    base_transaction_quantity = table.Column<int>(type: "integer", nullable: false),
                    base_transaction_reference = table.Column<string>(type: "text", nullable: false),
                    base_transaction_timestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    variant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    inventory_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_product_variant_transactions", x => x.id);
                    table.ForeignKey(
                        name: "fk_product_variant_transactions_product_inventories_inventory_",
                        column: x => x.inventory_id,
                        principalTable: "product_inventories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "product_variant_packaging_options",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    type = table.Column<string>(type: "text", nullable: false),
                    material = table.Column<string>(type: "text", nullable: false),
                    capacity_value = table.Column<decimal>(type: "numeric", nullable: false),
                    capacity_unit = table.Column<string>(type: "text", nullable: false),
                    price_amount = table.Column<decimal>(type: "numeric", nullable: false),
                    price_currency = table.Column<string>(type: "text", nullable: false),
                    status = table.Column<int>(type: "integer", nullable: false),
                    product_variant_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_product_variant_packaging_options", x => x.id);
                    table.ForeignKey(
                        name: "fk_product_variant_packaging_options_product_variants_product_",
                        column: x => x.product_variant_id,
                        principalTable: "product_variants",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "sales_order_line_item_applied_discounts",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    sales_order_line_item_sales_order_id = table.Column<Guid>(type: "uuid", nullable: false),
                    sales_order_line_item_id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "text", nullable: false),
                    type = table.Column<string>(type: "text", nullable: false),
                    percentage = table.Column<decimal>(type: "numeric", nullable: false),
                    description = table.Column<string>(type: "text", nullable: true),
                    valid_from = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    valid_to = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    is_active = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_sales_order_line_item_applied_discounts", x => new { x.sales_order_line_item_sales_order_id, x.sales_order_line_item_id, x.id });
                    table.ForeignKey(
                        name: "fk_sales_order_line_item_applied_discounts_sales_order_line_it",
                        columns: x => new { x.sales_order_line_item_sales_order_id, x.sales_order_line_item_id },
                        principalTable: "sales_order_line_item",
                        principalColumns: new[] { "sales_order_id", "id" },
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "contract_term",
                columns: table => new
                {
                    contract_id = table.Column<Guid>(type: "uuid", nullable: false),
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    description = table.Column<string>(type: "text", nullable: false),
                    type = table.Column<string>(type: "text", nullable: false),
                    expiration_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_contract_term", x => new { x.contract_id, x.id });
                    table.ForeignKey(
                        name: "fk_contract_term_contracts_contract_id",
                        column: x => x.contract_id,
                        principalTable: "contracts",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "contracted_material",
                columns: table => new
                {
                    contract_id = table.Column<Guid>(type: "uuid", nullable: false),
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    material_id = table.Column<Guid>(type: "uuid", nullable: false),
                    material_name = table.Column<string>(type: "text", nullable: false),
                    unit_price_amount = table.Column<decimal>(type: "numeric", nullable: false),
                    unit_price_currency = table.Column<string>(type: "text", nullable: false),
                    minimum_order_value = table.Column<decimal>(type: "numeric", nullable: false),
                    minimum_order_unit = table.Column<string>(type: "text", nullable: false),
                    maximum_order_value = table.Column<decimal>(type: "numeric", nullable: true),
                    maximum_order_unit = table.Column<string>(type: "text", nullable: true),
                    lead_time_days = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_contracted_material", x => new { x.contract_id, x.id });
                    table.ForeignKey(
                        name: "fk_contracted_material_contracts_contract_id",
                        column: x => x.contract_id,
                        principalTable: "contracts",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "batch_ingredients",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    material_id = table.Column<Guid>(type: "uuid", nullable: false),
                    required_quantity_value = table.Column<decimal>(type: "numeric", nullable: false),
                    required_quantity_unit = table.Column<string>(type: "text", nullable: false),
                    actual_quantity = table.Column<double>(type: "double precision", nullable: true),
                    batch_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_batch_ingredients", x => x.id);
                    table.ForeignKey(
                        name: "fk_batch_ingredients_work_order_batches_batch_id",
                        column: x => x.batch_id,
                        principalTable: "work_order_batches",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "batch_production_losses",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    quantity = table.Column<double>(type: "double precision", nullable: false),
                    loss_type = table.Column<string>(type: "text", nullable: false),
                    reason = table.Column<string>(type: "text", nullable: false),
                    recorded_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    batch_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_batch_production_losses", x => x.id);
                    table.ForeignKey(
                        name: "fk_batch_production_losses_work_order_batches_batch_id",
                        column: x => x.batch_id,
                        principalTable: "work_order_batches",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "batch_quality_checks",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    parameter = table.Column<string>(type: "text", nullable: false),
                    measured_value = table.Column<double>(type: "double precision", nullable: false),
                    minimum_value = table.Column<double>(type: "double precision", nullable: false),
                    maximum_value = table.Column<double>(type: "double precision", nullable: false),
                    checked_by = table.Column<string>(type: "text", nullable: false),
                    checked_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    batch_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_batch_quality_checks", x => x.id);
                    table.ForeignKey(
                        name: "fk_batch_quality_checks_work_order_batches_batch_id",
                        column: x => x.batch_id,
                        principalTable: "work_order_batches",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "ix_batch_ingredients_batch_id",
                table: "batch_ingredients",
                column: "batch_id");

            migrationBuilder.CreateIndex(
                name: "ix_batch_production_losses_batch_id",
                table: "batch_production_losses",
                column: "batch_id");

            migrationBuilder.CreateIndex(
                name: "ix_batch_quality_checks_batch_id",
                table: "batch_quality_checks",
                column: "batch_id");

            migrationBuilder.CreateIndex(
                name: "ix_bill_of_materials_batch_id",
                table: "bill_of_materials",
                column: "batch_id");

            migrationBuilder.CreateIndex(
                name: "ix_capabilities_production_line_id",
                table: "capabilities",
                column: "production_line_id");

            migrationBuilder.CreateIndex(
                name: "ix_contracts_supplier_id",
                table: "contracts",
                column: "supplier_id");

            migrationBuilder.CreateIndex(
                name: "ix_invoice_lines_invoice_id",
                table: "invoice_lines",
                column: "invoice_id");

            migrationBuilder.CreateIndex(
                name: "ix_invoice_payments_invoice_id",
                table: "invoice_payments",
                column: "invoice_id");

            migrationBuilder.CreateIndex(
                name: "ix_invoice_refunds_invoice_id",
                table: "invoice_refunds",
                column: "invoice_id");

            migrationBuilder.CreateIndex(
                name: "ix_material_inventory_reservations_inventory_id",
                table: "material_inventory_reservations",
                column: "inventory_id");

            migrationBuilder.CreateIndex(
                name: "ix_material_inventory_material_id",
                table: "MaterialInventory",
                column: "material_id");

            migrationBuilder.CreateIndex(
                name: "ix_material_inventory_transactions_inventory_double_id",
                table: "MaterialInventoryTransactions",
                column: "inventory_double_id");

            migrationBuilder.CreateIndex(
                name: "ix_product_inventories_product_id",
                table: "product_inventories",
                column: "product_id");

            migrationBuilder.CreateIndex(
                name: "ix_product_variant_packaging_options_product_variant_id",
                table: "product_variant_packaging_options",
                column: "product_variant_id");

            migrationBuilder.CreateIndex(
                name: "ix_product_variant_transactions_inventory_id",
                table: "product_variant_transactions",
                column: "inventory_id");

            migrationBuilder.CreateIndex(
                name: "ix_product_variants_product_id",
                table: "product_variants",
                column: "product_id");

            migrationBuilder.CreateIndex(
                name: "ix_product_variants_product_id1",
                table: "product_variants",
                column: "product_id1");

            migrationBuilder.CreateIndex(
                name: "ix_product_variants_variant_sku",
                table: "product_variants",
                column: "VariantSku",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_product_inventory_transactions_inventory_int_id",
                table: "ProductInventoryTransactions",
                column: "inventory_int_id");

            migrationBuilder.CreateIndex(
                name: "ix_production_line_schedules_batch_id",
                table: "production_line_schedules",
                column: "batch_id");

            migrationBuilder.CreateIndex(
                name: "ix_production_line_schedules_batch_id_production_line_id",
                table: "production_line_schedules",
                columns: new[] { "batch_id", "production_line_id" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_production_line_schedules_production_line_id",
                table: "production_line_schedules",
                column: "production_line_id");

            migrationBuilder.CreateIndex(
                name: "ix_production_line_schedules_start_time_end_time",
                table: "production_line_schedules",
                columns: new[] { "start_time", "end_time" });

            migrationBuilder.CreateIndex(
                name: "ix_sales_order_applied_discounts_sales_order_id",
                table: "SalesOrderAppliedDiscounts",
                column: "sales_order_id");

            migrationBuilder.CreateIndex(
                name: "ix_work_order_batches_work_order_id",
                table: "work_order_batches",
                column: "work_order_id1");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "batch_ingredients");

            migrationBuilder.DropTable(
                name: "batch_production_losses");

            migrationBuilder.DropTable(
                name: "batch_quality_checks");

            migrationBuilder.DropTable(
                name: "bill_of_materials");

            migrationBuilder.DropTable(
                name: "capabilities");

            migrationBuilder.DropTable(
                name: "contract_term");

            migrationBuilder.DropTable(
                name: "contracted_material");

            migrationBuilder.DropTable(
                name: "invoice_lines");

            migrationBuilder.DropTable(
                name: "invoice_payments");

            migrationBuilder.DropTable(
                name: "invoice_refunds");

            migrationBuilder.DropTable(
                name: "material_inventory_reservations");

            migrationBuilder.DropTable(
                name: "MaterialInventoryTransactions");

            migrationBuilder.DropTable(
                name: "materials");

            migrationBuilder.DropTable(
                name: "outbox_messages");

            migrationBuilder.DropTable(
                name: "product_variant_packaging_options");

            migrationBuilder.DropTable(
                name: "product_variant_stock_levels");

            migrationBuilder.DropTable(
                name: "product_variant_transactions");

            migrationBuilder.DropTable(
                name: "ProductInventoryTransactions");

            migrationBuilder.DropTable(
                name: "production_line_schedules");

            migrationBuilder.DropTable(
                name: "purchase_order_item");

            migrationBuilder.DropTable(
                name: "sales_order_line_item_applied_discounts");

            migrationBuilder.DropTable(
                name: "SalesOrderAppliedDiscounts");

            migrationBuilder.DropTable(
                name: "work_order_batches");

            migrationBuilder.DropTable(
                name: "contracts");

            migrationBuilder.DropTable(
                name: "invoices");

            migrationBuilder.DropTable(
                name: "MaterialInventory");

            migrationBuilder.DropTable(
                name: "product_variants");

            migrationBuilder.DropTable(
                name: "product_inventories");

            migrationBuilder.DropTable(
                name: "production_lines");

            migrationBuilder.DropTable(
                name: "purchase_orders");

            migrationBuilder.DropTable(
                name: "sales_order_line_item");

            migrationBuilder.DropTable(
                name: "work_orders");

            migrationBuilder.DropTable(
                name: "suppliers");

            migrationBuilder.DropTable(
                name: "inventory_double");

            migrationBuilder.DropTable(
                name: "products");

            migrationBuilder.DropTable(
                name: "inventory_int");

            migrationBuilder.DropTable(
                name: "sales_orders");
        }
    }
}
