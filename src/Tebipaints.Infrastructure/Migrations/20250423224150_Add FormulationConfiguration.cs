using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Tebipaints.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddFormulationConfiguration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Formulations",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    description = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    shelf_life = table.Column<int>(type: "integer", nullable: false),
                    estimated_production_time = table.Column<TimeSpan>(type: "interval", nullable: false),
                    status = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_formulations", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "Ingredients",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    material_id = table.Column<Guid>(type: "uuid", nullable: false),
                    quantity_value = table.Column<decimal>(type: "numeric", nullable: false),
                    quantity_unit = table.Column<string>(type: "text", nullable: false),
                    formulation_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_ingredients", x => x.id);
                    table.ForeignKey(
                        name: "fk_ingredients_formulations_formulation_id",
                        column: x => x.formulation_id,
                        principalTable: "Formulations",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Versions",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    time_stamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    snapshot_minimum_production_quantity_value = table.Column<decimal>(type: "numeric", nullable: false),
                    snapshot_minimum_production_quantity_unit = table.Column<string>(type: "text", nullable: false),
                    snapshot_estimated_production_time = table.Column<TimeSpan>(type: "interval", nullable: false),
                    snapshot_shelf_life = table.Column<int>(type: "integer", nullable: false),
                    snapshot_instructions = table.Column<string>(type: "text", nullable: false),
                    is_finalized = table.Column<bool>(type: "boolean", nullable: false),
                    formulation_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_versions", x => x.id);
                    table.ForeignKey(
                        name: "fk_versions_formulations_formulation_id",
                        column: x => x.formulation_id,
                        principalTable: "Formulations",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ChangeLog",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    version_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_change_log", x => x.id);
                    table.ForeignKey(
                        name: "fk_change_log_versions_version_id",
                        column: x => x.version_id,
                        principalTable: "Versions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ingredient_snapshot",
                columns: table => new
                {
                    formulation_snapshot_version_id = table.Column<Guid>(type: "uuid", nullable: false),
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ingredient_id = table.Column<Guid>(type: "uuid", nullable: false),
                    raw_material_id = table.Column<Guid>(type: "uuid", nullable: false),
                    quantity_value = table.Column<decimal>(type: "numeric", nullable: false),
                    quantity_unit = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_ingredient_snapshot", x => new { x.formulation_snapshot_version_id, x.id });
                    table.ForeignKey(
                        name: "fk_ingredient_snapshot_versions_formulation_snapshot_version_id",
                        column: x => x.formulation_snapshot_version_id,
                        principalTable: "Versions",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "change_log_entry",
                columns: table => new
                {
                    change_log_id = table.Column<Guid>(type: "uuid", nullable: false),
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    time_stamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    description = table.Column<string>(type: "text", nullable: false),
                    author = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_change_log_entry", x => new { x.change_log_id, x.id });
                    table.ForeignKey(
                        name: "fk_change_log_entry_change_log_change_log_id",
                        column: x => x.change_log_id,
                        principalTable: "ChangeLog",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "ix_change_log_version_id",
                table: "ChangeLog",
                column: "version_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_ingredients_formulation_id",
                table: "Ingredients",
                column: "formulation_id");

            migrationBuilder.CreateIndex(
                name: "ix_versions_formulation_id",
                table: "Versions",
                column: "formulation_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "change_log_entry");

            migrationBuilder.DropTable(
                name: "ingredient_snapshot");

            migrationBuilder.DropTable(
                name: "Ingredients");

            migrationBuilder.DropTable(
                name: "ChangeLog");

            migrationBuilder.DropTable(
                name: "Versions");

            migrationBuilder.DropTable(
                name: "Formulations");
        }
    }
}
