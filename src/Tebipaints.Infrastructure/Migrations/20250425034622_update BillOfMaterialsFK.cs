using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tebipaints.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class updateBillOfMaterialsFK : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_bill_of_materials_work_orders_batch_id",
                table: "bill_of_materials");

            migrationBuilder.RenameColumn(
                name: "batch_id",
                table: "bill_of_materials",
                newName: "work_order_id");

            migrationBuilder.RenameIndex(
                name: "ix_bill_of_materials_batch_id",
                table: "bill_of_materials",
                newName: "ix_bill_of_materials_work_order_id");

            migrationBuilder.AddForeignKey(
                name: "fk_bill_of_materials_work_orders_work_order_id",
                table: "bill_of_materials",
                column: "work_order_id",
                principalTable: "work_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_bill_of_materials_work_orders_work_order_id",
                table: "bill_of_materials");

            migrationBuilder.RenameColumn(
                name: "work_order_id",
                table: "bill_of_materials",
                newName: "batch_id");

            migrationBuilder.RenameIndex(
                name: "ix_bill_of_materials_work_order_id",
                table: "bill_of_materials",
                newName: "ix_bill_of_materials_batch_id");

            migrationBuilder.AddForeignKey(
                name: "fk_bill_of_materials_work_orders_batch_id",
                table: "bill_of_materials",
                column: "batch_id",
                principalTable: "work_orders",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
