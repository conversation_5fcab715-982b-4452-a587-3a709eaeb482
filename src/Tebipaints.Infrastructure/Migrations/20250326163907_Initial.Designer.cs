// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Tebipaints.Infrastructure;

#nullable disable

namespace Tebipaints.Infrastructure.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250326163907_Initial")]
    partial class Initial
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Tebipaints.Domain.Inventory.Inventory<double>", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("Location")
                        .HasColumnType("integer")
                        .HasColumnName("location");

                    b.HasKey("Id");

                    b.ToTable("inventory_double", (string)null);

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("Tebipaints.Domain.Inventory.Inventory<int>", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("Location")
                        .HasColumnType("integer")
                        .HasColumnName("location");

                    b.HasKey("Id");

                    b.ToTable("inventory_int", (string)null);

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("Tebipaints.Domain.Invoice.Invoice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedOnUtc")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on_utc");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uuid")
                        .HasColumnName("customer_id");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("due_date");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("status");

                    b.Property<string>("WalkInCustomerName")
                        .HasColumnType("text")
                        .HasColumnName("walk_in_customer_name");

                    b.HasKey("Id")
                        .HasName("pk_invoices");

                    b.ToTable("invoices", (string)null);
                });

            modelBuilder.Entity("Tebipaints.Domain.Material.Material", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("code");

                    b.Property<string>("DefaultUnit")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("default_unit");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("status");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("type");

                    b.HasKey("Id")
                        .HasName("pk_materials");

                    b.ToTable("materials", (string)null);
                });

            modelBuilder.Entity("Tebipaints.Domain.Procurement.PurchaseOrder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("ApprovedBy")
                        .HasColumnType("text")
                        .HasColumnName("approved_by");

                    b.Property<DateTime?>("ApprovedDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("approved_date");

                    b.Property<DateTime>("OrderDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("order_date");

                    b.Property<string>("RequestedBy")
                        .HasColumnType("text")
                        .HasColumnName("requested_by");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("status");

                    b.Property<Guid>("SupplierId")
                        .HasColumnType("uuid")
                        .HasColumnName("supplier_id");

                    b.HasKey("Id")
                        .HasName("pk_purchase_orders");

                    b.ToTable("purchase_orders", (string)null);
                });

            modelBuilder.Entity("Tebipaints.Domain.Procurement.Supplier", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("code");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("status");

                    b.HasKey("Id")
                        .HasName("pk_suppliers");

                    b.ToTable("suppliers", (string)null);
                });

            modelBuilder.Entity("Tebipaints.Domain.Product.Product", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedOnUtc")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on_utc");

                    b.Property<Guid?>("FormulationId")
                        .HasColumnType("uuid")
                        .HasColumnName("formulation_id");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("status");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("type");

                    b.HasKey("Id")
                        .HasName("pk_products");

                    b.ToTable("products", (string)null);
                });

            modelBuilder.Entity("Tebipaints.Domain.Product.ProductVariant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<Guid?>("ProductId1")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id1");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("status");

                    b.HasKey("Id")
                        .HasName("pk_product_variants");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("ix_product_variants_product_id");

                    b.HasIndex("ProductId1")
                        .HasDatabaseName("ix_product_variants_product_id1");

                    b.ToTable("product_variants", (string)null);
                });

            modelBuilder.Entity("Tebipaints.Domain.Production.ProductionLine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("status");

                    b.HasKey("Id")
                        .HasName("pk_production_lines");

                    b.ToTable("production_lines", (string)null);
                });

            modelBuilder.Entity("Tebipaints.Domain.Production.WorkOrder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("due_date");

                    b.Property<Guid>("FormulationId")
                        .HasColumnType("uuid")
                        .HasColumnName("formulation_id");

                    b.Property<string>("PackagingType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("packaging_type");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("start_date");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("status");

                    b.Property<Guid>("VariantId")
                        .HasColumnType("uuid")
                        .HasColumnName("variant_id");

                    b.Property<string>("WorkOrderNumber")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("work_order_number");

                    b.HasKey("Id")
                        .HasName("pk_work_orders");

                    b.ToTable("work_orders", (string)null);
                });

            modelBuilder.Entity("Tebipaints.Domain.SalesOrder.SalesOrder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedOnUtc")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_on_utc");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uuid")
                        .HasColumnName("customer_id");

                    b.Property<DateTime?>("FulfilledOnUtc")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("fulfilled_on_utc");

                    b.Property<Guid?>("InvoiceId")
                        .HasColumnType("uuid")
                        .HasColumnName("invoice_id");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("WalkInCustomerName")
                        .HasColumnType("text")
                        .HasColumnName("walk_in_customer_name");

                    b.HasKey("Id")
                        .HasName("pk_sales_orders");

                    b.ToTable("sales_orders", (string)null);
                });

            modelBuilder.Entity("Tebipaints.Infrastructure.Outbox.OutboxMessage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("content");

                    b.Property<string>("Error")
                        .HasColumnType("text")
                        .HasColumnName("error");

                    b.Property<DateTime>("OccurredOnUtc")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("occurred_on_utc");

                    b.Property<DateTime?>("ProcessedOnUtc")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("processed_on_utc");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("type");

                    b.HasKey("Id")
                        .HasName("pk_outbox_messages");

                    b.ToTable("outbox_messages", (string)null);
                });

            modelBuilder.Entity("Tebipaints.Domain.Inventory.MaterialInventory", b =>
                {
                    b.HasBaseType("Tebipaints.Domain.Inventory.Inventory<double>");

                    b.Property<Guid>("MaterialId")
                        .HasColumnType("uuid")
                        .HasColumnName("material_id");

                    b.HasIndex("MaterialId")
                        .HasDatabaseName("ix_material_inventory_material_id");

                    b.ToTable("MaterialInventory", (string)null);
                });

            modelBuilder.Entity("Tebipaints.Domain.Inventory.ProductInventory", b =>
                {
                    b.HasBaseType("Tebipaints.Domain.Inventory.Inventory<int>");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uuid")
                        .HasColumnName("product_id");

                    b.HasIndex("ProductId")
                        .HasDatabaseName("ix_product_inventories_product_id");

                    b.ToTable("product_inventories", (string)null);
                });

            modelBuilder.Entity("Tebipaints.Domain.Inventory.Inventory<double>", b =>
                {
                    b.OwnsMany("Tebipaints.Domain.Inventory.InventoryTransaction<double>", "Transactions", b1 =>
                        {
                            b1.Property<Guid>("TransactionId")
                                .HasColumnType("uuid")
                                .HasColumnName("transaction_id");

                            b1.Property<Guid>("InventoryId")
                                .HasColumnType("uuid")
                                .HasColumnName("inventory_double_id");

                            b1.Property<double>("Quantity")
                                .HasColumnType("double precision")
                                .HasColumnName("quantity");

                            b1.Property<string>("Reference")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("reference");

                            b1.Property<DateTime>("Timestamp")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("timestamp");

                            b1.Property<string>("Type")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("type");

                            b1.HasKey("TransactionId", "InventoryId")
                                .HasName("pk_material_inventory_transactions");

                            b1.HasIndex("InventoryId")
                                .HasDatabaseName("ix_material_inventory_transactions_inventory_double_id");

                            b1.ToTable("MaterialInventoryTransactions", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("InventoryId")
                                .HasConstraintName("fk_material_inventory_transactions_inventory_double_inventory_do");
                        });

                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("Tebipaints.Domain.Inventory.Inventory<int>", b =>
                {
                    b.OwnsMany("Tebipaints.Domain.Inventory.InventoryTransaction<int>", "Transactions", b1 =>
                        {
                            b1.Property<Guid>("TransactionId")
                                .HasColumnType("uuid")
                                .HasColumnName("transaction_id");

                            b1.Property<Guid>("InventoryId")
                                .HasColumnType("uuid")
                                .HasColumnName("inventory_int_id");

                            b1.Property<int>("Quantity")
                                .HasColumnType("integer")
                                .HasColumnName("quantity");

                            b1.Property<string>("Reference")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("reference");

                            b1.Property<DateTime>("Timestamp")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("timestamp");

                            b1.Property<string>("Type")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("type");

                            b1.HasKey("TransactionId", "InventoryId")
                                .HasName("pk_product_inventory_transactions");

                            b1.HasIndex("InventoryId")
                                .HasDatabaseName("ix_product_inventory_transactions_inventory_int_id");

                            b1.ToTable("ProductInventoryTransactions", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("InventoryId")
                                .HasConstraintName("fk_product_inventory_transactions_inventory_int_inventory_int_id");
                        });

                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("Tebipaints.Domain.Invoice.Invoice", b =>
                {
                    b.OwnsOne("Tebipaints.Domain.Shared.Money", "AmountDue", b1 =>
                        {
                            b1.Property<Guid>("InvoiceId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric")
                                .HasColumnName("amount_due_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("amount_due_currency");

                            b1.HasKey("InvoiceId");

                            b1.ToTable("invoices");

                            b1.WithOwner()
                                .HasForeignKey("InvoiceId")
                                .HasConstraintName("fk_invoices_invoices_id");
                        });

                    b.OwnsOne("Tebipaints.Domain.Shared.Money", "AmountPaid", b1 =>
                        {
                            b1.Property<Guid>("InvoiceId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric")
                                .HasColumnName("amount_paid_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("amount_paid_currency");

                            b1.HasKey("InvoiceId");

                            b1.ToTable("invoices");

                            b1.WithOwner()
                                .HasForeignKey("InvoiceId")
                                .HasConstraintName("fk_invoices_invoices_id");
                        });

                    b.OwnsOne("Tebipaints.Domain.Shared.Money", "GrandTotal", b1 =>
                        {
                            b1.Property<Guid>("InvoiceId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric")
                                .HasColumnName("grand_total_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("grand_total_currency");

                            b1.HasKey("InvoiceId");

                            b1.ToTable("invoices");

                            b1.WithOwner()
                                .HasForeignKey("InvoiceId")
                                .HasConstraintName("fk_invoices_invoices_id");
                        });

                    b.OwnsOne("Tebipaints.Domain.Shared.Money", "Subtotal", b1 =>
                        {
                            b1.Property<Guid>("InvoiceId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric")
                                .HasColumnName("subtotal_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("subtotal_currency");

                            b1.HasKey("InvoiceId");

                            b1.ToTable("invoices");

                            b1.WithOwner()
                                .HasForeignKey("InvoiceId")
                                .HasConstraintName("fk_invoices_invoices_id");
                        });

                    b.OwnsOne("Tebipaints.Domain.Shared.Money", "Tax", b1 =>
                        {
                            b1.Property<Guid>("InvoiceId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric")
                                .HasColumnName("tax_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("tax_currency");

                            b1.HasKey("InvoiceId");

                            b1.ToTable("invoices");

                            b1.WithOwner()
                                .HasForeignKey("InvoiceId")
                                .HasConstraintName("fk_invoices_invoices_id");
                        });

                    b.OwnsOne("Tebipaints.Domain.Invoice.InvoiceNumber", "InvoiceNumber", b1 =>
                        {
                            b1.Property<Guid>("InvoiceId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("invoice_number_value");

                            b1.HasKey("InvoiceId");

                            b1.ToTable("invoices");

                            b1.WithOwner()
                                .HasForeignKey("InvoiceId")
                                .HasConstraintName("fk_invoices_invoices_id");
                        });

                    b.OwnsMany("Tebipaints.Domain.Invoice.LineItem", "LineItems", b1 =>
                        {
                            b1.Property<Guid>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<string>("Description")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("description");

                            b1.Property<Guid>("InvoiceId")
                                .HasColumnType("uuid")
                                .HasColumnName("invoice_id");

                            b1.Property<Guid>("ProductId")
                                .HasColumnType("uuid")
                                .HasColumnName("product_id");

                            b1.Property<int>("Quantity")
                                .HasColumnType("integer")
                                .HasColumnName("quantity");

                            b1.HasKey("Id")
                                .HasName("pk_invoice_lines");

                            b1.HasIndex("InvoiceId")
                                .HasDatabaseName("ix_invoice_lines_invoice_id");

                            b1.ToTable("invoice_lines", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("InvoiceId")
                                .HasConstraintName("fk_invoice_lines_invoices_invoice_id");

                            b1.OwnsOne("Tebipaints.Domain.Shared.Money", "LineItemDiscount", b2 =>
                                {
                                    b2.Property<Guid>("LineItemId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("id");

                                    b2.Property<decimal>("Amount")
                                        .HasColumnType("numeric")
                                        .HasColumnName("line_item_discount_amount");

                                    b2.Property<string>("Currency")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("line_item_discount_currency");

                                    b2.HasKey("LineItemId");

                                    b2.ToTable("invoice_lines");

                                    b2.WithOwner()
                                        .HasForeignKey("LineItemId")
                                        .HasConstraintName("fk_invoice_lines_invoice_lines_id");
                                });

                            b1.OwnsOne("Tebipaints.Domain.Shared.SKU", "Sku", b2 =>
                                {
                                    b2.Property<Guid>("LineItemId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("id");

                                    b2.Property<string>("Value")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("sku_value");

                                    b2.HasKey("LineItemId");

                                    b2.ToTable("invoice_lines");

                                    b2.WithOwner()
                                        .HasForeignKey("LineItemId")
                                        .HasConstraintName("fk_invoice_lines_invoice_lines_id");
                                });

                            b1.OwnsOne("Tebipaints.Domain.Shared.Money", "UnitPrice", b2 =>
                                {
                                    b2.Property<Guid>("LineItemId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("id");

                                    b2.Property<decimal>("Amount")
                                        .HasColumnType("numeric")
                                        .HasColumnName("unit_price_amount");

                                    b2.Property<string>("Currency")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("unit_price_currency");

                                    b2.HasKey("LineItemId");

                                    b2.ToTable("invoice_lines");

                                    b2.WithOwner()
                                        .HasForeignKey("LineItemId")
                                        .HasConstraintName("fk_invoice_lines_invoice_lines_id");
                                });

                            b1.Navigation("LineItemDiscount")
                                .IsRequired();

                            b1.Navigation("Sku")
                                .IsRequired();

                            b1.Navigation("UnitPrice")
                                .IsRequired();
                        });

                    b.OwnsMany("Tebipaints.Domain.Invoice.Payment", "Payments", b1 =>
                        {
                            b1.Property<Guid>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<DateTime>("Date")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("date");

                            b1.Property<Guid>("InvoiceId")
                                .HasColumnType("uuid")
                                .HasColumnName("invoice_id");

                            b1.Property<string>("PaymentMethod")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("payment_method");

                            b1.Property<string>("Reference")
                                .HasColumnType("text")
                                .HasColumnName("reference");

                            b1.HasKey("Id")
                                .HasName("pk_invoice_payments");

                            b1.HasIndex("InvoiceId")
                                .HasDatabaseName("ix_invoice_payments_invoice_id");

                            b1.ToTable("invoice_payments", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("InvoiceId")
                                .HasConstraintName("fk_invoice_payments_invoices_invoice_id");

                            b1.OwnsOne("Tebipaints.Domain.Shared.Money", "Amount", b2 =>
                                {
                                    b2.Property<Guid>("PaymentId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("id");

                                    b2.Property<decimal>("Amount")
                                        .HasColumnType("numeric")
                                        .HasColumnName("amount_amount");

                                    b2.Property<string>("Currency")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("amount_currency");

                                    b2.HasKey("PaymentId");

                                    b2.ToTable("invoice_payments");

                                    b2.WithOwner()
                                        .HasForeignKey("PaymentId")
                                        .HasConstraintName("fk_invoice_payments_invoice_payments_id");
                                });

                            b1.OwnsOne("Tebipaints.Domain.Invoice.Receipt", "Receipt", b2 =>
                                {
                                    b2.Property<Guid>("PaymentId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("id");

                                    b2.Property<Guid>("InvoiceId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("receipt_invoice_id");

                                    b2.Property<DateTime>("IssuedDate")
                                        .HasColumnType("timestamp with time zone")
                                        .HasColumnName("receipt_issued_date");

                                    b2.HasKey("PaymentId");

                                    b2.ToTable("invoice_payments");

                                    b2.WithOwner()
                                        .HasForeignKey("PaymentId")
                                        .HasConstraintName("fk_invoice_payments_invoice_payments_id");

                                    b2.OwnsOne("Tebipaints.Domain.Shared.Money", "Amount", b3 =>
                                        {
                                            b3.Property<Guid>("ReceiptPaymentId")
                                                .HasColumnType("uuid")
                                                .HasColumnName("id");

                                            b3.Property<decimal>("Amount")
                                                .HasColumnType("numeric")
                                                .HasColumnName("receipt_amount_amount");

                                            b3.Property<string>("Currency")
                                                .IsRequired()
                                                .HasColumnType("text")
                                                .HasColumnName("receipt_amount_currency");

                                            b3.HasKey("ReceiptPaymentId");

                                            b3.ToTable("invoice_payments");

                                            b3.WithOwner()
                                                .HasForeignKey("ReceiptPaymentId")
                                                .HasConstraintName("fk_invoice_payments_invoice_payments_id");
                                        });

                                    b2.OwnsOne("Tebipaints.Domain.Invoice.ReceiptNumber", "Number", b3 =>
                                        {
                                            b3.Property<Guid>("ReceiptPaymentId")
                                                .HasColumnType("uuid")
                                                .HasColumnName("id");

                                            b3.Property<string>("Value")
                                                .IsRequired()
                                                .HasColumnType("text")
                                                .HasColumnName("receipt_number_value");

                                            b3.HasKey("ReceiptPaymentId");

                                            b3.ToTable("invoice_payments");

                                            b3.WithOwner()
                                                .HasForeignKey("ReceiptPaymentId")
                                                .HasConstraintName("fk_invoice_payments_invoice_payments_id");
                                        });

                                    b2.Navigation("Amount")
                                        .IsRequired();

                                    b2.Navigation("Number")
                                        .IsRequired();
                                });

                            b1.Navigation("Amount")
                                .IsRequired();

                            b1.Navigation("Receipt");
                        });

                    b.OwnsMany("Tebipaints.Domain.Invoice.Refund", "Refunds", b1 =>
                        {
                            b1.Property<Guid>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<Guid>("InvoiceId")
                                .HasColumnType("uuid")
                                .HasColumnName("invoice_id");

                            b1.Property<DateTime?>("ProcessedDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("processed_date");

                            b1.Property<string>("Reason")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("reason");

                            b1.Property<DateTime>("RefundDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("refund_date");

                            b1.Property<int>("Status")
                                .HasColumnType("integer")
                                .HasColumnName("status");

                            b1.HasKey("Id")
                                .HasName("pk_invoice_refunds");

                            b1.HasIndex("InvoiceId")
                                .HasDatabaseName("ix_invoice_refunds_invoice_id");

                            b1.ToTable("invoice_refunds", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("InvoiceId")
                                .HasConstraintName("fk_invoice_refunds_invoices_invoice_id");

                            b1.OwnsOne("Tebipaints.Domain.Shared.Money", "Amount", b2 =>
                                {
                                    b2.Property<Guid>("RefundId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("id");

                                    b2.Property<decimal>("Amount")
                                        .HasColumnType("numeric")
                                        .HasColumnName("amount_amount");

                                    b2.Property<string>("Currency")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("amount_currency");

                                    b2.HasKey("RefundId");

                                    b2.ToTable("invoice_refunds");

                                    b2.WithOwner()
                                        .HasForeignKey("RefundId")
                                        .HasConstraintName("fk_invoice_refunds_invoice_refunds_id");
                                });

                            b1.Navigation("Amount")
                                .IsRequired();
                        });

                    b.Navigation("AmountDue")
                        .IsRequired();

                    b.Navigation("AmountPaid")
                        .IsRequired();

                    b.Navigation("GrandTotal")
                        .IsRequired();

                    b.Navigation("InvoiceNumber")
                        .IsRequired();

                    b.Navigation("LineItems");

                    b.Navigation("Payments");

                    b.Navigation("Refunds");

                    b.Navigation("Subtotal")
                        .IsRequired();

                    b.Navigation("Tax")
                        .IsRequired();
                });

            modelBuilder.Entity("Tebipaints.Domain.Procurement.PurchaseOrder", b =>
                {
                    b.OwnsMany("Tebipaints.Domain.Procurement.PurchaseOrderItem", "Items", b1 =>
                        {
                            b1.Property<Guid>("PurchaseOrderId")
                                .HasColumnType("uuid")
                                .HasColumnName("purchase_order_id");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasColumnName("id");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<Guid>("MaterialId")
                                .HasColumnType("uuid")
                                .HasColumnName("material_id");

                            b1.Property<string>("MaterialName")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("material_name");

                            b1.Property<string>("Notes")
                                .HasColumnType("text")
                                .HasColumnName("notes");

                            b1.HasKey("PurchaseOrderId", "Id")
                                .HasName("pk_purchase_order_item");

                            b1.ToTable("purchase_order_item", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("PurchaseOrderId")
                                .HasConstraintName("fk_purchase_order_item_purchase_orders_purchase_order_id");

                            b1.OwnsOne("Tebipaints.Domain.Shared.Measurement", "Quantity", b2 =>
                                {
                                    b2.Property<Guid>("PurchaseOrderItemPurchaseOrderId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("purchase_order_id");

                                    b2.Property<int>("PurchaseOrderItemId")
                                        .HasColumnType("integer")
                                        .HasColumnName("id");

                                    b2.Property<string>("Unit")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("quantity_unit");

                                    b2.Property<decimal>("Value")
                                        .HasColumnType("numeric")
                                        .HasColumnName("quantity_value");

                                    b2.HasKey("PurchaseOrderItemPurchaseOrderId", "PurchaseOrderItemId");

                                    b2.ToTable("purchase_order_item");

                                    b2.WithOwner()
                                        .HasForeignKey("PurchaseOrderItemPurchaseOrderId", "PurchaseOrderItemId")
                                        .HasConstraintName("fk_purchase_order_item_purchase_order_item_purchase_order_id_id");
                                });

                            b1.OwnsOne("Tebipaints.Domain.Shared.Measurement", "ReceivedQuantity", b2 =>
                                {
                                    b2.Property<Guid>("PurchaseOrderItemPurchaseOrderId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("purchase_order_id");

                                    b2.Property<int>("PurchaseOrderItemId")
                                        .HasColumnType("integer")
                                        .HasColumnName("id");

                                    b2.Property<string>("Unit")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("received_quantity_unit");

                                    b2.Property<decimal>("Value")
                                        .HasColumnType("numeric")
                                        .HasColumnName("received_quantity_value");

                                    b2.HasKey("PurchaseOrderItemPurchaseOrderId", "PurchaseOrderItemId");

                                    b2.ToTable("purchase_order_item");

                                    b2.WithOwner()
                                        .HasForeignKey("PurchaseOrderItemPurchaseOrderId", "PurchaseOrderItemId")
                                        .HasConstraintName("fk_purchase_order_item_purchase_order_item_purchase_order_id_id");
                                });

                            b1.OwnsOne("Tebipaints.Domain.Shared.Money", "UnitPrice", b2 =>
                                {
                                    b2.Property<Guid>("PurchaseOrderItemPurchaseOrderId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("purchase_order_id");

                                    b2.Property<int>("PurchaseOrderItemId")
                                        .HasColumnType("integer")
                                        .HasColumnName("id");

                                    b2.Property<decimal>("Amount")
                                        .HasColumnType("numeric")
                                        .HasColumnName("unit_price_amount");

                                    b2.Property<string>("Currency")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("unit_price_currency");

                                    b2.HasKey("PurchaseOrderItemPurchaseOrderId", "PurchaseOrderItemId");

                                    b2.ToTable("purchase_order_item");

                                    b2.WithOwner()
                                        .HasForeignKey("PurchaseOrderItemPurchaseOrderId", "PurchaseOrderItemId")
                                        .HasConstraintName("fk_purchase_order_item_purchase_order_item_purchase_order_id_id");
                                });

                            b1.Navigation("Quantity")
                                .IsRequired();

                            b1.Navigation("ReceivedQuantity")
                                .IsRequired();

                            b1.Navigation("UnitPrice")
                                .IsRequired();
                        });

                    b.OwnsOne("Tebipaints.Domain.Procurement.PurchaseOrderNumber", "OrderNumber", b1 =>
                        {
                            b1.Property<Guid>("PurchaseOrderId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("order_number_value");

                            b1.HasKey("PurchaseOrderId");

                            b1.ToTable("purchase_orders");

                            b1.WithOwner()
                                .HasForeignKey("PurchaseOrderId")
                                .HasConstraintName("fk_purchase_orders_purchase_orders_id");
                        });

                    b.Navigation("Items");

                    b.Navigation("OrderNumber")
                        .IsRequired();
                });

            modelBuilder.Entity("Tebipaints.Domain.Procurement.Supplier", b =>
                {
                    b.OwnsOne("Tebipaints.Domain.Procurement.ContactInfo", "ContactInfo", b1 =>
                        {
                            b1.Property<Guid>("SupplierId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.HasKey("SupplierId");

                            b1.ToTable("suppliers");

                            b1.WithOwner()
                                .HasForeignKey("SupplierId")
                                .HasConstraintName("fk_suppliers_suppliers_id");
                        });

                    b.OwnsMany("Tebipaints.Domain.Procurement.Contract", "Contracts", b1 =>
                        {
                            b1.Property<Guid>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<DateTime>("EndDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("end_date");

                            b1.Property<DateTime>("StartDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("start_date");

                            b1.Property<string>("Status")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("status");

                            b1.Property<Guid>("supplierId")
                                .HasColumnType("uuid")
                                .HasColumnName("supplier_id");

                            b1.HasKey("Id")
                                .HasName("pk_contracts");

                            b1.HasIndex("supplierId")
                                .HasDatabaseName("ix_contracts_supplier_id");

                            b1.ToTable("contracts", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("supplierId")
                                .HasConstraintName("fk_contracts_suppliers_supplier_id");

                            b1.OwnsOne("Tebipaints.Domain.Procurement.ContractNumber", "ContractNumber", b2 =>
                                {
                                    b2.Property<Guid>("ContractId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("id");

                                    b2.Property<string>("Value")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("contract_number_value");

                                    b2.HasKey("ContractId");

                                    b2.ToTable("contracts");

                                    b2.WithOwner()
                                        .HasForeignKey("ContractId")
                                        .HasConstraintName("fk_contracts_contracts_id");
                                });

                            b1.OwnsMany("Tebipaints.Domain.Procurement.ContractTerm", "Terms", b2 =>
                                {
                                    b2.Property<Guid>("ContractId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("contract_id");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("integer")
                                        .HasColumnName("id");

                                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b2.Property<int>("Id"));

                                    b2.Property<string>("Description")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("description");

                                    b2.Property<DateTime?>("ExpirationDate")
                                        .HasColumnType("timestamp with time zone")
                                        .HasColumnName("expiration_date");

                                    b2.Property<string>("Type")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("type");

                                    b2.HasKey("ContractId", "Id")
                                        .HasName("pk_contract_term");

                                    b2.ToTable("contract_term", (string)null);

                                    b2.WithOwner()
                                        .HasForeignKey("ContractId")
                                        .HasConstraintName("fk_contract_term_contracts_contract_id");
                                });

                            b1.OwnsMany("Tebipaints.Domain.Procurement.ContractedMaterial", "Materials", b2 =>
                                {
                                    b2.Property<Guid>("ContractId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("contract_id");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("integer")
                                        .HasColumnName("id");

                                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b2.Property<int>("Id"));

                                    b2.Property<int>("LeadTimeDays")
                                        .HasColumnType("integer")
                                        .HasColumnName("lead_time_days");

                                    b2.Property<Guid>("MaterialId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("material_id");

                                    b2.Property<string>("MaterialName")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("material_name");

                                    b2.HasKey("ContractId", "Id")
                                        .HasName("pk_contracted_material");

                                    b2.ToTable("contracted_material", (string)null);

                                    b2.WithOwner()
                                        .HasForeignKey("ContractId")
                                        .HasConstraintName("fk_contracted_material_contracts_contract_id");

                                    b2.OwnsOne("Tebipaints.Domain.Shared.Measurement", "MaximumOrder", b3 =>
                                        {
                                            b3.Property<Guid>("ContractedMaterialContractId")
                                                .HasColumnType("uuid")
                                                .HasColumnName("contract_id");

                                            b3.Property<int>("ContractedMaterialId")
                                                .HasColumnType("integer")
                                                .HasColumnName("id");

                                            b3.Property<string>("Unit")
                                                .IsRequired()
                                                .HasColumnType("text")
                                                .HasColumnName("maximum_order_unit");

                                            b3.Property<decimal>("Value")
                                                .HasColumnType("numeric")
                                                .HasColumnName("maximum_order_value");

                                            b3.HasKey("ContractedMaterialContractId", "ContractedMaterialId");

                                            b3.ToTable("contracted_material");

                                            b3.WithOwner()
                                                .HasForeignKey("ContractedMaterialContractId", "ContractedMaterialId")
                                                .HasConstraintName("fk_contracted_material_contracted_material_contract_id_id");
                                        });

                                    b2.OwnsOne("Tebipaints.Domain.Shared.Measurement", "MinimumOrder", b3 =>
                                        {
                                            b3.Property<Guid>("ContractedMaterialContractId")
                                                .HasColumnType("uuid")
                                                .HasColumnName("contract_id");

                                            b3.Property<int>("ContractedMaterialId")
                                                .HasColumnType("integer")
                                                .HasColumnName("id");

                                            b3.Property<string>("Unit")
                                                .IsRequired()
                                                .HasColumnType("text")
                                                .HasColumnName("minimum_order_unit");

                                            b3.Property<decimal>("Value")
                                                .HasColumnType("numeric")
                                                .HasColumnName("minimum_order_value");

                                            b3.HasKey("ContractedMaterialContractId", "ContractedMaterialId");

                                            b3.ToTable("contracted_material");

                                            b3.WithOwner()
                                                .HasForeignKey("ContractedMaterialContractId", "ContractedMaterialId")
                                                .HasConstraintName("fk_contracted_material_contracted_material_contract_id_id");
                                        });

                                    b2.OwnsOne("Tebipaints.Domain.Shared.Money", "UnitPrice", b3 =>
                                        {
                                            b3.Property<Guid>("ContractedMaterialContractId")
                                                .HasColumnType("uuid")
                                                .HasColumnName("contract_id");

                                            b3.Property<int>("ContractedMaterialId")
                                                .HasColumnType("integer")
                                                .HasColumnName("id");

                                            b3.Property<decimal>("Amount")
                                                .HasColumnType("numeric")
                                                .HasColumnName("unit_price_amount");

                                            b3.Property<string>("Currency")
                                                .IsRequired()
                                                .HasColumnType("text")
                                                .HasColumnName("unit_price_currency");

                                            b3.HasKey("ContractedMaterialContractId", "ContractedMaterialId");

                                            b3.ToTable("contracted_material");

                                            b3.WithOwner()
                                                .HasForeignKey("ContractedMaterialContractId", "ContractedMaterialId")
                                                .HasConstraintName("fk_contracted_material_contracted_material_contract_id_id");
                                        });

                                    b2.Navigation("MaximumOrder");

                                    b2.Navigation("MinimumOrder")
                                        .IsRequired();

                                    b2.Navigation("UnitPrice")
                                        .IsRequired();
                                });

                            b1.Navigation("ContractNumber")
                                .IsRequired();

                            b1.Navigation("Materials");

                            b1.Navigation("Terms");
                        });

                    b.Navigation("ContactInfo")
                        .IsRequired();

                    b.Navigation("Contracts");
                });

            modelBuilder.Entity("Tebipaints.Domain.Product.Product", b =>
                {
                    b.OwnsOne("Tebipaints.Domain.Product.Color", "Color", b1 =>
                        {
                            b1.Property<Guid>("ProductId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("color_value");

                            b1.HasKey("ProductId");

                            b1.ToTable("products");

                            b1.WithOwner()
                                .HasForeignKey("ProductId")
                                .HasConstraintName("fk_products_products_id");
                        });

                    b.OwnsOne("Tebipaints.Domain.Shared.Money", "Price", b1 =>
                        {
                            b1.Property<Guid>("ProductId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("numeric")
                                .HasColumnName("price_amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("price_currency");

                            b1.HasKey("ProductId");

                            b1.ToTable("products");

                            b1.WithOwner()
                                .HasForeignKey("ProductId")
                                .HasConstraintName("fk_products_products_id");
                        });

                    b.OwnsOne("Tebipaints.Domain.Shared.SKU", "Sku", b1 =>
                        {
                            b1.Property<Guid>("ProductId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("ProductSku");

                            b1.HasKey("ProductId");

                            b1.ToTable("products");

                            b1.WithOwner()
                                .HasForeignKey("ProductId")
                                .HasConstraintName("fk_products_products_id");
                        });

                    b.OwnsOne("Tebipaints.Domain.Product.Volume", "Volume", b1 =>
                        {
                            b1.Property<Guid>("ProductId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<string>("Unit")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("volume_unit");

                            b1.Property<decimal>("Value")
                                .HasColumnType("numeric")
                                .HasColumnName("volume_value");

                            b1.HasKey("ProductId");

                            b1.ToTable("products");

                            b1.WithOwner()
                                .HasForeignKey("ProductId")
                                .HasConstraintName("fk_products_products_id");
                        });

                    b.OwnsOne("Tebipaints.Domain.Product.ProductName", "Name", b1 =>
                        {
                            b1.Property<Guid>("ProductId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("name_value");

                            b1.HasKey("ProductId");

                            b1.ToTable("products");

                            b1.WithOwner()
                                .HasForeignKey("ProductId")
                                .HasConstraintName("fk_products_products_id");
                        });

                    b.Navigation("Color")
                        .IsRequired();

                    b.Navigation("Name")
                        .IsRequired();

                    b.Navigation("Price");

                    b.Navigation("Sku")
                        .IsRequired();

                    b.Navigation("Volume")
                        .IsRequired();
                });

            modelBuilder.Entity("Tebipaints.Domain.Product.ProductVariant", b =>
                {
                    b.HasOne("Tebipaints.Domain.Product.Product", null)
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_product_variants_products_product_id");

                    b.HasOne("Tebipaints.Domain.Product.Product", null)
                        .WithMany("Variants")
                        .HasForeignKey("ProductId1")
                        .HasConstraintName("fk_product_variants_products_product_id1");

                    b.OwnsMany("Tebipaints.Domain.Product.PackagingOption", "PackagingOptions", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasColumnName("id");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<string>("Material")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("material");

                            b1.Property<Guid>("ProductVariantId")
                                .HasColumnType("uuid")
                                .HasColumnName("product_variant_id");

                            b1.Property<int>("Status")
                                .HasColumnType("integer")
                                .HasColumnName("status");

                            b1.Property<string>("Type")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("type");

                            b1.HasKey("Id")
                                .HasName("pk_product_variant_packaging_options");

                            b1.HasIndex("ProductVariantId")
                                .HasDatabaseName("ix_product_variant_packaging_options_product_variant_id");

                            b1.ToTable("product_variant_packaging_options", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("ProductVariantId")
                                .HasConstraintName("fk_product_variant_packaging_options_product_variants_product_");

                            b1.OwnsOne("Tebipaints.Domain.Shared.Measurement", "Capacity", b2 =>
                                {
                                    b2.Property<int>("PackagingOptionId")
                                        .HasColumnType("integer")
                                        .HasColumnName("id");

                                    b2.Property<string>("Unit")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("capacity_unit");

                                    b2.Property<decimal>("Value")
                                        .HasColumnType("numeric")
                                        .HasColumnName("capacity_value");

                                    b2.HasKey("PackagingOptionId");

                                    b2.ToTable("product_variant_packaging_options");

                                    b2.WithOwner()
                                        .HasForeignKey("PackagingOptionId")
                                        .HasConstraintName("fk_product_variant_packaging_options_product_variant_packaging");
                                });

                            b1.OwnsOne("Tebipaints.Domain.Shared.Money", "Price", b2 =>
                                {
                                    b2.Property<int>("PackagingOptionId")
                                        .HasColumnType("integer")
                                        .HasColumnName("id");

                                    b2.Property<decimal>("Amount")
                                        .HasColumnType("numeric")
                                        .HasColumnName("price_amount");

                                    b2.Property<string>("Currency")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("price_currency");

                                    b2.HasKey("PackagingOptionId");

                                    b2.ToTable("product_variant_packaging_options");

                                    b2.WithOwner()
                                        .HasForeignKey("PackagingOptionId")
                                        .HasConstraintName("fk_product_variant_packaging_options_product_variant_packaging");
                                });

                            b1.Navigation("Capacity")
                                .IsRequired();

                            b1.Navigation("Price")
                                .IsRequired();
                        });

                    b.OwnsOne("Tebipaints.Domain.Product.Color", "Color", b1 =>
                        {
                            b1.Property<Guid>("ProductVariantId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("color_value");

                            b1.HasKey("ProductVariantId");

                            b1.ToTable("product_variants");

                            b1.WithOwner()
                                .HasForeignKey("ProductVariantId")
                                .HasConstraintName("fk_product_variants_product_variants_id");
                        });

                    b.OwnsOne("Tebipaints.Domain.Shared.SKU", "Sku", b1 =>
                        {
                            b1.Property<Guid>("ProductVariantId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("VariantSku");

                            b1.HasKey("ProductVariantId");

                            b1.HasIndex("Value")
                                .IsUnique()
                                .HasDatabaseName("ix_product_variants_variant_sku");

                            b1.ToTable("product_variants");

                            b1.WithOwner()
                                .HasForeignKey("ProductVariantId")
                                .HasConstraintName("fk_product_variants_product_variants_id");
                        });

                    b.OwnsOne("Tebipaints.Domain.Product.Volume", "Volume", b1 =>
                        {
                            b1.Property<Guid>("ProductVariantId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<string>("Unit")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("volume_unit");

                            b1.Property<decimal>("Value")
                                .HasColumnType("numeric")
                                .HasColumnName("volume_value");

                            b1.HasKey("ProductVariantId");

                            b1.ToTable("product_variants");

                            b1.WithOwner()
                                .HasForeignKey("ProductVariantId")
                                .HasConstraintName("fk_product_variants_product_variants_id");
                        });

                    b.Navigation("Color");

                    b.Navigation("PackagingOptions");

                    b.Navigation("Sku")
                        .IsRequired();

                    b.Navigation("Volume")
                        .IsRequired();
                });

            modelBuilder.Entity("Tebipaints.Domain.Production.ProductionLine", b =>
                {
                    b.OwnsMany("Tebipaints.Domain.Production.BatchSchedule", "Schedules", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasColumnName("id");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<Guid>("BatchId")
                                .HasColumnType("uuid")
                                .HasColumnName("batch_id");

                            b1.Property<DateTime>("EndTime")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("end_time");

                            b1.Property<Guid>("ProductionLineId")
                                .HasColumnType("uuid")
                                .HasColumnName("production_line_id");

                            b1.Property<DateTime>("StartTime")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("start_time");

                            b1.HasKey("Id")
                                .HasName("pk_production_line_schedules");

                            b1.HasIndex("BatchId")
                                .HasDatabaseName("ix_production_line_schedules_batch_id");

                            b1.HasIndex("ProductionLineId")
                                .HasDatabaseName("ix_production_line_schedules_production_line_id");

                            b1.HasIndex("BatchId", "ProductionLineId")
                                .IsUnique()
                                .HasDatabaseName("ix_production_line_schedules_batch_id_production_line_id");

                            b1.HasIndex("StartTime", "EndTime")
                                .HasDatabaseName("ix_production_line_schedules_start_time_end_time");

                            b1.ToTable("production_line_schedules", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("ProductionLineId")
                                .HasConstraintName("fk_production_line_schedules_production_lines_production_line_");
                        });

                    b.OwnsMany("Tebipaints.Domain.Production.ProductionCapability", "Capabilities", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasColumnName("id");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<string>("ProductType")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("product_type");

                            b1.Property<Guid>("ProductionLineId")
                                .HasColumnType("uuid")
                                .HasColumnName("production_line_id");

                            b1.HasKey("Id")
                                .HasName("pk_capabilities");

                            b1.HasIndex("ProductionLineId")
                                .HasDatabaseName("ix_capabilities_production_line_id");

                            b1.ToTable("capabilities", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("ProductionLineId")
                                .HasConstraintName("fk_capabilities_production_lines_production_line_id");

                            b1.OwnsOne("Tebipaints.Domain.Shared.Measurement", "MaxBatchSize", b2 =>
                                {
                                    b2.Property<int>("ProductionCapabilityId")
                                        .HasColumnType("integer")
                                        .HasColumnName("id");

                                    b2.Property<string>("Unit")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("max_batch_size_unit");

                                    b2.Property<decimal>("Value")
                                        .HasColumnType("numeric")
                                        .HasColumnName("max_batch_size_value");

                                    b2.HasKey("ProductionCapabilityId");

                                    b2.ToTable("capabilities");

                                    b2.WithOwner()
                                        .HasForeignKey("ProductionCapabilityId")
                                        .HasConstraintName("fk_capabilities_capabilities_id");
                                });

                            b1.OwnsOne("Tebipaints.Domain.Shared.Measurement", "MinBatchSize", b2 =>
                                {
                                    b2.Property<int>("ProductionCapabilityId")
                                        .HasColumnType("integer")
                                        .HasColumnName("id");

                                    b2.Property<string>("Unit")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("min_batch_size_unit");

                                    b2.Property<decimal>("Value")
                                        .HasColumnType("numeric")
                                        .HasColumnName("min_batch_size_value");

                                    b2.HasKey("ProductionCapabilityId");

                                    b2.ToTable("capabilities");

                                    b2.WithOwner()
                                        .HasForeignKey("ProductionCapabilityId")
                                        .HasConstraintName("fk_capabilities_capabilities_id");
                                });

                            b1.Navigation("MaxBatchSize")
                                .IsRequired();

                            b1.Navigation("MinBatchSize")
                                .IsRequired();
                        });

                    b.Navigation("Capabilities");

                    b.Navigation("Schedules");
                });

            modelBuilder.Entity("Tebipaints.Domain.Production.WorkOrder", b =>
                {
                    b.OwnsMany("Tebipaints.Domain.Production.BillOfMaterialItem", "BillOfMaterials", b1 =>
                        {
                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasColumnName("id");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<Guid>("BatchId")
                                .HasColumnType("uuid")
                                .HasColumnName("batch_id");

                            b1.Property<Guid>("MaterialId")
                                .HasColumnType("uuid")
                                .HasColumnName("material_id");

                            b1.HasKey("Id")
                                .HasName("pk_bill_of_materials");

                            b1.HasIndex("BatchId")
                                .HasDatabaseName("ix_bill_of_materials_batch_id");

                            b1.ToTable("bill_of_materials", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("BatchId")
                                .HasConstraintName("fk_bill_of_materials_work_orders_batch_id");

                            b1.OwnsOne("Tebipaints.Domain.Shared.Measurement", "RequiredQuantity", b2 =>
                                {
                                    b2.Property<int>("BillOfMaterialItemId")
                                        .HasColumnType("integer")
                                        .HasColumnName("id");

                                    b2.Property<string>("Unit")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("required_quantity_unit");

                                    b2.Property<decimal>("Value")
                                        .HasColumnType("numeric")
                                        .HasColumnName("required_quantity_value");

                                    b2.HasKey("BillOfMaterialItemId");

                                    b2.ToTable("bill_of_materials");

                                    b2.WithOwner()
                                        .HasForeignKey("BillOfMaterialItemId")
                                        .HasConstraintName("fk_bill_of_materials_bill_of_materials_id");
                                });

                            b1.Navigation("RequiredQuantity")
                                .IsRequired();
                        });

                    b.OwnsOne("Tebipaints.Domain.Shared.Measurement", "PackagingCapacity", b1 =>
                        {
                            b1.Property<Guid>("WorkOrderId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<string>("Unit")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("packaging_capacity_unit");

                            b1.Property<decimal>("Value")
                                .HasColumnType("numeric")
                                .HasColumnName("packaging_capacity_value");

                            b1.HasKey("WorkOrderId");

                            b1.ToTable("work_orders");

                            b1.WithOwner()
                                .HasForeignKey("WorkOrderId")
                                .HasConstraintName("fk_work_orders_work_orders_id");
                        });

                    b.OwnsOne("Tebipaints.Domain.Shared.Measurement", "TotalQuantity", b1 =>
                        {
                            b1.Property<Guid>("WorkOrderId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<string>("Unit")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("total_quantity_unit");

                            b1.Property<decimal>("Value")
                                .HasColumnType("numeric")
                                .HasColumnName("total_quantity_value");

                            b1.HasKey("WorkOrderId");

                            b1.ToTable("work_orders");

                            b1.WithOwner()
                                .HasForeignKey("WorkOrderId")
                                .HasConstraintName("fk_work_orders_work_orders_id");
                        });

                    b.OwnsMany("Tebipaints.Domain.Production.Batch", "Batches", b1 =>
                        {
                            b1.Property<Guid>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<TimeSpan?>("ActualDuration")
                                .HasColumnType("interval")
                                .HasColumnName("actual_duration");

                            b1.Property<DateTime?>("ActualStartDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("actual_start_date");

                            b1.Property<double?>("ActualYield")
                                .HasColumnType("double precision")
                                .HasColumnName("actual_yield");

                            b1.Property<string>("BatchNumber")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("batch_number");

                            b1.Property<DateTime?>("CompletionDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("completion_date");

                            b1.Property<TimeSpan>("EstimatedDuration")
                                .HasColumnType("interval")
                                .HasColumnName("estimated_duration");

                            b1.Property<Guid>("FormulationId")
                                .HasColumnType("uuid")
                                .HasColumnName("formulation_id");

                            b1.Property<string>("Notes")
                                .HasColumnType("text")
                                .HasColumnName("notes");

                            b1.Property<DateTime>("PlannedStartDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("planned_start_date");

                            b1.Property<string>("Status")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("status");

                            b1.Property<Guid>("WorkOrderId")
                                .HasColumnType("uuid")
                                .HasColumnName("work_order_id");

                            b1.Property<Guid>("work_order_id")
                                .HasColumnType("uuid")
                                .HasColumnName("work_order_id");

                            b1.HasKey("Id")
                                .HasName("pk_work_order_batches");

                            b1.HasIndex("work_order_id")
                                .HasDatabaseName("ix_work_order_batches_work_order_id");

                            b1.ToTable("work_order_batches", null, t =>
                                {
                                    t.Property("work_order_id")
                                        .HasColumnName("work_order_id1");
                                });

                            b1.WithOwner()
                                .HasForeignKey("work_order_id")
                                .HasConstraintName("fk_work_order_batches_work_orders_work_order_id");

                            b1.OwnsOne("Tebipaints.Domain.Shared.Measurement", "TargetQuantity", b2 =>
                                {
                                    b2.Property<Guid>("BatchId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("id");

                                    b2.Property<string>("Unit")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("target_quantity_unit");

                                    b2.Property<decimal>("Value")
                                        .HasColumnType("numeric")
                                        .HasColumnName("target_quantity_value");

                                    b2.HasKey("BatchId");

                                    b2.ToTable("work_order_batches");

                                    b2.WithOwner()
                                        .HasForeignKey("BatchId")
                                        .HasConstraintName("fk_work_order_batches_work_order_batches_id");
                                });

                            b1.OwnsMany("Tebipaints.Domain.Production.BatchIngredient", "Ingredients", b2 =>
                                {
                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("integer")
                                        .HasColumnName("id");

                                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b2.Property<int>("Id"));

                                    b2.Property<double?>("ActualQuantity")
                                        .HasColumnType("double precision")
                                        .HasColumnName("actual_quantity");

                                    b2.Property<Guid>("BatchId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("batch_id");

                                    b2.Property<Guid>("MaterialId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("material_id");

                                    b2.HasKey("Id")
                                        .HasName("pk_batch_ingredients");

                                    b2.HasIndex("BatchId")
                                        .HasDatabaseName("ix_batch_ingredients_batch_id");

                                    b2.ToTable("batch_ingredients", (string)null);

                                    b2.WithOwner()
                                        .HasForeignKey("BatchId")
                                        .HasConstraintName("fk_batch_ingredients_work_order_batches_batch_id");

                                    b2.OwnsOne("Tebipaints.Domain.Shared.Measurement", "RequiredQuantity", b3 =>
                                        {
                                            b3.Property<int>("BatchIngredientId")
                                                .HasColumnType("integer")
                                                .HasColumnName("id");

                                            b3.Property<string>("Unit")
                                                .IsRequired()
                                                .HasColumnType("text")
                                                .HasColumnName("required_quantity_unit");

                                            b3.Property<decimal>("Value")
                                                .HasColumnType("numeric")
                                                .HasColumnName("required_quantity_value");

                                            b3.HasKey("BatchIngredientId");

                                            b3.ToTable("batch_ingredients");

                                            b3.WithOwner()
                                                .HasForeignKey("BatchIngredientId")
                                                .HasConstraintName("fk_batch_ingredients_batch_ingredients_id");
                                        });

                                    b2.Navigation("RequiredQuantity")
                                        .IsRequired();
                                });

                            b1.OwnsMany("Tebipaints.Domain.Production.ProductionLoss", "ProductionLosses", b2 =>
                                {
                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("integer")
                                        .HasColumnName("id");

                                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b2.Property<int>("Id"));

                                    b2.Property<Guid>("BatchId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("batch_id");

                                    b2.Property<string>("LossType")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("loss_type");

                                    b2.Property<double>("Quantity")
                                        .HasColumnType("double precision")
                                        .HasColumnName("quantity");

                                    b2.Property<string>("Reason")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("reason");

                                    b2.Property<DateTime>("RecordedAt")
                                        .HasColumnType("timestamp with time zone")
                                        .HasColumnName("recorded_at");

                                    b2.HasKey("Id")
                                        .HasName("pk_batch_production_losses");

                                    b2.HasIndex("BatchId")
                                        .HasDatabaseName("ix_batch_production_losses_batch_id");

                                    b2.ToTable("batch_production_losses", (string)null);

                                    b2.WithOwner()
                                        .HasForeignKey("BatchId")
                                        .HasConstraintName("fk_batch_production_losses_work_order_batches_batch_id");
                                });

                            b1.OwnsMany("Tebipaints.Domain.Production.QualityCheck", "QualityChecks", b2 =>
                                {
                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("integer")
                                        .HasColumnName("id");

                                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b2.Property<int>("Id"));

                                    b2.Property<Guid>("BatchId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("batch_id");

                                    b2.Property<DateTime>("CheckedAt")
                                        .HasColumnType("timestamp with time zone")
                                        .HasColumnName("checked_at");

                                    b2.Property<string>("CheckedBy")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("checked_by");

                                    b2.Property<double>("MaximumValue")
                                        .HasColumnType("double precision")
                                        .HasColumnName("maximum_value");

                                    b2.Property<double>("MeasuredValue")
                                        .HasColumnType("double precision")
                                        .HasColumnName("measured_value");

                                    b2.Property<double>("MinimumValue")
                                        .HasColumnType("double precision")
                                        .HasColumnName("minimum_value");

                                    b2.Property<string>("Parameter")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("parameter");

                                    b2.HasKey("Id")
                                        .HasName("pk_batch_quality_checks");

                                    b2.HasIndex("BatchId")
                                        .HasDatabaseName("ix_batch_quality_checks_batch_id");

                                    b2.ToTable("batch_quality_checks", (string)null);

                                    b2.WithOwner()
                                        .HasForeignKey("BatchId")
                                        .HasConstraintName("fk_batch_quality_checks_work_order_batches_batch_id");
                                });

                            b1.Navigation("Ingredients");

                            b1.Navigation("ProductionLosses");

                            b1.Navigation("QualityChecks");

                            b1.Navigation("TargetQuantity")
                                .IsRequired();
                        });

                    b.Navigation("Batches");

                    b.Navigation("BillOfMaterials");

                    b.Navigation("PackagingCapacity")
                        .IsRequired();

                    b.Navigation("TotalQuantity")
                        .IsRequired();
                });

            modelBuilder.Entity("Tebipaints.Domain.SalesOrder.SalesOrder", b =>
                {
                    b.OwnsOne("Tebipaints.Domain.SalesOrder.OrderNumber", "OrderNumber", b1 =>
                        {
                            b1.Property<Guid>("SalesOrderId")
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("order_number_value");

                            b1.HasKey("SalesOrderId");

                            b1.ToTable("sales_orders");

                            b1.WithOwner()
                                .HasForeignKey("SalesOrderId")
                                .HasConstraintName("fk_sales_orders_sales_orders_id");
                        });

                    b.OwnsMany("Tebipaints.Domain.Shared.DiscountPolicy", "AppliedDiscounts", b1 =>
                        {
                            b1.Property<Guid>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<string>("Description")
                                .HasColumnType("text")
                                .HasColumnName("description");

                            b1.Property<bool>("IsActive")
                                .HasColumnType("boolean")
                                .HasColumnName("is_active");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("name");

                            b1.Property<decimal>("Percentage")
                                .HasColumnType("numeric")
                                .HasColumnName("percentage");

                            b1.Property<Guid>("SalesOrderId")
                                .HasColumnType("uuid")
                                .HasColumnName("sales_order_id");

                            b1.Property<string>("Type")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("type");

                            b1.Property<DateTime?>("ValidFrom")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("valid_from");

                            b1.Property<DateTime?>("ValidTo")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("valid_to");

                            b1.HasKey("Id")
                                .HasName("pk_sales_order_applied_discounts");

                            b1.HasIndex("SalesOrderId")
                                .HasDatabaseName("ix_sales_order_applied_discounts_sales_order_id");

                            b1.ToTable("SalesOrderAppliedDiscounts", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("SalesOrderId")
                                .HasConstraintName("fk_sales_order_applied_discounts_sales_orders_sales_order_id");
                        });

                    b.OwnsMany("Tebipaints.Domain.SalesOrder.SalesOrderLineItem", "SalesOrderLineItems", b1 =>
                        {
                            b1.Property<Guid>("SalesOrderId")
                                .HasColumnType("uuid")
                                .HasColumnName("sales_order_id");

                            b1.Property<Guid>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<string>("Description")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("description");

                            b1.Property<decimal>("DiscountPercentage")
                                .HasColumnType("numeric")
                                .HasColumnName("discount_percentage");

                            b1.Property<Guid>("ProductId")
                                .HasColumnType("uuid")
                                .HasColumnName("product_id");

                            b1.Property<int>("Quantity")
                                .HasColumnType("integer")
                                .HasColumnName("quantity");

                            b1.HasKey("SalesOrderId", "Id")
                                .HasName("pk_sales_order_line_item");

                            b1.ToTable("sales_order_line_item", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("SalesOrderId")
                                .HasConstraintName("fk_sales_order_line_item_sales_orders_sales_order_id");

                            b1.OwnsMany("Tebipaints.Domain.Shared.DiscountPolicy", "AppliedDiscounts", b2 =>
                                {
                                    b2.Property<Guid>("SalesOrderLineItemSalesOrderId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("sales_order_line_item_sales_order_id");

                                    b2.Property<Guid>("SalesOrderLineItemId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("sales_order_line_item_id");

                                    b2.Property<Guid>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("uuid")
                                        .HasColumnName("id");

                                    b2.Property<string>("Description")
                                        .HasColumnType("text")
                                        .HasColumnName("description");

                                    b2.Property<bool>("IsActive")
                                        .HasColumnType("boolean")
                                        .HasColumnName("is_active");

                                    b2.Property<string>("Name")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("name");

                                    b2.Property<decimal>("Percentage")
                                        .HasColumnType("numeric")
                                        .HasColumnName("percentage");

                                    b2.Property<string>("Type")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("type");

                                    b2.Property<DateTime?>("ValidFrom")
                                        .HasColumnType("timestamp with time zone")
                                        .HasColumnName("valid_from");

                                    b2.Property<DateTime?>("ValidTo")
                                        .HasColumnType("timestamp with time zone")
                                        .HasColumnName("valid_to");

                                    b2.HasKey("SalesOrderLineItemSalesOrderId", "SalesOrderLineItemId", "Id")
                                        .HasName("pk_sales_order_line_item_applied_discounts");

                                    b2.ToTable("sales_order_line_item_applied_discounts", (string)null);

                                    b2.WithOwner()
                                        .HasForeignKey("SalesOrderLineItemSalesOrderId", "SalesOrderLineItemId")
                                        .HasConstraintName("fk_sales_order_line_item_applied_discounts_sales_order_line_it");
                                });

                            b1.OwnsOne("Tebipaints.Domain.Shared.SKU", "Sku", b2 =>
                                {
                                    b2.Property<Guid>("SalesOrderLineItemSalesOrderId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("sales_order_id");

                                    b2.Property<Guid>("SalesOrderLineItemId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("id");

                                    b2.HasKey("SalesOrderLineItemSalesOrderId", "SalesOrderLineItemId");

                                    b2.ToTable("sales_order_line_item");

                                    b2.WithOwner()
                                        .HasForeignKey("SalesOrderLineItemSalesOrderId", "SalesOrderLineItemId")
                                        .HasConstraintName("fk_sales_order_line_item_sales_order_line_item_sales_order_id_");
                                });

                            b1.OwnsOne("Tebipaints.Domain.Shared.Money", "UnitPrice", b2 =>
                                {
                                    b2.Property<Guid>("SalesOrderLineItemSalesOrderId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("sales_order_id");

                                    b2.Property<Guid>("SalesOrderLineItemId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("id");

                                    b2.Property<decimal>("Amount")
                                        .HasColumnType("numeric")
                                        .HasColumnName("unit_price_amount");

                                    b2.Property<string>("Currency")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("unit_price_currency");

                                    b2.HasKey("SalesOrderLineItemSalesOrderId", "SalesOrderLineItemId");

                                    b2.ToTable("sales_order_line_item");

                                    b2.WithOwner()
                                        .HasForeignKey("SalesOrderLineItemSalesOrderId", "SalesOrderLineItemId")
                                        .HasConstraintName("fk_sales_order_line_item_sales_order_line_item_sales_order_id_");
                                });

                            b1.Navigation("AppliedDiscounts");

                            b1.Navigation("Sku")
                                .IsRequired();

                            b1.Navigation("UnitPrice")
                                .IsRequired();
                        });

                    b.Navigation("AppliedDiscounts");

                    b.Navigation("OrderNumber")
                        .IsRequired();

                    b.Navigation("SalesOrderLineItems");
                });

            modelBuilder.Entity("Tebipaints.Domain.Inventory.MaterialInventory", b =>
                {
                    b.HasOne("Tebipaints.Domain.Inventory.Inventory<double>", null)
                        .WithOne()
                        .HasForeignKey("Tebipaints.Domain.Inventory.MaterialInventory", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_material_inventory_inventory_double_id");

                    b.OwnsMany("Tebipaints.Domain.Inventory.MaterialReservation", "Reservations", b1 =>
                        {
                            b1.Property<Guid>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<DateTime>("CreatedAt")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("created_at");

                            b1.Property<Guid>("InventoryId")
                                .HasColumnType("uuid")
                                .HasColumnName("inventory_id");

                            b1.Property<Guid>("MaterialId")
                                .HasColumnType("uuid")
                                .HasColumnName("material_id");

                            b1.Property<string>("Purpose")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("purpose");

                            b1.Property<double>("Quantity")
                                .HasColumnType("double precision")
                                .HasColumnName("quantity");

                            b1.HasKey("Id")
                                .HasName("pk_material_inventory_reservations");

                            b1.HasIndex("InventoryId")
                                .HasDatabaseName("ix_material_inventory_reservations_inventory_id");

                            b1.ToTable("material_inventory_reservations", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("InventoryId")
                                .HasConstraintName("fk_material_inventory_reservations_material_inventory_inventory");
                        });

                    b.Navigation("Reservations");
                });

            modelBuilder.Entity("Tebipaints.Domain.Inventory.ProductInventory", b =>
                {
                    b.HasOne("Tebipaints.Domain.Inventory.Inventory<int>", null)
                        .WithOne()
                        .HasForeignKey("Tebipaints.Domain.Inventory.ProductInventory", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_product_inventories_inventory_int_id");

                    b.OwnsMany("Tebipaints.Domain.Inventory.ProductInventoryTransaction", "VariantTransactions", b1 =>
                        {
                            b1.Property<Guid>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uuid")
                                .HasColumnName("id");

                            b1.Property<Guid>("InventoryId")
                                .HasColumnType("uuid")
                                .HasColumnName("inventory_id");

                            b1.Property<Guid>("VariantId")
                                .HasColumnType("uuid")
                                .HasColumnName("variant_id");

                            b1.HasKey("Id")
                                .HasName("pk_product_variant_transactions");

                            b1.HasIndex("InventoryId")
                                .HasDatabaseName("ix_product_variant_transactions_inventory_id");

                            b1.ToTable("product_variant_transactions", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("InventoryId")
                                .HasConstraintName("fk_product_variant_transactions_product_inventories_inventory_");

                            b1.OwnsOne("Tebipaints.Domain.Inventory.InventoryTransaction<int>", "BaseTransaction", b2 =>
                                {
                                    b2.Property<Guid>("ProductInventoryTransactionId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("id");

                                    b2.Property<int>("Quantity")
                                        .HasColumnType("integer")
                                        .HasColumnName("base_transaction_quantity");

                                    b2.Property<string>("Reference")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("base_transaction_reference");

                                    b2.Property<DateTime>("Timestamp")
                                        .HasColumnType("timestamp with time zone")
                                        .HasColumnName("base_transaction_timestamp");

                                    b2.Property<Guid>("TransactionId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("base_transaction_transaction_id");

                                    b2.Property<string>("Type")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("base_transaction_type");

                                    b2.HasKey("ProductInventoryTransactionId");

                                    b2.ToTable("product_variant_transactions");

                                    b2.WithOwner()
                                        .HasForeignKey("ProductInventoryTransactionId")
                                        .HasConstraintName("fk_product_variant_transactions_product_variant_transactions_id");
                                });

                            b1.Navigation("BaseTransaction")
                                .IsRequired();
                        });

                    b.OwnsMany("Tebipaints.Domain.Inventory.VariantStockLevel", "_variantStockLevels", b1 =>
                        {
                            b1.Property<Guid>("InventoryId")
                                .HasColumnType("uuid")
                                .HasColumnName("inventory_id");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer")
                                .HasColumnName("id");

                            NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b1.Property<int>("Id"));

                            b1.Property<int>("StockLevel")
                                .HasColumnType("integer")
                                .HasColumnName("stock_level");

                            b1.HasKey("InventoryId", "Id")
                                .HasName("pk_product_variant_stock_levels");

                            b1.ToTable("product_variant_stock_levels", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("InventoryId")
                                .HasConstraintName("fk_product_variant_stock_levels_product_inventories_inventory_");

                            b1.OwnsOne("Tebipaints.Domain.Inventory.VariantPackaging", "VariantPackaging", b2 =>
                                {
                                    b2.Property<Guid>("VariantStockLevelInventoryId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("inventory_id");

                                    b2.Property<int>("VariantStockLevelId")
                                        .HasColumnType("integer")
                                        .HasColumnName("id");

                                    b2.Property<string>("Type")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("PackagingType");

                                    b2.Property<Guid>("VariantId")
                                        .HasColumnType("uuid")
                                        .HasColumnName("VariantId");

                                    b2.HasKey("VariantStockLevelInventoryId", "VariantStockLevelId");

                                    b2.ToTable("product_variant_stock_levels");

                                    b2.WithOwner()
                                        .HasForeignKey("VariantStockLevelInventoryId", "VariantStockLevelId")
                                        .HasConstraintName("fk_product_variant_stock_levels_product_variant_stock_levels_i");

                                    b2.OwnsOne("Tebipaints.Domain.Shared.Measurement", "Capacity", b3 =>
                                        {
                                            b3.Property<Guid>("VariantPackagingVariantStockLevelInventoryId")
                                                .HasColumnType("uuid")
                                                .HasColumnName("inventory_id");

                                            b3.Property<int>("VariantPackagingVariantStockLevelId")
                                                .HasColumnType("integer")
                                                .HasColumnName("id");

                                            b3.Property<string>("Unit")
                                                .IsRequired()
                                                .HasColumnType("text")
                                                .HasColumnName("variant_packaging_capacity_unit");

                                            b3.Property<decimal>("Value")
                                                .HasColumnType("numeric")
                                                .HasColumnName("variant_packaging_capacity_value");

                                            b3.HasKey("VariantPackagingVariantStockLevelInventoryId", "VariantPackagingVariantStockLevelId");

                                            b3.ToTable("product_variant_stock_levels");

                                            b3.WithOwner()
                                                .HasForeignKey("VariantPackagingVariantStockLevelInventoryId", "VariantPackagingVariantStockLevelId")
                                                .HasConstraintName("fk_product_variant_stock_levels_product_variant_stock_levels_i");
                                        });

                                    b2.Navigation("Capacity")
                                        .IsRequired();
                                });

                            b1.Navigation("VariantPackaging")
                                .IsRequired();
                        });

                    b.Navigation("VariantTransactions");

                    b.Navigation("_variantStockLevels");
                });

            modelBuilder.Entity("Tebipaints.Domain.Product.Product", b =>
                {
                    b.Navigation("Variants");
                });
#pragma warning restore 612, 618
        }
    }
}
