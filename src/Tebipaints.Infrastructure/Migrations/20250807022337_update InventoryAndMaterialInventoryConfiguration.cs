using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tebipaints.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class updateInventoryAndMaterialInventoryConfiguration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "pk_product_inventory_transactions",
                table: "ProductInventoryTransactions");

            migrationBuilder.AlterColumn<string>(
                name: "location",
                table: "inventory_int",
                type: "text",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AddColumn<int>(
                name: "minimum_stock_level",
                table: "inventory_int",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "reorder_point",
                table: "inventory_int",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AlterColumn<string>(
                name: "location",
                table: "inventory_double",
                type: "text",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AddColumn<double>(
                name: "minimum_stock_level",
                table: "inventory_double",
                type: "double precision",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<double>(
                name: "reorder_point",
                table: "inventory_double",
                type: "double precision",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddPrimaryKey(
                name: "PK_ProductInventoryTransactions",
                table: "ProductInventoryTransactions",
                columns: new[] { "transaction_id", "inventory_int_id" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_ProductInventoryTransactions",
                table: "ProductInventoryTransactions");

            migrationBuilder.DropColumn(
                name: "minimum_stock_level",
                table: "inventory_int");

            migrationBuilder.DropColumn(
                name: "reorder_point",
                table: "inventory_int");

            migrationBuilder.DropColumn(
                name: "minimum_stock_level",
                table: "inventory_double");

            migrationBuilder.DropColumn(
                name: "reorder_point",
                table: "inventory_double");

            migrationBuilder.AlterColumn<int>(
                name: "location",
                table: "inventory_int",
                type: "integer",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<int>(
                name: "location",
                table: "inventory_double",
                type: "integer",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AddPrimaryKey(
                name: "pk_product_inventory_transactions",
                table: "ProductInventoryTransactions",
                columns: new[] { "transaction_id", "inventory_int_id" });
        }
    }
}
