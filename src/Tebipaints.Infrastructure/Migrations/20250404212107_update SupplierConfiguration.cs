using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tebipaints.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class updateSupplierConfiguration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "contact_info_address",
                table: "suppliers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "contact_info_contact_person",
                table: "suppliers",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "contact_info_email",
                table: "suppliers",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "contact_info_phone",
                table: "suppliers",
                type: "text",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "contact_info_address",
                table: "suppliers");

            migrationBuilder.DropColumn(
                name: "contact_info_contact_person",
                table: "suppliers");

            migrationBuilder.DropColumn(
                name: "contact_info_email",
                table: "suppliers");

            migrationBuilder.DropColumn(
                name: "contact_info_phone",
                table: "suppliers");
        }
    }
}
