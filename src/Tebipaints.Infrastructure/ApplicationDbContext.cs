using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Tebipaints.Application.Abstractions.Clock;
using Tebipaints.Application.Exceptions;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Inventory;
using Tebipaints.Domain.Shared;
using Tebipaints.Infrastructure.Outbox;

namespace Tebipaints.Infrastructure;

public sealed class ApplicationDbContext : DbContext, IUnitOfWork
{
    
    private static readonly JsonSerializerSettings JsonSerializerSettings = new()
    {
        TypeNameHandling = TypeNameHandling.All
    };

    private readonly IDateTimeProvider _dateTimeProvider;
    private readonly ILogger<ApplicationDbContext> _logger;
    public ApplicationDbContext(DbContextOptions options, IDateTimeProvider dateTimeProvider, ILogger<ApplicationDbContext> logger) : base(options)
    {
        _dateTimeProvider = dateTimeProvider;
        _logger = logger;
    }
    
    public DbSet<NumberSequence> NumberSequences { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Configure generic InventoryTransaction as owned type
        modelBuilder.Owned<InventoryTransaction<double>>();
        modelBuilder.Owned<InventoryTransaction<int>>();
        
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);
        base.OnModelCreating(modelBuilder);
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        try
        {
            // Convert all DateTime properties to UTC if they are Unspecified
            foreach (var entry in ChangeTracker.Entries())
            {
                foreach (var prop in entry.Properties)
                {
                    if (prop.Metadata.ClrType == typeof(DateTime) && prop.CurrentValue is DateTime dt)
                    {
                        if (dt.Kind == DateTimeKind.Unspecified)
                        {
                            prop.CurrentValue = DateTime.SpecifyKind(dt, DateTimeKind.Utc);
                        }
                    }
                    else if (prop.Metadata.ClrType == typeof(DateTime?) && prop.CurrentValue is DateTime nullableDt)
                    {
                        if (nullableDt.Kind == DateTimeKind.Unspecified)
                        {
                            prop.CurrentValue = DateTime.SpecifyKind(nullableDt, DateTimeKind.Utc);
                        }
                    }
                }
            }

            
            AddDomainEventsAsOutboxMessages();
            var result = await base.SaveChangesAsync(cancellationToken);

            return result;
        }
        catch (DbUpdateConcurrencyException e)
        {
            
            throw new ConcurrencyException("Concurrency exception.", e);
        }
    }
    
    private void AddDomainEventsAsOutboxMessages()
    {
        var outboxMessages = ChangeTracker
            .Entries<Entity>()
            .Select(entry => entry.Entity)
            .SelectMany(entity =>
            {
                var domainEvents = entity.GetDomainEvents();

                entity.ClearDomainEvents();

                return domainEvents;
            })
            .Select(domainEvent => new OutboxMessage(
                Guid.NewGuid(),
                _dateTimeProvider.UtcNow,
                domainEvent.GetType().Name,
                JsonConvert.SerializeObject(domainEvent, JsonSerializerSettings)))
            .ToList();

        AddRange(outboxMessages);
    }

}