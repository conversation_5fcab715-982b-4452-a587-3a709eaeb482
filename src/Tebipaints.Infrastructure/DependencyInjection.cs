using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Quartz;
using Tebipaints.Application.Abstractions.Clock;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Application.Abstractions.Reporting;
using Tebipaints.Application.Formulation.Services;
using Tebipaints.Domain.Abstractions;
using Tebipaints.Domain.Formulation;
using Tebipaints.Domain.Formulation.Services;
using Tebipaints.Domain.Inventory;
using Tebipaints.Domain.Invoice;
using Tebipaints.Domain.Material;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.Production.Repositories;
using Tebipaints.Domain.SalesOrder;
using Tebipaints.Domain.Shared;
using Tebipaints.Infrastructure.Clock;
using Tebipaints.Infrastructure.Data;
using Tebipaints.Infrastructure.Formulation;
using Tebipaints.Infrastructure.Generators;
using Tebipaints.Infrastructure.Outbox;
using Tebipaints.Infrastructure.Repositories;

namespace Tebipaints.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration
            .GetConnectionString("Database") ?? throw new ArgumentNullException(nameof(configuration));

        services.AddDbContext<ApplicationDbContext>(options =>
        {
            options.UseNpgsql(connectionString).UseSnakeCaseNamingConvention();
        });

        services.AddTransient<IDateTimeProvider, DateTimeProvider>();

        services.AddScoped<INumberSequenceRepository, NumberSequenceRepository>();

        services.AddScoped<IDocumentNumberGenerator, DocumentNumberGenerator>();

        services.AddScoped<IPurchaseOrderRepository, PurchaseOrderRepository>();

        services.AddScoped<ISupplierRepository, SupplierRepository>();

        services.AddScoped<IGoodsReceiptRepository, GoodsReceiptRepository>();

        services.AddScoped<IPurchaseInvoiceRepository, PurchaseInvoiceRepository>();

        services.AddScoped<IMaterialRepository, MaterialRepository>();

        services.AddScoped<IProductionLineRepository, ProductionLineRepository>();

        services.AddScoped<IProductRepository, ProductRepository>();

        services.AddScoped<IWorkOrderRepository, WorkOrderRepository>();

        services.AddScoped<IProductInventoryRepository, ProductInventoryRepository>();

        services.AddScoped<ISalesOrderRepository, SalesOrderRepository>();

        services.AddScoped<IBatchRepository, BatchRepository>();

        services.AddScoped<IFormulationRepository, FormulationRepository>();

        // Formulation domain services
        services.AddScoped<IFormulationCostCalculationService, FormulationCostCalculationService>();
        services.AddScoped<IFormulationEfficiencyService, FormulationEfficiencyService>();
        services.AddSingleton<CostCalculationSettings>();

        services.AddScoped<IDiscountPolicyRepository, DiscountPolicyRepository>();

        services.AddScoped<IInvoiceRepository, InvoiceRepository>();

        services.AddScoped<IMaterialInventoryRepository, MaterialInventoryRepository>();

        services.AddScoped<IUnitOfWork>(sp => sp.GetRequiredService<ApplicationDbContext>());

        services.AddSingleton<ISqlConnectionFactory>(_ => new SqlConnectionFactory(connectionString));

        services.Configure<PuppeteerSettings>(
            configuration.GetSection("Puppeteer"));

        services.AddScoped<IPdfGenerator, PdfGenerator>();

        services.AddRazorTemplating();

        AddBackgroundJobs(services, configuration);


        return services;
    }

    private static void AddBackgroundJobs(IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<OutboxOptions>(configuration.GetSection("Outbox"));

        services.AddQuartz();

        services.AddQuartzHostedService(options => options.WaitForJobsToComplete = true);

        services.ConfigureOptions<ProcessOutboxMessagesJobSetup>();
    }
}