using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Infrastructure.Configurations;

internal sealed class GoodsReceiptConfiguration : IEntityTypeConfiguration<GoodsReceipt>
{
    public void Configure(EntityTypeBuilder<GoodsReceipt> builder)
    {
        builder.ToTable("goods_receipts");
        
        builder.HasKey(gr => gr.Id);

        builder.Property(gr => gr.PurchaseOrderId)
            .IsRequired();

        // Configure foreign key relationship to PurchaseOrder
        builder.HasOne<PurchaseOrder>()
            .WithMany()
            .HasForeignKey(gr => gr.PurchaseOrderId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(gr => gr.PurchaseOrderNumber)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(gr => gr.SupplierId)
            .IsRequired();

        // Configure foreign key relationship to Supplier
        builder.HasOne<Supplier>()
            .WithMany()
            .HasForeignKey(gr => gr.SupplierId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(gr => gr.ReceivedDate)
            .IsRequired();

        builder.Property(gr => gr.ReceivedBy)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(gr => gr.DeliveryNote)
            .HasMaxLength(200);

        builder.Property(gr => gr.SupplierReference)
            .HasMaxLength(100);

        builder.Property(gr => gr.Status)
            .HasConversion<string>()
            .IsRequired();

        // Configure the GoodsReceiptNumber value object
        builder.OwnsOne(gr => gr.ReceiptNumber, rnb =>
        {
            rnb.Property(rn => rn.Value)
                .HasColumnName("receipt_number")
                .HasMaxLength(50)
                .IsRequired();
        });

        // Configure the Items collection
        builder.OwnsMany(gr => gr.Items, ib =>
        {
            ib.ToTable("goods_receipt_items");
            
            ib.WithOwner().HasForeignKey("GoodsReceiptId");
            
            ib.Property<Guid>("Id");
            ib.HasKey("Id");

            ib.Property(i => i.MaterialId)
                .IsRequired();

            ib.Property(i => i.MaterialName)
                .HasMaxLength(200)
                .IsRequired();

            ib.Property(i => i.Notes)
                .HasMaxLength(500);

            ib.Property(i => i.QualityStatus)
                .HasConversion<string>()
                .IsRequired();

            // Configure OrderedQuantity
            ib.OwnsOne(i => i.OrderedQuantity, oqb =>
            {
                oqb.Property(m => m.Value)
                    .HasColumnName("ordered_quantity_value")
                    .HasPrecision(18, 4)
                    .IsRequired();
                    
                oqb.Property(m => m.Unit)
                    .HasColumnName("ordered_quantity_unit")
                    .HasConversion(
                        u => u.Symbol,
                        s => SafeConvertToUnitOfMeasure(s))
                    .HasMaxLength(10)
                    .IsRequired();
            });

            // Configure ReceivedQuantity
            ib.OwnsOne(i => i.ReceivedQuantity, rqb =>
            {
                rqb.Property(m => m.Value)
                    .HasColumnName("received_quantity_value")
                    .HasPrecision(18, 4)
                    .IsRequired();
                    
                rqb.Property(m => m.Unit)
                    .HasColumnName("received_quantity_unit")
                    .HasConversion(
                        u => u.Symbol,
                        s => SafeConvertToUnitOfMeasure(s))
                    .HasMaxLength(10)
                    .IsRequired();
            });

            // Configure UnitPrice
            ib.OwnsOne(i => i.UnitPrice, upb =>
            {
                upb.Property(m => m.Amount)
                    .HasColumnName("unit_price_amount")
                    .HasPrecision(18, 4)
                    .IsRequired();

                upb.Property(m => m.Currency)
                    .HasColumnName("unit_price_currency")
                    .HasConversion(c => c.Code, code => Currency.FromCode(code))
                    .HasMaxLength(3)
                    .IsRequired();
            });
        });

        // Add indexes
        builder.HasIndex(gr => gr.PurchaseOrderId)
            .HasDatabaseName("IX_goods_receipts_purchase_order_id");

        builder.HasIndex(gr => gr.SupplierId)
            .HasDatabaseName("IX_goods_receipts_supplier_id");

        builder.HasIndex(gr => gr.ReceivedDate)
            .HasDatabaseName("IX_goods_receipts_received_date");

        builder.OwnsOne(gr => gr.ReceiptNumber, rnb =>
        {
            rnb.HasIndex(rn => rn.Value)
                .IsUnique()
                .HasDatabaseName("IX_goods_receipts_receipt_number");
        });
    }

    private static UnitOfMeasure SafeConvertToUnitOfMeasure(string symbol)
    {
        var result = UnitOfMeasureConverter.FromString(symbol);
        return result.IsSuccess ? result.Value : UnitOfMeasure.Litre; // Default fallback
    }
}
