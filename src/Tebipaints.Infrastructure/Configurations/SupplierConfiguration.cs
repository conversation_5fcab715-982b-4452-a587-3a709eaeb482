using System.Diagnostics.SymbolStore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Infrastructure.Configurations;

internal sealed class SupplierConfiguration : IEntityTypeConfiguration<Supplier>
{
    public void Configure(EntityTypeBuilder<Supplier> builder)
    {
        builder.ToTable("suppliers");
        
        builder.HasKey(supplier => supplier.Id);

        builder.OwnsOne(supplier => supplier.ContactInfo, cib =>
        {
            cib.Property(c => c.ContactPerson);
            cib.Property(c => c.Email);
            cib.Property(c => c.Phone);
            cib.Property(c => c.Address);
        });

        builder.Property(supplier => supplier.Name);

        builder.Property(supplier => supplier.Code);

        builder.Property(supplier => supplier.Status).HasConversion<string>();

        builder.HasMany(s => s.Contracts)
            .WithOne() // inverse nav, `.WithOne(c => c.Supplier)`
            .HasForeignKey("SupplierId")
            .OnDelete(DeleteBehavior.Cascade);
    }
}