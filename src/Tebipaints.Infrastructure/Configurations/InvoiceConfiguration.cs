using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tebipaints.Domain.Invoice;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Infrastructure.Configurations;

internal sealed class InvoiceConfiguration : IEntityTypeConfiguration<Invoice>
{
    public void Configure(EntityTypeBuilder<Invoice> builder)
    {
        builder.ToTable("invoices");
        
        builder.<PERSON><PERSON>ey(i => i.Id);

        builder.Property(i => i.CustomerId);

        builder.Property(i => i.WalkInCustomerName);

        builder.OwnsOne(i => i.InvoiceNumber, ib =>
        {
            ib.Property(n => n.Value);
        });
        
        builder.OwnsOne(n => n.Subtotal, st =>
        {
            st.Property(m => m.Currency)
                .HasConversion(c => c.Code, code => Currency.FromCode(code));

            st.Property(m => m.Amount);
        });
        
        builder.OwnsOne(n => n.Tax, tb =>
        {
            tb.Property(m => m.Currency)
                .HasConversion(c => c.Code, code => Currency.FromCode(code));

            tb.Property(m => m.Amount);
        });
        
        builder.OwnsOne(n => n.GrandTotal, gb =>
        {
            gb.Property(m => m.Currency)
                .HasConversion(c => c.Code, code => Currency.FromCode(code));

            gb.Property(m => m.Amount);
        });
        
        builder.OwnsOne(n => n.AmountPaid, ab =>
        {
            ab.Property(m => m.Currency)
                .HasConversion(c => c.Code, code => Currency.FromCode(code));

            ab.Property(m => m.Amount);
        });
        
        builder.OwnsOne(n => n.AmountDue, ad =>
        {
            ad.Property(m => m.Currency)
                .HasConversion(c => c.Code, code => Currency.FromCode(code));

            ad.Property(m => m.Amount);
        });

        builder.Property(n => n.DueDate);

        builder.Property(n => n.CreatedOnUtc);

        builder.Property(n => n.Status).HasConversion<string>();

        builder.OwnsMany(n => n.LineItems, lb =>
        {
            lb.ToTable("invoice_lines");

            lb.HasKey(l => l.Id);

            lb.WithOwner().HasForeignKey("InvoiceId");

            lb.Property(l => l.ProductId);

            lb.Property(l => l.Description);

            lb.OwnsOne(l => l.Sku, sb =>
            {
                sb.Property(s => s.Value);
            });

            lb.OwnsOne(l => l.UnitPrice, ub =>
            {
                ub.Property(m => m.Currency)
                    .HasConversion(c => c.Code, code => Currency.FromCode(code));

                ub.Property(m => m.Amount);
            });

            lb.OwnsOne(l => l.LineItemDiscount, ub =>
            {
                ub.Property(m => m.Currency)
                    .HasConversion(c => c.Code, code => Currency.FromCode(code));

                ub.Property(m => m.Amount);
            });

            lb.Property(l => l.Quantity);

        });

        builder.OwnsMany(n => n.Payments, pb =>
        {
            pb.ToTable("invoice_payments");
            
            pb.HasKey(p => p.Id);
            
            pb.WithOwner().HasForeignKey("InvoiceId");

            pb.Property(p => p.Reference);

            pb.OwnsOne(p => p.Amount, ab =>
            {
                ab.Property(m => m.Currency)
                    .HasConversion(c => c.Code, code => Currency.FromCode(code));

                ab.Property(m => m.Amount);
            });

            pb.Property(p => p.Date);

            pb.Property(p => p.PaymentMethod).HasConversion<string>();

            pb.OwnsOne(p => p.Receipt, rb =>
            {
                rb.Property(r => r.InvoiceId);
                rb.Property(r => r.PaymentId);
                rb.Property(r => r.IssuedDate);
                rb.OwnsOne(r => r.Amount, ab =>
                {
                    ab.Property(m => m.Currency)
                        .HasConversion(c => c.Code, code => Currency.FromCode(code));

                    ab.Property(m => m.Amount);
                });

                rb.OwnsOne(r => r.Number, rn =>
                    { rn.Property(n => n.Value); });

            });
        });

        builder.OwnsMany(n => n.Refunds, rb =>
        {
            rb.ToTable("invoice_refunds");
            rb.HasKey(r => r.Id);
            rb.WithOwner().HasForeignKey("InvoiceId");
            
            rb.Property(r => r.InvoiceId);

            rb.Property(r => r.Reason);

            rb.Property(r => r.RefundDate);

            rb.Property(r => r.Status);

            rb.Property(r => r.ProcessedDate);

            rb.OwnsOne(r => r.Amount, ab =>
            {
                ab.Property(a => a.Amount);
                
                ab.Property(a => a.Currency)
                    .HasConversion(
                        c => c.Code, code => Currency.FromCode(code));
            });
        });
    }
}