using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Infrastructure.Configurations;

internal sealed class ProductVariantConfiguration : IEntityTypeConfiguration<ProductVariant>
{
    public void Configure(EntityTypeBuilder<ProductVariant> builder)
    {
        builder.ToTable("product_variants");
        
        // Configure primary key
        builder.HasKey(x => x.Id);
        
        // Configure relationship with Product
        builder.HasOne<Domain.Product.Product>()
            .WithMany("Variants")
            .HasForeignKey("ProductId")
            .IsRequired();

        builder.Property(pv => pv.ProductId);

        builder.OwnsOne(pv => pv.Sku, sb =>
        {
            sb.Property(s => s.Value);
            
            // Create unique index here, where we have access to the owned type configuration
            sb.HasIndex(s => s.Value)
                .IsUnique();
        });

        builder.OwnsOne(pv => pv.Volume, vb =>
        {
            vb.Property(v => v.Value);
            vb.Property(v => v.Unit)
                .HasConversion(
                    s => s.Unit,
                    u => VolumetricUnit.FromUnit(u));
        });

        builder.OwnsOne(pv => pv.Color);

        builder.Property(pv => pv.Status)
            .HasConversion<string>();

        // Configure PackagingOptions as owned entity collection
        builder.OwnsMany(pv => pv.PackagingOptions, po =>
        {
            po.ToTable("product_variant_packaging_options");
            
            po.WithOwner().HasForeignKey("ProductVariantId");
            
            po.Property<int>("Id").ValueGeneratedOnAdd();
            po.HasKey("Id");

            po.Property(p => p.Type)
                .HasConversion<string>()
                .IsRequired();

            po.Property(p => p.Material)
                .IsRequired();

            po.OwnsOne(p => p.Capacity, cb =>
            {
                cb.Property(c => c.Value).IsRequired();
                cb.Property(c => c.Unit)
                    .HasConversion(
                        s => s.Symbol,
                        u => UnitOfMeasureConverter.FromString(u).Value)
                    .IsRequired();
            });

            po.OwnsOne(p => p.Price, pb =>
            {
                pb.Property(m => m.Currency)
                    .HasConversion(c => c.Code, code => Currency.FromCode(code))
                    .IsRequired();

                pb.Property(m => m.Amount).IsRequired();
            });

            po.Property(p => p.Status).HasConversion<string>();
        });

    }
}