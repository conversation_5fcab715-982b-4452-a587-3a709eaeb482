using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tebipaints.Domain.Material;

namespace Tebipaints.Infrastructure.Configurations;

internal sealed class MaterialConfiguration : IEntityTypeConfiguration<Material>
{
    public void Configure(EntityTypeBuilder<Material> builder)
    {
        builder.ToTable("materials");
        
        builder.HasKey(m => m.Id);

        builder.Property(m => m.Code);
        
        builder.Property(m => m.Name);

        builder.Property(m => m.Description);

        builder.Property(m => m.DefaultUnit);

        builder.Property(m => m.Type).HasConversion<string>();
        
        builder.Property(m => m.Status).HasConversion<string>();
    }
}