using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tebipaints.Domain.Production;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Infrastructure.Configurations;

internal sealed class WorkOrderConfiguration : IEntityTypeConfiguration<WorkOrder>
{
    public void Configure(EntityTypeBuilder<WorkOrder> builder)
    {
        builder.ToTable("work_orders");

        builder.HasKey(w => w.Id);

        builder.Property(w => w.WorkOrderNumber);

        builder.Property(w => w.ProductId);

        builder.Property(w => w.VariantId);

        builder.Property(w => w.PackagingType).HasConversion<string>();

        builder.OwnsOne(w => w.PackagingCapacity, pb =>
        {
            pb.Property(m => m.Value);
            pb.Property(w => w.Unit)
                .HasConversion(
                    u => u.Symbol,
                    s => UnitOfMeasureConverter.FromString(s).Value);
        });

        builder.Property(w => w.FormulationId);

        builder.OwnsOne(w => w.TotalQuantity, tb =>
        {
            tb.Property(m => m.Value);
            tb.Property(w => w.Unit)
                .HasConversion(
                    u => u.Symbol,
                    s => UnitOfMeasureConverter.FromString(s).Value);
        });

        builder.Property(w => w.StartDate);

        builder.Property(w => w.DueDate);

        builder.Property(w => w.Status).HasConversion<string>();

        builder.OwnsMany(w => w.Batches, bb =>
        {
            bb.ToTable("work_order_batches");

            bb.WithOwner().HasForeignKey("WorkOrderId");

            bb.HasKey(b => b.Id);

            bb.Property(b => b.BatchNumber);

            bb.Property(b => b.FormulationId);

            bb.OwnsOne(b => b.TargetQuantity, qb =>
            {
                qb.Property(m => m.Value);
                qb.Property(m => m.Unit)
                    .HasConversion(
                        u => u.Symbol,
                        s => UnitOfMeasureConverter.FromString(s).Value);
            });

            bb.Property(b => b.Status).HasConversion<string>();

            bb.Property(b => b.PlannedStartDate);

            bb.Property(b => b.ActualStartDate);

            bb.Property(b => b.CompletionDate);

            bb.Property(b => b.EstimatedDuration);

            bb.Property(b => b.ActualDuration);

            bb.Property(b => b.ActualYield);

            bb.Property(b => b.Notes);

            bb.OwnsMany(b => b.Ingredients, ing =>
            {
                ing.ToTable("batch_ingredients");
                ing.WithOwner().HasForeignKey("BatchId");

                ing.Property<int>("Id").ValueGeneratedOnAdd();
                ing.HasKey("Id");

                ing.Property(i => i.MaterialId).IsRequired();

                ing.OwnsOne(i => i.RequiredQuantity, rb =>
                {
                    rb.Property(m => m.Value);
                    rb.Property(m => m.Unit)
                        .HasConversion(
                            u => u.Symbol,
                            s => UnitOfMeasureConverter.FromString(s).Value);
                });

                ing.Property(i => i.ActualQuantity);
            });

            bb.OwnsMany(b => b.QualityChecks, qb =>
            {
                qb.ToTable("batch_quality_checks");
                qb.WithOwner().HasForeignKey("BatchId");

                qb.Property<int>("Id").ValueGeneratedOnAdd();
                qb.HasKey("Id");

                qb.Property(q => q.Parameter);
                qb.Property(q => q.MeasuredValue);
                qb.Property(q => q.MinimumValue);
                qb.Property(q => q.MaximumValue);
                qb.Property(q => q.CheckedBy);
                qb.Property(q => q.CheckedAt);
            });

            bb.OwnsMany(b => b.ProductionLosses, lb =>
            {
                lb.ToTable("batch_production_losses");
                lb.WithOwner().HasForeignKey("BatchId");

                lb.Property<int>("Id").ValueGeneratedOnAdd();
                lb.HasKey("Id");

                lb.Property(q => q.Quantity);
                lb.Property(q => q.LossType).HasConversion<string>();
                lb.Property(q => q.Reason);
                lb.Property(q => q.RecordedAt);
            });
        });

        builder.OwnsMany(w => w.BillOfMaterials, bomb =>
        {
            bomb.ToTable("bill_of_materials");
            bomb.WithOwner().HasForeignKey("WorkOrderId");

            bomb.Property<int>("Id").ValueGeneratedOnAdd();
            bomb.HasKey("Id");

            bomb.Property(bom => bom.MaterialId);
            bomb.OwnsOne(bom => bom.RequiredQuantity, qb =>
            {
                qb.Property(m => m.Value);
                qb.Property(m => m.Unit)
                    .HasConversion(
                        u => u.Symbol,
                        s => UnitOfMeasureConverter.FromString(s).Value);
            });
        });
    }
}