using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Infrastructure.Configurations;

internal sealed class PurchaseInvoiceConfiguration : IEntityTypeConfiguration<PurchaseInvoice>
{
    public void Configure(EntityTypeBuilder<PurchaseInvoice> builder)
    {
        builder.ToTable("purchase_invoices");
        
        builder.HasKey(pi => pi.Id);

        builder.Property(pi => pi.SupplierInvoiceNumber)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(pi => pi.PurchaseOrderId)
            .IsRequired();

        // Configure foreign key relationship to PurchaseOrder
        builder.HasOne<PurchaseOrder>()
            .WithMany()
            .HasForeignKey(pi => pi.PurchaseOrderId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(pi => pi.PurchaseOrderNumber)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(pi => pi.SupplierId)
            .IsRequired();

        // Configure foreign key relationship to Supplier
        builder.HasOne<Supplier>()
            .WithMany()
            .HasForeignKey(pi => pi.SupplierId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(pi => pi.InvoiceDate)
            .IsRequired();

        builder.Property(pi => pi.DueDate)
            .IsRequired();

        builder.Property(pi => pi.Notes)
            .HasMaxLength(1000);

        builder.Property(pi => pi.Status)
            .HasConversion<string>()
            .IsRequired();

        builder.Property(pi => pi.ApprovedDate);

        builder.Property(pi => pi.ApprovedBy)
            .HasMaxLength(100);

        builder.Property(pi => pi.RejectedDate);

        builder.Property(pi => pi.RejectedBy)
            .HasMaxLength(100);

        builder.Property(pi => pi.RejectionReason)
            .HasMaxLength(500);

        builder.Property(pi => pi.LastMatchingDate);

        builder.Property(pi => pi.LastMatchingResult);

        builder.Property(pi => pi.LastMatchingVariancePercentage)
            .HasPrecision(5, 2);

        builder.Property(pi => pi.MatchingNotes)
            .HasMaxLength(1000);

        // Configure TotalAmount
        builder.OwnsOne(pi => pi.TotalAmount, tab =>
        {
            tab.Property(m => m.Amount)
                .HasColumnName("total_amount")
                .HasPrecision(18, 4)
                .IsRequired();

            tab.Property(m => m.Currency)
                .HasColumnName("total_currency")
                .HasConversion(c => c.Code, code => Currency.FromCode(code))
                .HasMaxLength(3)
                .IsRequired();
        });

        // Configure the Items collection
        builder.OwnsMany(pi => pi.Items, ib =>
        {
            ib.ToTable("purchase_invoice_items");
            
            ib.WithOwner().HasForeignKey("PurchaseInvoiceId");
            
            ib.Property<Guid>("Id");
            ib.HasKey("Id");

            ib.Property(i => i.MaterialId)
                .IsRequired();

            ib.Property(i => i.MaterialName)
                .HasMaxLength(200)
                .IsRequired();

            ib.Property(i => i.Notes)
                .HasMaxLength(500);

            // Configure InvoicedQuantity
            ib.OwnsOne(i => i.InvoicedQuantity, iqb =>
            {
                iqb.Property(m => m.Value)
                    .HasColumnName("invoiced_quantity_value")
                    .HasPrecision(18, 4)
                    .IsRequired();
                    
                iqb.Property(m => m.Unit)
                    .HasColumnName("invoiced_quantity_unit")
                    .HasConversion(
                        u => u.Symbol,
                        s => UnitOfMeasureConverter.FromString(s).IsSuccess
                            ? UnitOfMeasureConverter.FromString(s).Value
                            : UnitOfMeasure.Litre) // Default fallback
                    .HasMaxLength(10)
                    .IsRequired();
            });

            // Configure UnitPrice
            ib.OwnsOne(i => i.UnitPrice, upb =>
            {
                upb.Property(m => m.Amount)
                    .HasColumnName("unit_price_amount")
                    .HasPrecision(18, 4)
                    .IsRequired();

                upb.Property(m => m.Currency)
                    .HasColumnName("unit_price_currency")
                    .HasConversion(c => c.Code, code => Currency.FromCode(code))
                    .HasMaxLength(3)
                    .IsRequired();
            });
        });

        // Add indexes
        builder.HasIndex(pi => pi.PurchaseOrderId)
            .HasDatabaseName("IX_purchase_invoices_purchase_order_id");

        builder.HasIndex(pi => pi.SupplierId)
            .HasDatabaseName("IX_purchase_invoices_supplier_id");

        builder.HasIndex(pi => pi.InvoiceDate)
            .HasDatabaseName("IX_purchase_invoices_invoice_date");

        builder.HasIndex(pi => pi.DueDate)
            .HasDatabaseName("IX_purchase_invoices_due_date");

        builder.HasIndex(pi => pi.Status)
            .HasDatabaseName("IX_purchase_invoices_status");

        // Unique constraint for supplier invoice number per supplier
        builder.HasIndex(pi => new { pi.SupplierInvoiceNumber, pi.SupplierId })
            .IsUnique()
            .HasDatabaseName("IX_purchase_invoices_supplier_invoice_number_supplier_id");
    }
}
