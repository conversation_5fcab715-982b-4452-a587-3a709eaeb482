using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tebipaints.Domain.Inventory;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Infrastructure.Configurations;

public class ProductInventoryConfiguration : IEntityTypeConfiguration<ProductInventory>
{
    public void Configure(EntityTypeBuilder<ProductInventory> builder)
    {
        builder.ToTable("product_inventories");

        // Configure inheritance
        builder.HasBaseType<Inventory<int>>();

        // Configure ProductId reference
        builder.Property(x => x.ProductId)
            .IsRequired();

        builder.HasIndex(x => x.ProductId);

        // Transactions are configured in the base Inventory<int> configuration

        builder.OwnsMany<VariantStockLevel>("_variantStockLevels", vsl =>
        {
            vsl.ToTable("product_variant_stock_levels");
            
            vsl.WithOwner().HasForeignKey("InventoryId");

            // Configure stock level
            vsl.Property(x => x.StockLevel)
                .IsRequired();

            // Configure the owned VariantPackaging
            vsl.OwnsOne(x => x.VariantPackaging, vp =>
            {
                vp.Property(x => x.VariantId)
                    .IsRequired();
                    
                vp.Property(x => x.Type)
                    .HasConversion<string>()
                    .IsRequired();
                    
                vp.OwnsOne(x => x.Capacity, c =>
                {
                    c.Property(x => x.Value)
                        .IsRequired();
                        
                    c.Property(x => x.Unit)
                        .HasConversion(
                            unit => unit.Symbol,
                            symbol => UnitOfMeasure.All.First(u => u.Symbol == symbol))
                        .IsRequired();
                });
            });
        });

        builder.OwnsMany(p => p.VariantTransactions, vb =>
        {
            vb.ToTable("product_variant_transactions");
            vb.WithOwner().HasForeignKey("InventoryId");

            vb.Property<Guid>("Id").ValueGeneratedOnAdd();
            vb.HasKey("Id");

            vb.Property(v => v.VariantId).IsRequired();

            vb.OwnsOne(v => v.BaseTransaction, btb =>
            {
                btb.Property(b => b.TransactionId);

                btb.Property(b => b.Type)
                    .HasConversion<string>();
                
                btb.Property(x => x.Quantity);
                btb.Property(x => x.Reference);
                btb.Property(x => x.Timestamp);
            });
        });
    }
}