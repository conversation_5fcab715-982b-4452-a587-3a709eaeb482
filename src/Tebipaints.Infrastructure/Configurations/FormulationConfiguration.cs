using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Tebipaints.Domain.Formulation;
using Tebipaints.Domain.Shared;
using Version = Tebipaints.Domain.Formulation.Version;

namespace Tebipaints.Infrastructure.Configurations;

internal sealed class FormulationConfiguration : IEntityTypeConfiguration<Domain.Formulation.Formulation> 
{
    public void Configure(EntityTypeBuilder<Domain.Formulation.Formulation> builder)
    {
        builder.ToTable("formulations");

        builder.HasKey(f => f.Id);
        builder.Property(f => f.Name).HasMaxLength(100).IsRequired();
        builder.Property(f => f.Description).HasMaxLength(200);
        builder.Property(f => f.ShelfLife).IsRequired();
        builder.Property(f => f.EstimatedProductionTime).IsRequired();
        builder.Property(f => f.Status).HasConversion<string>();

        // Configure EstimatedCost as owned entity
        builder.OwnsOne(f => f.EstimatedCost, cb =>
        {
            cb.Property(c => c.CalculatedAt).HasColumnName("cost_calculated_at");
            cb.Property(c => c.CalculationBasis).HasColumnName("cost_calculation_basis").HasMaxLength(500);

            cb.OwnsOne(c => c.MaterialCost, mb =>
            {
                mb.Property(m => m.Amount).HasColumnName("material_cost_amount").HasPrecision(18, 2);
                mb.Property(m => m.Currency).HasColumnName("material_cost_currency")
                    .HasConversion(
                        c => c.Code,
                        code => Currency.FromCode(code))
                    .HasMaxLength(3);
            });

            cb.OwnsOne(c => c.LaborCost, lb =>
            {
                lb.Property(l => l.Amount).HasColumnName("labor_cost_amount").HasPrecision(18, 2);
                lb.Property(l => l.Currency).HasColumnName("labor_cost_currency")
                    .HasConversion(
                        c => c.Code,
                        code => Currency.FromCode(code))
                    .HasMaxLength(3);
            });

            cb.OwnsOne(c => c.OverheadCost, ob =>
            {
                ob.Property(o => o.Amount).HasColumnName("overhead_cost_amount").HasPrecision(18, 2);
                ob.Property(o => o.Currency).HasColumnName("overhead_cost_currency")
                    .HasConversion(
                        c => c.Code,
                        code => Currency.FromCode(code))
                    .HasMaxLength(3);
            });

            cb.OwnsOne(c => c.TotalCost, tb =>
            {
                tb.Property(t => t.Amount).HasColumnName("total_cost_amount").HasPrecision(18, 2);
                tb.Property(t => t.Currency).HasColumnName("total_cost_currency")
                    .HasConversion(
                        c => c.Code,
                        code => Currency.FromCode(code))
                    .HasMaxLength(3);
            });

            cb.OwnsOne(c => c.CostPerUnit, ub =>
            {
                ub.Property(u => u.Amount).HasColumnName("cost_per_unit_amount").HasPrecision(18, 2);
                ub.Property(u => u.Currency).HasColumnName("cost_per_unit_currency")
                    .HasConversion(
                        c => c.Code,
                        code => Currency.FromCode(code))
                    .HasMaxLength(3);
            });
        });

        builder.OwnsMany(f => f.Ingredients, fib =>
        {
            fib.ToTable("ingredients");
            fib.WithOwner().HasForeignKey("formulation_id");
            fib.HasKey(i => i.Id);
            fib.Property(i => i.MaterialId);
            fib.OwnsOne(i => i.Quantity, qb =>
            {
                qb.Property(m => m.Value);
                qb.Property(m => m.Unit)
                    .HasConversion(
                        u => u.Symbol,
                        s => UnitOfMeasureConverter.FromString(s).Value);
            });
        });

        builder.OwnsMany(f => f.Versions, vb =>
        {
            vb.ToTable("versions");
            vb.WithOwner().HasForeignKey("formulation_id");
            vb.HasKey(i => i.Id);

            vb.OwnsOne(v => v.Snapshot, sb =>
            {
                sb.OwnsOne(s => s.MinimumProductionQuantity, qb =>
                {
                    qb.Property(m => m.Value);
                    qb.Property(m => m.Unit)
                        .HasConversion(
                            u => u.Symbol,
                            s => UnitOfMeasureConverter.FromString(s).Value);
                });

                sb.Property(s => s.EstimatedProductionTime);
                sb.Property(s => s.ShelfLife);

                sb.OwnsMany(s => s.Ingredients, ib =>
                {
                    ib.Property(i => i.IngredientId);
                    ib.Property(i => i.RawMaterialId);
                    ib.OwnsOne(i => i.Quantity, qb =>
                    {
                        qb.Property(m => m.Value);
                        qb.Property(m => m.Unit)
                            .HasConversion(
                                u => u.Symbol,
                                s => UnitOfMeasureConverter.FromString(s).Value);
                    });
                });

                var converter = new ValueConverter<IReadOnlyList<string>, string>(
                    v => string.Join(";", v), // To database
                    v => v.Split(";", StringSplitOptions.None).ToList() // From database
                );

                sb.Property(s => s.Instructions)
                    .HasConversion(converter);
            });

            vb.OwnsOne(v => v.ChangeLog, clb =>
            {
                clb.ToTable("change_logs");
                clb.WithOwner().HasForeignKey("version_id");
                clb.HasKey(c => c.Id);
                

                clb.OwnsMany(c => c.Entries, eb =>
                {
                    eb.Property(e => e.TimeStamp);
                    eb.Property(e => e.Description);
                    eb.Property(e => e.Author);
                });
            });
        });
    }
}