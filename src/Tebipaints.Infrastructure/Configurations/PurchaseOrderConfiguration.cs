using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Infrastructure.Configurations;

internal sealed class PurchaseOrderConfiguration : IEntityTypeConfiguration<PurchaseOrder>
{
    public void Configure(EntityTypeBuilder<PurchaseOrder> builder)
    {
        builder.ToTable("purchase_orders");
        
        builder.HasKey(p => p.Id);

        builder.Property(p => p.SupplierId);

        builder.Property(p => p.OrderDate);

        builder.Property(p => p.RequestedBy);

        builder.Property(p => p.ApprovedDate);
        
        builder.Property(p => p.ApprovedBy);

        builder.Property(p => p.PromiseDate);

        builder.Property(p => p.DiscountPercentage);

        builder.OwnsOne(p => p.FreightCharge, fb =>
        {
            fb.Property(f => f.Amount);

            fb.Property(f => f.Currency)
                .HasConversion(c => c.Code, code => Currency.FromCode(code));
        });

        builder.Property(p => p.TaxRate);

        builder.Property(p => p.OrderCurrency)
            .HasConversion(c => c.Code, code => Currency.FromCode(code));
        
        builder.Property(p => p.Status).HasConversion<string>();

        builder.OwnsOne(p => p.OrderNumber, onb =>
        {
            onb.Property(p => p.Value);
        });

        builder.OwnsMany(p => p.Items, ib =>
        {
            ib.ToTable("purchase_order_items");

            ib.WithOwner().HasForeignKey("PurchaseOrderId");

            ib.Property<Guid>("Id");
            ib.HasKey("Id");

            ib.Property(i => i.MaterialId);

            ib.Property(i => i.MaterialName);

            ib.OwnsOne(i => i.Quantity, qb =>
            {
                qb.Property(m => m.Value)
                    .HasColumnName("quantity_value");
                qb.Property(m => m.Unit)
                    .HasColumnName("quantity_unit")
                    .HasConversion(
                        u => u.Symbol,
                        s => UnitOfMeasureConverter.FromString(s).Value);
            });

            ib.OwnsOne(i => i.UnitPrice, pc =>
            {
                pc.Property(m => m.Amount)
                    .HasColumnName("unit_price_amount");
                pc.Property(m => m.Currency)
                    .HasColumnName("unit_price_currency")
                    .HasConversion(c => c.Code, code => Currency.FromCode(code));
            });

            ib.OwnsOne(i => i.ReceivedQuantity, rqb =>
            {
                rqb.Property(m => m.Value)
                    .HasColumnName("received_quantity_value");
                rqb.Property(m => m.Unit)
                    .HasColumnName("received_quantity_unit")
                    .HasConversion(
                        u => u.Symbol,
                        s => UnitOfMeasureConverter.FromString(s).Value);
            });

            ib.Property(i => i.Notes);
        });
    }
}