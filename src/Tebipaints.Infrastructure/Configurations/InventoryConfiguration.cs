using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tebipaints.Domain.Inventory;

namespace Tebipaints.Infrastructure.Configurations;

// Base configuration for Inventory<double>
public class InventoryDoubleConfiguration : IEntityTypeConfiguration<Inventory<double>>
{
    public void Configure(EntityTypeBuilder<Inventory<double>> builder)
    {
        builder.ToTable("inventory_double");

        builder.<PERSON><PERSON><PERSON>(x => x.Id);

        builder.Property(i => i.Location).HasConversion<string>();

        // Configure base properties
        builder.Property(i => i.MinimumStockLevel)
            .HasColumnName("minimum_stock_level");

        builder.Property(i => i.ReorderPoint)
            .HasColumnName("reorder_point");

        // Configure the transactions as owned entities
        builder.OwnsMany(i => i.Transactions, tb =>
        {
            tb.ToTable("material_inventory_transactions");

            tb.WithOwner().HasForeign<PERSON>ey("inventory_double_id");

            tb.<PERSON>(nameof(InventoryTransaction<double>.TransactionId), "inventory_double_id");

            tb.Property(t => t.TransactionId)
                .ValueGeneratedNever();

            tb.Property(t => t.Type)
                .HasConversion<string>();

            tb.Property(t => t.Quantity);
            tb.Property(t => t.Reference);
            tb.Property(t => t.Timestamp);
        });
    }
}

// Base configuration for Inventory<int>
public class InventoryIntConfiguration : IEntityTypeConfiguration<Inventory<int>>
{
    public void Configure(EntityTypeBuilder<Inventory<int>> builder)
    {
        builder.ToTable("inventory_int");

        builder.HasKey(x => x.Id);

        builder.Property(i => i.Location).HasConversion<string>();

        // Configure base properties
        builder.Property(i => i.MinimumStockLevel)
            .HasColumnName("minimum_stock_level");

        builder.Property(i => i.ReorderPoint)
            .HasColumnName("reorder_point");

        // Configure the transactions as owned entities
        builder.OwnsMany(i => i.Transactions, tb =>
        {
            tb.ToTable("ProductInventoryTransactions");

            tb.WithOwner().HasForeignKey("inventory_int_id");

            tb.HasKey(nameof(InventoryTransaction<int>.TransactionId), "inventory_int_id");

            tb.Property(t => t.TransactionId)
                .ValueGeneratedNever();

            tb.Property(t => t.Type)
                .HasConversion<string>();

            tb.Property(t => t.Quantity);
            tb.Property(t => t.Reference);
            tb.Property(t => t.Timestamp);
        });
    }
}