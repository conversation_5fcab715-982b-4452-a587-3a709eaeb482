using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tebipaints.Domain.Inventory;

namespace Tebipaints.Infrastructure.Configurations;

public class MaterialInventoryConfiguration : IEntityTypeConfiguration<MaterialInventory>
{
    public void Configure(EntityTypeBuilder<MaterialInventory> builder)
    {
        builder.ToTable("material_inventories");

        // Configure inheritance - inherits from Inventory<double>
        builder.HasBaseType<Inventory<double>>();

        // Configure MaterialInventory-specific properties
        builder.Property(m => m.MaterialId)
            .IsRequired();

        builder.HasIndex(m => m.MaterialId);

        // Configure reservations (MaterialInventory-specific)
        builder.OwnsMany(i => i.Reservations, rb =>
        {
            rb.ToTable("material_inventory_reservations");

            rb.WithOwner().HasForeignKey("inventory_id");

            rb.Property<Guid>("Id")
                .ValueGeneratedOnAdd();

            rb.<PERSON>ey("Id");

            rb.Property(r => r.MaterialId);
            rb.Property(r => r.Quantity);
            rb.Property(r => r.Purpose);
            rb.Property(r => r.CreatedAt);
        });
    }
}