using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tebipaints.Domain.Product;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Infrastructure.Configurations;

internal sealed class ProductConfiguration : IEntityTypeConfiguration<Product>
{
    public void Configure(EntityTypeBuilder<Product> builder)
    {
        builder.ToTable("products");
        
        builder.HasKey(p => p.Id);

        builder.Property(p => p.FormulationId);

        builder.OwnsOne(p => p.Name);

        builder.OwnsOne(p => p.Sku, sb =>
        {
            sb.Property(s => s.Value);
        });

        builder.OwnsOne(p => p.Color);

        builder.OwnsOne(p => p.Volume, vb =>
        {
            vb.Property(v => v.Value);
            vb.Property(v => v.Unit)
                .HasConversion(
                    s => s.Unit,
                    u => VolumetricUnit.FromUnit(u));
        });

        builder.Property(p => p.Type).HasConversion<string>();

        builder.OwnsOne(p => p.Price, pc =>
        {
            pc.Property(m => m.Currency)
                .HasConversion(c => c.Code, code => Currency.FromCode(code));

            pc.Property(m => m.Amount);
        });

        builder.Property(p => p.CreatedOnUtc);
        
        builder.Property(p => p.Status).HasConversion<string>();
        
        builder.HasMany<ProductVariant>()
            .WithOne()
            .HasForeignKey(pv => pv.ProductId);
    }
}