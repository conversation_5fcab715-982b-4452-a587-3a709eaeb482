using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Procurement.Enums;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Infrastructure.Configurations;

internal sealed class ContractConfiguration : IEntityTypeConfiguration<Contract>
{
    public void Configure(EntityTypeBuilder<Contract> builder)
    {
        builder.ToTable("contracts");
        builder.HasKey(c => c.Id);

        builder.Property(c => c.Id).ValueGeneratedNever();
        builder.Property(c => c.StartDate);
        builder.Property(c => c.EndDate);
        builder.Property(c => c.Status).HasConversion<string>();

        builder.OwnsOne(c => c.ContractNumber, cn =>
        {
            cn.Property(p => p.Value);
        });

        builder.OwnsMany(c => c.Terms, tb =>
        {
            tb.ToTable("contract_terms");
            tb.WithOwner().HasForeignKey("ContractId");

            tb.Property(t => t.Description);
            tb.Property(t => t.Type).HasConversion<string>();
            tb.Property(t => t.ExpirationDate);
            
            // Add configuration for Values property
            tb.Property(t => t.Values)
                .HasConversion(
                    v => string.Join(",", v.Select(x => x.ToString())),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(Enum.Parse<TermValue>)
                        .ToList()
                );
        });

        builder.OwnsMany(c => c.Materials, mb =>
        {
            mb.ToTable("contract_materials");
            mb.WithOwner().HasForeignKey("ContractId");

            mb.Property(m => m.MaterialId);
            mb.Property(m => m.MaterialName);
            mb.Property(m => m.LeadTimeDays);

            mb.OwnsOne(m => m.MinimumOrder, min =>
            {
                min.Property(p => p.Value);
                min.Property(p => p.Unit)
                    .HasConversion(u => u.Symbol, s => UnitOfMeasureConverter.FromString(s).Value);
            });

            mb.OwnsOne(m => m.MaximumOrder, max =>
            {
                max.Property(p => p.Value);
                max.Property(p => p.Unit)
                    .HasConversion(u => u.Symbol, s => UnitOfMeasureConverter.FromString(s).Value);
            });

            mb.OwnsOne(m => m.UnitPrice, price =>
            {
                price.Property(p => p.Amount);
                price.Property(p => p.Currency)
                    .HasConversion(c => c.Code, code => Currency.FromCode(code));
            });
        });

        builder.HasOne<Supplier>() // inverse navigation
            .WithMany(s => s.Contracts)
            .HasForeignKey("SupplierId")
            .OnDelete(DeleteBehavior.Cascade);
    }
}