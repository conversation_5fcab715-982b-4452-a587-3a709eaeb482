using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tebipaints.Domain.Production;
using Tebipaints.Domain.Production.Enums;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Infrastructure.Configurations;

internal sealed class ProductionLineConfiguration : IEntityTypeConfiguration<ProductionLine>
{
    public void Configure(EntityTypeBuilder<ProductionLine> builder)
    {
        builder.ToTable("production_lines");
        builder.HasKey(p => p.Id);

        builder.Property(p => p.Name);
        builder.Property(p => p.Description);
        builder.Property(p => p.Status).HasConversion<string>();

        builder.OwnsMany(p => p.Capabilities, cb =>
        {
            cb.ToTable("capabilities");
            cb.WithOwner().HasForeignKey("ProductionLineId");

            cb.Property<int>("Id").ValueGeneratedOnAdd();
            cb.<PERSON>("Id");
            
            cb.Property(c => c.ProductType).HasConversion<string>();
            cb.OwnsOne(c => c.MinBatchSize, mb =>
            {
                mb.Property(m => m.Value);
                mb.Property(m => m.Unit)
                    .HasConversion(
                        u => u.Symbol,
                        s => UnitOfMeasureConverter.FromString(s).Value);
            });
            
            cb.OwnsOne(c => c.MaxBatchSize, mb =>
            {
                mb.Property(m => m.Value);
                mb.Property(m => m.Unit)
                    .HasConversion(
                        u => u.Symbol,
                        s => UnitOfMeasureConverter.FromString(s).Value);
            });
        });

        builder.OwnsMany(p => p.Schedules, sb =>
        {
            sb.ToTable("production_line_schedules");
            
            sb.WithOwner().HasForeignKey("ProductionLineId");
            
            sb.Property<int>("Id").ValueGeneratedOnAdd();
            sb.HasKey("Id");
            
            sb.Property(s => s.BatchId);
            sb.Property(s => s.StartTime);
            sb.Property(s => s.EndTime);

            // Create indexes for efficient querying
            sb.HasIndex(s => s.BatchId);
            sb.HasIndex(s => new { s.StartTime, s.EndTime });
            
            // Add unique constraint to prevent double-booking a batch
            sb.HasIndex(s => new { s.BatchId, s.ProductionLineId })
                .IsUnique();
        });
        
        // builder.Property(p => p.SupportedProductTypes)
        //     .HasConversion(
        //         types => string.Join(',', types.Select(t => t.ToString())),
        //         dbvalue => dbvalue.Split(',', StringSplitOptions.RemoveEmptyEntries)
        //             .Select(a => Enum.Parse<ProductType>(a))
        //             .ToList(),
        //         new ValueComparer<IReadOnlyCollection<ProductType>>(
        //             (c1, c2) => c1.SequenceEqual(c2),
        //             c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
        //             c => c.ToList()));
        
    }
}