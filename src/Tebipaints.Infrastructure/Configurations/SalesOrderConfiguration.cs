using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Tebipaints.Domain.SalesOrder;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Infrastructure.Configurations;

internal sealed class SalesOrderConfiguration : IEntityTypeConfiguration<SalesOrder>
{
    public void Configure(EntityTypeBuilder<SalesOrder> builder)
    {
        builder.ToTable("sales_orders");
        
        builder.<PERSON><PERSON>ey(s => s.Id);

        builder.Property(s => s.CustomerId);

        builder.Property(s => s.WalkInCustomerName);

        builder.Property(s => s.CreatedOnUtc);

        builder.Property(s => s.FulfilledOnUtc);

        builder.Property(s => s.InvoiceId);

        builder.OwnsOne(s => s.OrderNumber, onb =>
        {
            onb.Property(o => o.Value);
        });

        builder.OwnsMany(s => s.SalesOrderLineItems, ib =>
        {
            ib.Property(i => i.ProductId);

            ib.OwnsOne(i => i.Sku);

            ib.Property(i => i.Description);

            ib.Property(i => i.Quantity);

            ib.OwnsOne(i => i.UnitPrice, pc =>
            {
                pc.Property(m => m.Currency)
                    .HasConversion(c => c.Code, code => Currency.FromCode(code));

                pc.Property(m => m.Amount);
            });

            ib.Property(i => i.DiscountPercentage);

            ib.OwnsMany(i => i.AppliedDiscounts, db =>
            {
                db.Property(d => d.Name);

                db.Property(d => d.Type).HasConversion<string>();

                db.Property(d => d.Percentage);

                db.Property(d => d.Description);

                db.Property(d => d.ValidFrom);

                db.Property(d => d.ValidTo);

                db.Property(d => d.IsActive);
            });
        });

        builder.OwnsMany(s => s.AppliedDiscounts, ab =>
        {
            ab.ToTable("SalesOrderAppliedDiscounts");
            
            ab.HasKey(d => d.Id);
            
            ab.WithOwner().HasForeignKey("SalesOrderId");

            ab.Property(d => d.Name);

            ab.Property(d => d.Type)
                .HasConversion<string>();
            
            ab.Property(d => d.Description);

            ab.Property(d => d.ValidFrom);

            ab.Property(d => d.ValidTo);
            
            ab.Property(d => d.IsActive);
        });
    }
}