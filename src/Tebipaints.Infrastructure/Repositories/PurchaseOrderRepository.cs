using Microsoft.EntityFrameworkCore;
using Tebipaints.Domain.Procurement;

namespace Tebipaints.Infrastructure.Repositories;

internal sealed class PurchaseOrderRepository : Repository<PurchaseOrder>, IPurchaseOrderRepository
{
    public PurchaseOrderRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
    }

    public override async Task<PurchaseOrder?> GetByIdAsync(Guid purchaseOrderId, CancellationToken cancellationToken)
    {
        return await DbContext.Set<PurchaseOrder>()
            .Include(po => po.Items)
            .Where(po => po.Id == purchaseOrderId)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public void Update(PurchaseOrder purchaseOrder)
    {
        var entry = DbContext.Entry(purchaseOrder);

        if (entry.State == EntityState.Detached)
        {
            // For detached entities, attach and let EF Core track changes
            DbContext.Set<PurchaseOrder>().Attach(purchaseOrder);
            entry = DbContext.Entry(purchaseOrder);
        }

        // Only mark scalar properties as modified, not navigation properties or owned types
        foreach (var property in entry.Properties)
        {
            if (property.Metadata.IsForeignKey() || property.Metadata.IsKey())
                continue;

            if (property.IsModified)
                continue;

            property.IsModified = true;
        }
    }

    public bool DoesExist(Guid purchaseOrderId)
    {
        return DbContext.Set<PurchaseOrder>()
            .Any(po => po.Id == purchaseOrderId);
    }
}