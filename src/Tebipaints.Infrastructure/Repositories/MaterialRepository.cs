using Microsoft.EntityFrameworkCore;
using Tebipaints.Domain.Formulation;
using Tebipaints.Domain.Material;
using Tebipaints.Domain.Production;

namespace Tebipaints.Infrastructure.Repositories;

internal sealed class MaterialRepository : Repository<Material>, IMaterialRepository
{
    public MaterialRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
    }

    public async Task<Material?> GetByCodeAsync(string code, CancellationToken cancellationToken)
    {
        return await DbContext.Set<Material>()
            .FirstOrDefaultAsync(
                m => m.Code == code, 
                cancellationToken);
    }

    public async Task<Dictionary<Guid, MaterialDetails>?> GetMaterialDetailsAsync(List<Ingredient> ingredients)
    {
        if (!ingredients.Any())
        {
            return null;
        }

        var materialIds = ingredients
            .Select(i => i.MaterialId)
            .Distinct()
            .ToList();

        var materials = await DbContext.Set<Material>()
            .Where(m => materialIds.Contains(m.Id))
            .Select(m => new MaterialDetails(
                m.Id,
                m.Name,
                m.DefaultUnit,
                1.0))
            .ToDictionaryAsync(
                m => m.MaterialId,
                m => m);

        return materials.Count > 0 ? materials : null;
    }
}