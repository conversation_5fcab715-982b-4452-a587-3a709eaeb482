using Microsoft.EntityFrameworkCore;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.SalesOrder;

namespace Tebipaints.Infrastructure.Repositories;

internal sealed class SalesOrderRepository : Repository<SalesOrder>, ISalesOrderRepository
{
    public SalesOrderRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
    }

    public void UpdateAsync(SalesOrder salesOrder, CancellationToken cancellationToken)
    {
        var entry = DbContext.Entry(salesOrder);

        // If the entity is not being tracked, attach it
        if (entry.State == EntityState.Detached)
        {
            DbContext.Set<SalesOrder>().Attach(salesOrder);
        }

        // Mark as modified
        entry.State = EntityState.Modified;

        // Mark items collection as modified if it exists
        if (entry.Collection("_orderLineItems").IsLoaded)
        {
            entry.Collection("_orderLineItems").IsModified = true;
        }
    }
}