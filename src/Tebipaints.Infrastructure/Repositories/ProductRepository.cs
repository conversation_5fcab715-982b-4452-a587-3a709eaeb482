using Microsoft.EntityFrameworkCore;
using Tebipaints.Domain.Product;

namespace Tebipaints.Infrastructure.Repositories;

internal sealed class ProductRepository : Repository<Product>, IProductRepository
{
    public ProductRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
    }

    public void Update(Product product, CancellationToken cancellationToken = default)
    {
        var entry = DbContext.Entry(product);

        // If the entity is not being tracked, attach it
        if (entry.State == EntityState.Detached)
        {
            DbContext.Set<Product>().Attach(product);
        }

        // Mark as modified
        entry.State = EntityState.Modified;

        // Mark variants collection as modified if it exists
        if (entry.Collection("_variants").IsLoaded)
        {
            entry.Collection("_variants").IsModified = true;
        }
    }

    public async Task<Product?> GetByIdWithVariantAsync(Guid productId, CancellationToken cancellationToken)
    {
        return await DbContext.Set<Product>()
            .Include(p => p.Variants)  // Include variants collection (using backing field)
            .ThenInclude(v => v.PackagingOptions)  // Include packaging options for each variant
            .FirstOrDefaultAsync(p => p.Id == productId, cancellationToken);
    }
}