using Microsoft.EntityFrameworkCore;
using Tebipaints.Domain.Production;
using Tebipaints.Domain.Production.Repositories;

namespace Tebipaints.Infrastructure.Repositories;

internal sealed class BatchRepository : Repository<Batch>, IBatchRepository
{
    public BatchRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
    }

    public async Task<Batch?> GetByBatchNumberAsync(string batchNumber)
    {
        return await DbContext.Set<Batch>()
            .Include((b => b.QualityChecks))
            .Include(b => b.Ingredients)
            .Include(b => b.ProductionLosses)
            .FirstOrDefaultAsync(b => b.BatchNumber == batchNumber);
    }
}