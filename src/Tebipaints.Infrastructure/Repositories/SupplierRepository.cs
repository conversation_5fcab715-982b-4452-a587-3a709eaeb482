using Microsoft.EntityFrameworkCore;
using Tebipaints.Domain.Procurement;

namespace Tebipaints.Infrastructure.Repositories;

internal sealed class SupplierRepository : Repository<Supplier>, ISupplierRepository
{
    public SupplierRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
    }

    public async Task<Supplier?> GetByContractIdAsync(Guid contractId, CancellationToken cancellationToken = default)
    {
        return await DbContext.Set<Supplier>()
            .Include(s => s.Contracts)  // Include contracts for eager loading
            .FirstOrDefaultAsync(s =>
                    s.Contracts.Any(c => c.Id == contractId),
                cancellationToken);
    }

    public async Task<bool> ExistsAsync(string code, CancellationToken cancellationToken = default)
    {
        return await DbContext.Set<Supplier>()
            .AnyAsync(s => s.Code == code, cancellationToken);
    }

    public void UpdateSupplier(Supplier supplier)
    {
        // // Get the current state of the supplier from the database
        // var existingSupplier = DbContext.Set<Supplier>()
        //     .Include(s => s.Contracts)
        //     .FirstOrDefault(s => s.Id == supplier.Id);
        //
        // if (existingSupplier != null)
        // {
        //     // Update the supplier properties
        //     DbContext.Entry(existingSupplier).CurrentValues.SetValues(supplier);
        //
        //     // Handle contracts
        //     foreach (var contract in supplier.Contracts)
        //     {
        //         var existingContract = existingSupplier.Contracts
        //             .FirstOrDefault(c => c.Id == contract.Id);
        //
        //         if (existingContract == null)
        //         {
        //             // This is a new contract, add it directly to the supplier
        //             existingSupplier.AddContractInternal(contract);
        //         }
        //         else
        //         {
        //             // This is an existing contract, update it
        //             DbContext.Entry(existingContract).CurrentValues.SetValues(contract);
        //         }
        //     }
        // }
        // else
        // {
        //     // If the supplier doesn't exist, add it
        //     DbContext.Set<Supplier>().Add(supplier);
        // }
        
        // DbContext.Set<Supplier>().Update(supplier);
    }
}