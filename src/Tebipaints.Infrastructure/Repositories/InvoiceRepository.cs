using Microsoft.EntityFrameworkCore;
using Tebipaints.Domain.Invoice;

namespace Tebipaints.Infrastructure.Repositories;

internal sealed class InvoiceRepository : Repository<Invoice>, IInvoiceRepository
{
    public InvoiceRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
    }

    public Task<int> GetNextPaymentSequenceAsync(CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }

    public async Task UpdateAsync(Invoice invoice, CancellationToken cancellationToken)
    {
        // Get the entry for tracking
        var entry = DbContext.Entry(invoice);

        // If the entity is not being tracked, attach it
        if (entry.State == EntityState.Detached)
        {
            DbContext.Set<Invoice>().Attach(invoice);
        }

        // Mark as modified
        entry.State = EntityState.Modified;
    }
}