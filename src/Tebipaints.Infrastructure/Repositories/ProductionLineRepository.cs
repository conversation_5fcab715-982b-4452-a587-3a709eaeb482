using Microsoft.EntityFrameworkCore;
using Tebipaints.Domain.Production;
using Tebipaints.Domain.Production.Enums;
using Tebipaints.Domain.Production.Repositories;

namespace Tebipaints.Infrastructure.Repositories;

internal sealed class ProductionLineRepository : Repository<ProductionLine>, IProductionLineRepository
{
    public ProductionLineRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
    }

    public async Task<List<ProductionLine>> GetAvailableLines(DateTime fromDate, CancellationToken cancellationToken = default)
    {
        return await DbContext.Set<ProductionLine>()
            .Include(pl => pl.Capabilities)
            .Where(pl => pl.Status == ProductionLineStatus.Active &&
                         !pl.Schedules.Any(s => s.EndTime > fromDate))
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> IsLineOccupied(Guid lineId, DateTime startTime, TimeSpan duration, CancellationToken cancellationToken = default)
    {
        var endTime = startTime.Add(duration);

        return await DbContext.Set<ProductionLine>()
            .AnyAsync(pl => 
                    pl.Id == lineId && 
                    pl.Schedules.Any(s =>
                        s.StartTime < endTime && 
                        s.EndTime > startTime),
                cancellationToken);
    }

    public async Task<List<ProductionLine>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await DbContext.Set<ProductionLine>()
            .Include(pl => pl.Capabilities)
            .Include(pl => pl.Schedules)
            .ToListAsync(cancellationToken);
    }

    public async Task<ProductionLine?> GetDefaultLineForProductTypeAsync(ProductType productType, CancellationToken cancellationToken = default)
    {
        return await DbContext.Set<ProductionLine>()
            .Include(pl => pl.Capabilities)
            .Where(pl => 
                pl.Status == ProductionLineStatus.Active &&
                pl.Capabilities.Any(c => c.ProductType == productType))
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<ProductionLine>> GetAvailableLinesForProductTypeAsync(ProductType productType, DateTime startDate,
        CancellationToken cancellationToken = default)
    {
        return await DbContext.Set<ProductionLine>()
            .Include(pl => pl.Capabilities)
            .Include(pl => pl.Schedules)
            .Where(pl => 
                pl.Status == ProductionLineStatus.Active &&
                pl.Capabilities.Any(c => c.ProductType == productType) &&
                !pl.Schedules.Any(s => s.EndTime > startDate))
            .ToListAsync(cancellationToken);
    }
}