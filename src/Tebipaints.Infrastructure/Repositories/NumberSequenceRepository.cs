using Microsoft.EntityFrameworkCore;
using Tebipaints.Domain.Invoice;
using Tebipaints.Domain.Procurement;
using Tebipaints.Domain.Production;
using Tebipaints.Domain.SalesOrder;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Infrastructure.Repositories;

internal sealed class NumberSequenceRepository : Repository<NumberSequence>, INumberSequenceRepository
{
    public NumberSequenceRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
    }

    public async Task UpdateAsync(NumberSequence numberSequence, CancellationToken cancellationToken)
    {
        
    }

    public async Task<NumberSequence> GetByTypeAsync(string type, CancellationToken cancellationToken)
    {
        var sequence = await DbContext.Set<NumberSequence>()
            .Where(ns => ns.Type == type)
            .OrderByDescending(ns => ns.Year)
            .ThenByDescending(ns => ns.Month)
            .FirstOrDefaultAsync(cancellationToken);

        if (sequence is null)
        {
            // Check if we need to migrate existing numbers
            var lastUsedNumber = await GetLastUsedNumberForType(type);
            
            sequence = lastUsedNumber.HasValue 
                ? NumberSequence.CreateContinuation(
                    type,
                    lastUsedNumber.Value,
                    DateTime.UtcNow)
                : NumberSequence.Create(type);
                
            DbContext.Set<NumberSequence>().Add(sequence);
            await DbContext.SaveChangesAsync(cancellationToken);
        }

        return sequence;
    }
    
    private async Task<int?> GetLastUsedNumberForType(string type)
    {
        // This method would check existing entities for their numbers
        // and return the highest used number
        return type switch
        {
            "Invoice" => await GetLastUsedInvoiceNumber(),
            "PurchaseOrder" => await GetLastUsedPurchaseOrderNumber(),
            "SalesOrder" => await GetLastUsedSalesOrderNumber(),
            "WorkOrder" => await GetLastWorkOrderNumber(),
            "Contract" => await GetLastUsedContractNumber(),
            "GoodsReceipt" => await GetLastUsedGoodsReceiptNumber(),
            "Sku" => await GetLastUsedSkuNumber(),
            // ... other types
            _ => null
        };
    }

    private async Task<int?> GetLastUsedSkuNumber()
    {
        var lastSku = await DbContext.Set<SKU>()
            .OrderByDescending(s => s.Value)
            .FirstOrDefaultAsync();

        if (lastSku is null) return 0;
        
        var match = System.Text.RegularExpressions.Regex.Match(lastSku.Value, @"\d{5}$");
        
        return match.Success 
            ? int.Parse(match.Value) 
            : null;
    }

    private async Task<int?> GetLastUsedInvoiceNumber()
    {
        var lastInvoice = await DbContext.Set<Invoice>()
            .OrderByDescending(i => i.InvoiceNumber.Value)
            .FirstOrDefaultAsync();

        if (lastInvoice == null) return null;

        // Extract sequence number from invoice number
        // with format "TBP-INV-202401-000123"
        var match = System.Text.RegularExpressions.Regex.Match(
            lastInvoice.InvoiceNumber.Value, 
            @"\d{6}$");

        return match.Success 
            ? int.Parse(match.Value) 
            : null;
    }

    private async Task<int?> GetLastUsedContractNumber()
    {
        var lastContractNumber = await DbContext.Set<Supplier>()
            .SelectMany(s => s.Contracts)
            .OrderByDescending(c => c.ContractNumber.Value)
            .Select(c => c.ContractNumber.Value)
            .FirstOrDefaultAsync();

        if (string.IsNullOrWhiteSpace(lastContractNumber)) return null;

        var match = System.Text.RegularExpressions.Regex.Match(lastContractNumber, @"\d{6}$");

        return match.Success ? int.Parse(match.Value) : null;
    }

    private async Task<int?> GetLastUsedPurchaseOrderNumber()
    {
        var lastPurchaseOrder = await DbContext.Set<PurchaseOrder>()
            .OrderByDescending(p => p.OrderNumber.Value)
            .FirstOrDefaultAsync();
        
        if (lastPurchaseOrder == null) return null;

        var match = System.Text.RegularExpressions.Regex.Match(
            lastPurchaseOrder.OrderNumber.Value,
            @"\d{6}$");
        
        return match.Success ? int.Parse(match.Value) : null;
    }

    private async Task<int?> GetLastUsedSalesOrderNumber()
    {
        var lastSalesOrder = await DbContext.Set<SalesOrder>()
            .OrderByDescending(s => s.OrderNumber.Value)
            .FirstOrDefaultAsync();
        
        if (lastSalesOrder == null) return null;
        
        var match = System.Text.RegularExpressions.Regex.Match(
            lastSalesOrder.OrderNumber.Value,
            @"\d{6}$");
        
        return match.Success ? int.Parse(match.Value) : null;
    }

    private async Task<int?> GetLastWorkOrderNumber()
    {
        var lastWorkOrder = await DbContext.Set<WorkOrder>()
            .OrderByDescending(s => s.WorkOrderNumber)
            .FirstOrDefaultAsync();
        
        if (lastWorkOrder == null) return null;
        
        var match = System.Text.RegularExpressions.Regex.Match(
            lastWorkOrder.WorkOrderNumber,
            @"\d{6}$");
        
        return match.Success ? int.Parse(match.Value) : null;
    }

    private async Task<int?> GetLastUsedGoodsReceiptNumber()
    {
        var lastGoodsReceipt = await DbContext.Set<GoodsReceipt>()
            .OrderByDescending(gr => gr.ReceiptNumber.Value)
            .FirstOrDefaultAsync();

        if (lastGoodsReceipt == null) return null;

        var match = System.Text.RegularExpressions.Regex.Match(
            lastGoodsReceipt.ReceiptNumber.Value,
            @"\d{6}$");

        return match.Success ? int.Parse(match.Value) : null;
    }
}