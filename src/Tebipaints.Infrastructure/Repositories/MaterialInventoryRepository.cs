using Microsoft.EntityFrameworkCore;
using Tebipaints.Domain.Inventory;
using Tebipaints.Domain.Material;

namespace Tebipaints.Infrastructure.Repositories;

internal sealed class MaterialInventoryRepository : Repository<MaterialInventory>, IMaterialInventoryRepository
{
    public MaterialInventoryRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
    }

    public async Task<List<MaterialInventory>> GetByIdsAsync(List<Guid> materialIds, CancellationToken cancellationToken)
    {
        return await DbContext.Set<MaterialInventory>()
            .Include(mi => mi.Transactions)  // Include transactions if needed
            .Include(mi => mi.Reservations)  // Include reservations
            .Where(mi => materialIds.Contains(mi.MaterialId))
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<Guid, MaterialInventory>> GetMaterialInventoriesAsync(List<Guid> materialIds)
    {
        var inventories = await DbContext.Set<MaterialInventory>()
            .Include(mi => mi.Transactions)
            .Include(mi => mi.Reservations)
            .Where(mi => materialIds.Contains(mi.MaterialId))
            .ToDictionaryAsync(
                mi => mi.MaterialId,
                mi => mi);

        // Ensure all requested materials have an inventory entry
        foreach (var materialId in materialIds.Where(id => !inventories.ContainsKey(id)))
        {
            // Create new inventory for materials that don't have one
            var newInventory = MaterialInventory.Create(materialId, InventoryLocation.Factory, 0, 0);
            inventories.Add(materialId, newInventory.Value);
            DbContext.Set<MaterialInventory>().Add(newInventory.Value);
        }

        return inventories;
    }

    public void Add(MaterialInventory materialInventory, CancellationToken cancellationToken)
    {
        // Check if inventory already exists
        var exists = DbContext.Set<MaterialInventory>()
            .Any(mi => mi.MaterialId == materialInventory.MaterialId);

        if (exists)
        {
            throw new InvalidOperationException(
                $"Inventory already exists for material {materialInventory.MaterialId}");
        }

        DbContext.Set<MaterialInventory>().Add(materialInventory);
    }

    public Task AddReservationAsync(MaterialReservation materialReservation)
    {
        throw new NotImplementedException();
    }
}