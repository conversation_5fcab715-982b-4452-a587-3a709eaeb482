using Microsoft.EntityFrameworkCore;
using Tebipaints.Domain.Formulation;
using Tebipaints.Domain.Formulation.Enums;

namespace Tebipaints.Infrastructure.Repositories;

internal sealed class FormulationRepository : Repository<Domain.Formulation.Formulation>, IFormulationRepository
{
    public FormulationRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
    }

    public async Task<bool> IsUsedInActiveFormulationsAsync(Guid materialId, CancellationToken cancellationToken)
    {
        return await DbContext.Set<Domain.Formulation.Formulation>()
            .Where(f => f.Status != FormulationStatus.Archived)
            .AnyAsync(f => f.Ingredients.Any(i => i.MaterialId == materialId), cancellationToken);
    }
}