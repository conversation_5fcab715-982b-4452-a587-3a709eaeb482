using Microsoft.EntityFrameworkCore;
using Tebipaints.Domain.Procurement;

namespace Tebipaints.Infrastructure.Repositories;

internal sealed class GoodsReceiptRepository : Repository<GoodsReceipt>, IGoodsReceiptRepository
{
    public GoodsReceiptRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
    }

    public async Task<List<GoodsReceipt>> GetByPurchaseOrderIdAsync(Guid purchaseOrderId, CancellationToken cancellationToken = default)
    {
        return await DbContext.Set<GoodsReceipt>()
            .Include(gr => gr.Items)
            .Where(gr => gr.PurchaseOrderId == purchaseOrderId)
            .OrderByDescending(gr => gr.ReceivedDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<GoodsReceipt?> GetByReceiptNumberAsync(string receiptNumber, CancellationToken cancellationToken = default)
    {
        return await DbContext.Set<GoodsReceipt>()
            .Include(gr => gr.Items)
            .FirstOrDefaultAsync(gr => gr.ReceiptNumber.Value == receiptNumber, cancellationToken);
    }

    public override async Task<GoodsReceipt?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await DbContext.Set<GoodsReceipt>()
            .Include(gr => gr.Items)
            .FirstOrDefaultAsync(gr => gr.Id == id, cancellationToken);
    }

    public void Update(GoodsReceipt goodsReceipt)
    {
        var entry = DbContext.Entry(goodsReceipt);

        // If the entity is not being tracked, attach it
        if (entry.State == EntityState.Detached)
        {
            DbContext.Set<GoodsReceipt>().Attach(goodsReceipt);
        }

        // Mark as modified
        entry.State = EntityState.Modified;

        // Mark items collection as modified if it exists
        if (entry.Collection("_items").IsLoaded)
        {
            entry.Collection("_items").IsModified = true;
        }
    }

    public async Task<bool> ExistsAsync(string receiptNumber, CancellationToken cancellationToken = default)
    {
        return await DbContext.Set<GoodsReceipt>()
            .AnyAsync(gr => gr.ReceiptNumber.Value == receiptNumber, cancellationToken);
    }
}
