using Microsoft.EntityFrameworkCore;
using Tebipaints.Domain.Procurement;

namespace Tebipaints.Infrastructure.Repositories;

internal sealed class PurchaseInvoiceRepository : Repository<PurchaseInvoice>, IPurchaseInvoiceRepository
{
    public PurchaseInvoiceRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
    }

    public async Task<List<PurchaseInvoice>> GetByPurchaseOrderIdAsync(Guid purchaseOrderId, CancellationToken cancellationToken = default)
    {
        return await DbContext.Set<PurchaseInvoice>()
            .Include(pi => pi.Items)
            .Where(pi => pi.PurchaseOrderId == purchaseOrderId)
            .OrderByDescending(pi => pi.InvoiceDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<PurchaseInvoice?> GetBySupplierInvoiceNumberAsync(string supplierInvoiceNumber, Guid supplierId, CancellationToken cancellationToken = default)
    {
        return await DbContext.Set<PurchaseInvoice>()
            .Include(pi => pi.Items)
            .FirstOrDefaultAsync(pi => pi.SupplierInvoiceNumber == supplierInvoiceNumber && pi.SupplierId == supplierId, cancellationToken);
    }

    public override async Task<PurchaseInvoice?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await DbContext.Set<PurchaseInvoice>()
            .Include(pi => pi.Items)
            .FirstOrDefaultAsync(pi => pi.Id == id, cancellationToken);
    }

    public void Update(PurchaseInvoice purchaseInvoice)
    {
        var entry = DbContext.Entry(purchaseInvoice);

        // If the entity is not being tracked, attach it
        if (entry.State == EntityState.Detached)
        {
            DbContext.Set<PurchaseInvoice>().Attach(purchaseInvoice);
        }

        // Mark as modified
        entry.State = EntityState.Modified;

        // Mark items collection as modified if it exists
        if (entry.Collection("_items").IsLoaded)
        {
            entry.Collection("_items").IsModified = true;
        }
    }

    public async Task<bool> ExistsAsync(string supplierInvoiceNumber, Guid supplierId, CancellationToken cancellationToken = default)
    {
        return await DbContext.Set<PurchaseInvoice>()
            .AnyAsync(pi => pi.SupplierInvoiceNumber == supplierInvoiceNumber && pi.SupplierId == supplierId, cancellationToken);
    }
}
