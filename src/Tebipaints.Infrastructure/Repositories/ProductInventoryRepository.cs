using Microsoft.EntityFrameworkCore;
using Tebipaints.Domain.Inventory;

namespace Tebipaints.Infrastructure.Repositories;

internal sealed class ProductInventoryRepository : Repository<ProductInventory>, IProductInventoryRepository
{
    public ProductInventoryRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
    }

    public async Task<ProductInventory?> GetByProductAndLocationAsync(Guid productId, InventoryLocation location, CancellationToken cancellationToken)
    {
        return await DbContext.Set<ProductInventory>()
            .Include(pi => pi.Transactions)
            .Include(pi => pi.VariantStockLevels)
            .Include(pi => pi.VariantTransactions)
            .FirstOrDefaultAsync(
                pi => pi.ProductId == productId && 
                      pi.Location == location,
                cancellationToken);
    }

    public async Task<List<ProductInventory>> GetByIdsAsync(List<Guid> productIds, CancellationToken cancellationToken)
    {
        return await DbContext.Set<ProductInventory>()
            .Include(pi => pi.Transactions)
            .Include(pi => pi.VariantStockLevels)
            .Include(pi => pi.VariantTransactions)
            .Where(pi => productIds.Contains(pi.ProductId))
            .ToListAsync(cancellationToken);
    }
}