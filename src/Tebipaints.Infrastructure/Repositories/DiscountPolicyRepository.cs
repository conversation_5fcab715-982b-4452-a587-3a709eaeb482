using Microsoft.EntityFrameworkCore;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Infrastructure.Repositories;

internal sealed class DiscountPolicyRepository : Repository<DiscountPolicy>, IDiscountPolicyRepository
{
    public DiscountPolicyRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
    }

    public async Task<List<DiscountPolicy>> GetByIdsAsync(List<Guid> ids, CancellationToken cancellationToken)
    {
        return await DbContext.Set<DiscountPolicy>()
            .Where(p => ids.Contains(p.Id))
            .ToListAsync(cancellationToken);
    }
}