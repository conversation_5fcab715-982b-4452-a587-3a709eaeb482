using Microsoft.EntityFrameworkCore;
using Tebipaints.Domain.Production;
using Tebipaints.Domain.Production.Repositories;

namespace Tebipaints.Infrastructure.Repositories;

internal sealed class WorkOrderRepository : Repository<WorkOrder>, IWorkOrderRepository
{
    public WorkOrderRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
    }

    public override async Task<WorkOrder?> GetByIdAsync(
        Guid id,
        CancellationToken cancellationToken = default)
    {
        return await DbContext
            .Set<WorkOrder>()
            .Include(w => w.Batches)
            .ThenInclude(b => b.Ingredients)
            .Include(w => w.Batches)
            .ThenInclude(b => b.QualityChecks)
            .Include(w => w.Batches)
            .ThenInclude(b => b.ProductionLosses)
            .Include(w => w.BillOfMaterials)
            .FirstOrDefaultAsync(w => w.Id == id, cancellationToken);
    }

}