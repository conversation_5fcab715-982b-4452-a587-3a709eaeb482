using MediatR;
using Microsoft.AspNetCore.Mvc;
using Tebipaints.Application.Production.ProductionLines.ActivateProductionLine;
using Tebipaints.Application.Production.ProductionLines.AddProductionLine;
using Tebipaints.Application.Production.ProductionLines.DeactivateProductionLine;
using Tebipaints.Application.Production.ProductionLines.ListProductionLines;
using Tebipaints.Application.Production.WorkOrders.CompleteBatch;
using Tebipaints.Application.Production.WorkOrders.CompleteWorkOrder;
using Tebipaints.Application.Production.WorkOrders.CreateWorkOrder;
using Tebipaints.Application.Production.WorkOrders.ListWorkOrders;
using Tebipaints.Application.Production.WorkOrders.RecordBatchQualityCheck;
using Tebipaints.Application.Production.WorkOrders.RecordProductionLoss;
using Tebipaints.Application.Production.WorkOrders.StartWorkOrder;

namespace Tebipaints.Api.Controllers.Production;

[ApiController]
[Route("api/production")]
public class ProductionController : ControllerBase
{
    private readonly ISender _sender;

    public ProductionController(ISender sender)
    {
        _sender = sender;
    }
    
    [HttpGet("work-orders/{id:guid}")]
    public async Task<IActionResult> GetWorkOrder(Guid id)
    {
        // Placeholder for the GET endpoint
        // This is needed for the CreatedAtAction to work
        throw new NotImplementedException();
    }
    
    [HttpGet("production-lines/{id:guid}")]
    public async Task<IActionResult> GetProductionLine(Guid id)
    {
        // Placeholder for the GET endpoint
        // This is needed for the CreatedAtAction to work
        throw new NotImplementedException();
    }

    [HttpGet("work-orders")]
    public async Task<IActionResult> GetWorkOrders()
    {
        var query = new ListWorkOrdersQuery();
        var result = await _sender.Send(query);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok(result.Value);
    }
    
    [HttpPost("work-orders")]
    public async Task<IActionResult> CreateWorkOrder([FromBody] CreateWorkOrderRequest request)
    {
        var command = new CreateWorkOrderCommand(
            request.ProductId,
            request.FormulationId,
            request.VariantId,
            request.PackagingType,
            request.PackagingCapacity,
            request.PackagingCapacityUnit,
            request.TotalQuantity,
            request.Units,
            request.DueDate,
            request.PlannedStartDate);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return CreatedAtAction(
            nameof(GetWorkOrder), 
            new { id = result.Value }, 
            result.Value);
    }
    
    [HttpPost("work-orders/{id:guid}/start")]
    public async Task<IActionResult> StartWorkOrder(Guid id)
    {
        var command = new StartWorkOrderCommand(id);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpPost("work-orders/{id:guid}/complete")]
    public async Task<IActionResult> CompleteWorkOrder(Guid id)
    {
        var command = new CompleteWorkOrderCommand(id);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpPost("work-orders/{id:guid}/batches/{batchId:guid}/complete")]
    public async Task<IActionResult> CompleteBatch(
        Guid id, 
        Guid batchId, 
        [FromBody] CompleteBatchRequest request)
    {
        var command = new CompleteBatchCommand(
            id,
            batchId,
            request.ActualYield);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpPost("work-orders/{id:guid}/batches/{batchId:guid}/quality-checks")]
    public async Task<IActionResult> RecordBatchQualityCheck(
        Guid id, 
        Guid batchId, 
        [FromBody] RecordBatchQualityCheckRequest request)
    {
        var command = new RecordBatchQualityCheckCommand(
            id,
            batchId,
            request.Parameter,
            request.MeasuredValue,
            request.MinimumValue,
            request.MaximumValue,
            request.CheckedBy);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpPost("work-orders/{id:guid}/batches/{batchId:guid}/losses")]
    public async Task<IActionResult> RecordProductionLoss(
        Guid id,
        Guid batchId,
        [FromBody] RecordProductionLossRequest request)
    {
        var command = new RecordProductionLossCommand(
            id,
            batchId,
            request.Quantity,
            request.LossType,
            request.Reason);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }

    [HttpGet("production-lines")]
    public async Task<IActionResult> GetProductionLines()
    {
        var query = new ListProductionLinesQuery();
        var result = await _sender.Send(query);
        
        return Ok(result.Value);
    }
    
    [HttpPost("production-lines")]
    public async Task<IActionResult> AddProductionLine([FromBody] AddProductionLineRequest request)
    {
        var command = new AddProductionLineCommand(
            request.Name,
            request.Description,
            request.Capabilities.ConvertAll(c => new ProductionCapabilityDto(
                c.ProductType,
                c.MinBatchSize,
                c.MaxBatchSize,
                c.Units)));

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return CreatedAtAction(
            nameof(GetProductionLine), 
            new { id = result.Value }, 
            result.Value);
    }
    
    [HttpPost("production-lines/{id:guid}/deactivate")]
    public async Task<IActionResult> DeactivateProductionLine(
        Guid id, 
        [FromBody] DeactivateProductionLineRequest request)
    {
        var command = new DeactivateProductionLineCommand(
            id,
            request.Reason);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpPost("production-lines/{id:guid}/activate")]
    public async Task<IActionResult> ActivateProductionLine(Guid id)
    {
        var command = new ActivateProductionLineCommand(id);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
}
