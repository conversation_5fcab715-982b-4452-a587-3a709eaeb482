using MediatR;
using Microsoft.AspNetCore.Mvc;
using Tebipaints.Application.Materials.CreateMaterial;
using Tebipaints.Application.Materials.DiscontinueMaterial;
using Tebipaints.Application.Materials.ListMaterials;
using Tebipaints.Application.Materials.UpdateMaterial;

namespace Tebipaints.Api.Controllers.Materials
{
    [ApiController]
    [Route("api/materials")]
    public class MaterialsController : ControllerBase
    {
        private readonly ISender _sender;

        public MaterialsController(ISender sender)
        {
            _sender = sender;
        }

        [HttpGet]
        public async Task<IActionResult> ListMaterials()
        {
            var query = new ListMaterialsQuery();
            var result = await _sender.Send(query);
            
            return Ok(result.Value);
        }

        [HttpPost("create")]
        public async Task<IActionResult> CreateMaterail(CreateMaterialRequest request, CancellationToken cancellationToken)
        {
            var command = new CreateMaterialCommand(
                request.Code,
                request.Name,
                request.Description,
                request.Type,
                request.DefaultUnit);
            
            var result = await _sender.Send(command, cancellationToken);

            if (result.IsFailure)
            {
                return BadRequest(result.Error);
            }
            
            return CreatedAtAction(nameof(CreateMaterail), new { id = result.Value }, result.Value);
        }

        [HttpPost("discontinue")]
        public async Task<IActionResult> DiscontinueMaterial(DiscontinueMaterialRequest request,
            CancellationToken cancellationToken)
        {
            var command = new DiscontinueMaterialCommand(
                request.MaterialId, request.Reason);
            
            var result = await _sender.Send(command, cancellationToken);

            if (result.IsFailure)
            {
                return BadRequest(result.Error);
            }
            
            return Ok();
        }

        [HttpPost("update")]
        public async Task<IActionResult> UpdateMaterial(UpdateMaterialRequest request,
            CancellationToken cancellationToken)
        {
            var command = new UpdateMaterialCommand(
                request.MaterialId,
                request.Name,
                request.Description,
                request.DefaultUnit);
            
            var result = await _sender.Send(command, cancellationToken);

            if (result.IsFailure)
            {
                return BadRequest(result.Error);
            }

            return Ok();
        }
    }
}
