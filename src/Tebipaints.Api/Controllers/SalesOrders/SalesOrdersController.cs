using MediatR;
using Microsoft.AspNetCore.Mvc;
using Tebipaints.Application.SalesOrders.CreateSalesOrder;
using Tebipaints.Application.SalesOrders.IssueInvoice;
using Tebipaints.Domain.Abstractions;

namespace Tebipaints.Api.Controllers.SalesOrders;
[ApiController]
[Route("api/sales")]
public class SalesOrdersController : ControllerBase
{
    private readonly ISender _sender;

    public SalesOrdersController(ISender sender)
    {
        _sender = sender;
    }

    [HttpPost]
    public async Task<IActionResult> CreateSalesOrder([FromBody] CreateSalesOrderRequest request)
    {
        var command = new CreateSalesOrderCommand(
            request.LineItems.ConvertAll(l => new OrderLineItemDto(
                l.ProductId,
                l.Sku,
                l.Description,
                l.Quantity,
                l.UnitPrice,
                l.Currency)),
            request.Discounts.ConvertAll(d => new DiscountDto(
                d.DiscountPolicyId,
                d.LineItemId)))
        {
            CustomerId = request.CustomerId,
            WalkInCustomerName = request.WalkInCustomerName
        };
        
        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }
        
        return CreatedAtAction(
            nameof(CreateSalesOrder), 
            new { id = result.Value }, 
            result.Value);
    }
    
    [HttpPost("{id:guid}/invoice")]
    public async Task<IActionResult> IssueInvoice(Guid id)
    {
        var command = new IssueInvoiceCommand(id);
    
        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }
    
        return Ok(result.Value);
    }
}
