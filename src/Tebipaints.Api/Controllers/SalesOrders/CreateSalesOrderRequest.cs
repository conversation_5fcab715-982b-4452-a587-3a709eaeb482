namespace Tebipaints.Api.Controllers.SalesOrders;

public record CreateSalesOrderRequest(
    List<OrderLineItemRequest> LineItems,
    List<DiscountRequest> Discounts,
    Guid? CustomerId = null,
    string? WalkInCustomerName = null);
    
public record OrderLineItemRequest(
    Guid ProductId,
    string Sku,
    string Description,
    int Quantity,
    decimal UnitPrice,
    string Currency);

public record DiscountRequest(
    Guid DiscountPolicyId,
    Guid LineItemId);