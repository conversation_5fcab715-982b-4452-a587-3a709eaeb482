using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Tebipaints.Application.Inventories.AdjustStock;
using Tebipaints.Application.Inventories.CreateProductInventory;
using Tebipaints.Application.Inventories.ListMaterialInventory;
using Tebipaints.Application.Inventories.ListProductInventory;
using Tebipaints.Application.Inventories.TransferStock;
using Tebipaints.Application.Inventories.UpdateReorderPoints;
using PackagingOptionDto = Tebipaints.Application.Inventories.AdjustStock.PackagingOptionDto;

namespace Tebipaints.Api.Controllers.Inventories;

[Route("api/inventory")]
[ApiController]
public class InventoriesController : ControllerBase
{
    private readonly ISender _sender;

    public InventoriesController(ISender sender)
    {
        _sender = sender;
    }
    
    [HttpGet("products/{id:guid}")]
    public async Task<IActionResult> GetInventory(Guid id)
    {
        // Placeholder for the GET endpoint
        // This is needed for the CreatedAtAction to work
        throw new NotImplementedException();
    }

    [HttpGet("products")]
    public async Task<IActionResult> GetProductInventory()
    {
        var query = new ListProductInventoryQuery();
        var result = await _sender.Send(query);
        
        return Ok(result.Value);
    }
    
    [HttpGet("materials")]
    public async Task<IActionResult> GetMaterialInventory()
    {
        var query = new ListMaterialInventoryQuery();
        var result = await _sender.Send(query);
        
        return Ok(result.Value);
    }
    
    [HttpPost("products")]
    public async Task<IActionResult> CreateProductInventory(
        [FromBody] CreateProductInventoryRequest request)
    {
        var command = new CreateProductInventoryCommand(
            request.ProductId,
            request.InventoryLocation,
            request.MinimumStockLevel,
            request.ReorderPoint);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return CreatedAtAction(
            nameof(GetInventory), 
            new { id = result.Value }, 
            result.Value);
    }
    
    [HttpPost("products/{id:guid}/adjust")]
    public async Task<IActionResult> AdjustStock(
        Guid id, 
        [FromBody] AdjustStockRequest request)
    {
        var command = new AdjustStockCommand(
            id,
            request.VariantId,
            new PackagingOptionDto(
                request.Packaging.PackagingType,
                request.Packaging.Material,
                request.Packaging.Capacity,
                request.Packaging.Unit,
                request.Packaging.UnitPrice,
                request.Packaging.Currency),
            request.NewQuantity,
            request.Reason);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpPost("products/{id:guid}/transfer")]
    public async Task<IActionResult> TransferStock(
        Guid id, 
        [FromBody] TransferStockRequest request)
    {
        var command = new TransferStockCommand(
            id,
            request.DestinationLocation,
            request.VariantId,
            new PackagingOptionDto(
                request.Packaging.PackagingType,
                request.Packaging.Material,
                request.Packaging.Capacity,
                request.Packaging.Unit,
                request.Packaging.UnitPrice,
                request.Packaging.Currency),
            request.Quantity,
            request.Reference);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpPut("products/{id:guid}/reorder-points")]
    public async Task<IActionResult> UpdateReorderPoints(
        Guid id, 
        [FromBody] UpdateReorderPointsRequest request)
    {
        var command = new UpdateReorderPointsCommand(
            id,
            request.MinimumStockLevel,
            request.ReorderPoint);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
}
