using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Tebipaints.Application.Formulation.ApproveFormulation;
using Tebipaints.Application.Formulation.CalculateFormulationCost;
using Tebipaints.Application.Formulation.CreateFormulation;
using Tebipaints.Application.Formulation.GetFormulation;
using Tebipaints.Application.Formulation.ListFormulations;
using Tebipaints.Application.Formulation.ModifyFormulation;
using Tebipaints.Application.Formulation.RejectFormulation;
using Tebipaints.Application.Formulation.SearchFormulations;
using Tebipaints.Application.Formulation.SubmitForApproval;

namespace Tebipaints.Api.Controllers.Formulations;

[ApiController]
[Route("api/formulations")]
public class FormulationsController : ControllerBase
{
    private readonly ISender _sender;

    public FormulationsController(ISender sender)
    {
        _sender = sender;
    }
    
    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetFormulation(Guid id)
    {
        var query = new GetFormulationQuery(id);
        var result = await _sender.Send(query);

        if (result.IsFailure)
        {
            return NotFound(result.Error);
        }

        return Ok(result.Value);
    }

    [HttpGet]
    public async Task<IActionResult> GetAllFormulations()
    {
        var query = new ListFormulationsQuery();
        var result = await _sender.Send(query);
        
        return Ok(result.Value);
    }

    [HttpPost]
    public async Task<IActionResult> CreateFormulation(CreateFormulationRequest request)
    {
        var command = new CreateFormulationCommand(
            request.Name,
            request.Description,
            request.MinimumProductionQuantity,
            request.Units,
            request.ProductionTimeInHours,
            request.ShelfLife,
            request.Instructions,
            request.Ingredients.ConvertAll(i => new IngredientCommand(
                i.MaterialId,
                i.Quantity,
                i.Units)));
        
        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }
        
        return CreatedAtAction(nameof(GetFormulation), new { id = result.Value.Id }, result.Value);
    }

    [HttpPut("{Id:guid}")]
    public async Task<IActionResult> ModifyFormulation(Guid id, ModifyFormulationRequest request)
    {
        var command = new ModifyFormulationCommand(
            id,
            request.Author,
            request.MinimumProductionQuantity,
            request.Units,
            request.ProductionTimeInHours,
            request.ShelfLife,
            request.Instructions,
            request.Ingredients.ConvertAll(i => new IngredientDto(
                i.MaterialId,
                i.Quantity,
                i.Units)));
        
        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }
        
        return Ok();
    }

    [HttpPost("{id:guid}/submit-for-approval")]
    public async Task<IActionResult> SubmitForApproval(Guid id, [FromBody] SubmitForApprovalRequest request)
    {
        var command = new SubmitForApprovalCommand(id, request.SubmittedBy);
        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }

    [HttpPost("{id:guid}/approve")]
    public async Task<IActionResult> ApproveFormulation(Guid id, [FromBody] ApproveFormulationRequest request)
    {
        var command = new ApproveFormulationCommand(id, request.ApprovedBy, request.ApprovalNotes);
        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }

    [HttpPost("{id:guid}/reject")]
    public async Task<IActionResult> RejectFormulation(Guid id, [FromBody] RejectFormulationRequest request)
    {
        var command = new RejectFormulationCommand(id, request.RejectedBy, request.RejectionReason);
        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }

    [HttpGet("search")]
    public async Task<IActionResult> SearchFormulations([FromQuery] SearchFormulationsQuery query)
    {
        var result = await _sender.Send(query);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok(result.Value);
    }

    [HttpGet("{id:guid}/cost")]
    public async Task<IActionResult> CalculateFormulationCost(
        Guid id,
        [FromQuery] decimal productionQuantity,
        [FromQuery] string currency = "GHS")
    {
        var query = new CalculateFormulationCostQuery(id, productionQuantity, currency);
        var result = await _sender.Send(query);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok(result.Value);
    }
}
