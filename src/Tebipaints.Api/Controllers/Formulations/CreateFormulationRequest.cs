namespace Tebipaints.Api.Controllers.Formulations;

public record CreateFormulationRequest(
    string Name,
    string Description,
    double MinimumProductionQuantity,
    string Units,
    int ProductionTimeInHours,
    int ShelfLife,
    List<string>? Instructions,
    List<IngredientRequest> Ingredients);
    
    public record IngredientRequest(
        Guid MaterialId,
        double Quantity,
        string Units);