using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Tebipaints.Application.Products.AssignFormulation;
using Tebipaints.Application.Products.CreateProduct;
using Tebipaints.Application.Products.DiscontinueProduct;
using Tebipaints.Application.Products.ListProductsQuery;
using Tebipaints.Application.Products.UpdatePrice;

namespace Tebipaints.Api.Controllers.Products;

[ApiController]
[Route("api/products")]
public class ProductsController : ControllerBase
{
    private readonly ISender _sender;

    public ProductsController(ISender sender)
    {
        _sender = sender;
    }

    [HttpGet]
    public async Task<IActionResult> GetAllProducts()
    {
        var query = new ListProductsQuery();
        var result = await _sender.Send(query);
        return Ok(result.Value);
    }

    [HttpPost]
    public async Task<IActionResult> CreateProduct([FromBody] CreateProductRequest request)
    {
        var command = new CreateProductCommand(
            request.ProductName,
            request.Color,
            request.Volume,
            request.ProductType,
            request.Unit);
        
        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }
        
        return CreatedAtAction(nameof(GetProduct), new { id = result.Value }, result.Value);
    }

    [HttpPost("{id:guid}/formulation")]
    public async Task<IActionResult> AssignFormulation(Guid id, [FromBody] AssignFormulationRequest request)
    {
        var command = new AssignFormulationCommand(
            id,
            request.FormulationId);
        
        var result = await _sender.Send(command);
        
        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpPost("{id:guid}/discontinue")]
    public async Task<IActionResult> DiscontinueProduct(Guid id)
    {
        var command = new DiscontinueProductCommand(id);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpPut("{id:guid}/variants/{variantId:guid}/price")]
    public async Task<IActionResult> UpdatePrice(
        Guid id, 
        Guid variantId, 
        [FromBody] UpdatePriceRequest request)
    {
        var command = new UpdatePriceCommand(
            id,
            variantId,
            request.Price);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetProduct(Guid id)
    {
        // Placeholder for the GET endpoint
        // This is needed for the CreatedAtAction to work
        throw new NotImplementedException();
    }
    
    [HttpGet("{id:guid}/variants/{variantId:guid}")]
    public async Task<IActionResult> GetVariant(Guid id, Guid variantId)
    {
        // Placeholder for the GET endpoint
        // This is needed for the CreatedAtAction to work
        throw new NotImplementedException();
    }
}