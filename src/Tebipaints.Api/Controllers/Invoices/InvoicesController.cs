using MediatR;
using Microsoft.AspNetCore.Mvc;
using Tebipaints.Application.Invoices.IssueRefund;
using Tebipaints.Application.Invoices.MakePayment;

namespace Tebipaints.Api.Controllers.Invoices;
[ApiController]
[Route("api/invoices")]
public class InvoicesController : ControllerBase
{
    private readonly ISender _sender;

    public InvoicesController(ISender sender)
    {
        _sender = sender;
    }
    
    [HttpPost("{id:guid}/refunds")]
    public async Task<IActionResult> IssueRefund(
        Guid id, 
        [FromBody] IssueRefundRequest request)
    {
        var command = new IssueRefundCommand(
            id,
            request.Amount,
            request.Reason);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return CreatedAtAction(
            nameof(GetRefund), 
            new { id = id, refundId = result.Value }, 
            result.Value);
    }
    
    [HttpPost("{id:guid}/payments")]
    public async Task<IActionResult> MakePayment(
        Guid id, 
        [FromBody] MakePaymentRequest request)
    {
        var command = new MakePaymentCommand(
            id,
            request.Amount,
            request.PaymentMethod,
            request.Reference);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return CreatedAtAction(
            nameof(GetPayment), 
            new { id = id, paymentId = result.Value }, 
            result.Value);
    }
    
    [HttpGet("{id:guid}/refunds/{refundId:guid}")]
    public async Task<IActionResult> GetRefund(Guid id, Guid refundId)
    {
        // Placeholder for the GET endpoint
        // This is needed for the CreatedAtAction to work
        throw new NotImplementedException();
    }

    [HttpGet("{id:guid}/payments/{paymentId:guid}")]
    public async Task<IActionResult> GetPayment(Guid id, Guid paymentId)
    {
        // Placeholder for the GET endpoint
        // This is needed for the CreatedAtAction to work
        throw new NotImplementedException();
    }
}
