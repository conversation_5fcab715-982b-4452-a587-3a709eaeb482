namespace Tebipaints.Api.Controllers.Procurements;

public record RenewContractRequest(
    DateTime NewStartDate,
    DateTime NewEndDate,
    List<UpdateContractTermRequest> UpdatedTerms,
    List<UpdateContractedMaterialRequest> UpdatedContractedMaterials);

public record UpdateContractTermRequest(
    string Description,
    string Type,
    List<string> Values,
    DateTime? ExpirationDate);

public record UpdateContractedMaterialRequest(
    Guid MaterialId,
    decimal NewUnitPrice,
    decimal? NewMinimumOrder,
    decimal? NewMaximumOrder,
    int? NewLeadTimeDays);