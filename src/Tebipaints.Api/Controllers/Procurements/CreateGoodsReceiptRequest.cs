namespace Tebipaints.Api.Controllers.Procurements;

public record CreateGoodsReceiptRequest(
    Guid PurchaseOrderId,
    string ReceivedBy,
    string? DeliveryNote,
    string? SupplierReference,
    DateTime? ReceivedDate,
    List<CreateGoodsReceiptItemRequest> Items);

public record CreateGoodsReceiptItemRequest(
    Guid MaterialId,
    string MaterialName,
    decimal OrderedQuantityValue,
    string OrderedQuantityUnit,
    decimal ReceivedQuantityValue,
    string ReceivedQuantityUnit,
    decimal UnitPriceAmount,
    string UnitPriceCurrency,
    string? Notes,
    string QualityStatus = "Pending");
