namespace Tebipaints.Api.Controllers.Procurements;

public record CreatePurchaseInvoiceRequest(
    string SupplierInvoiceNumber,
    Guid PurchaseOrderId,
    Guid SupplierId,
    DateTime InvoiceDate,
    DateTime DueDate,
    decimal TotalAmount,
    string TotalCurrency,
    string? Notes,
    List<CreatePurchaseInvoiceItemRequest> Items);

public record CreatePurchaseInvoiceItemRequest(
    Guid MaterialId,
    string MaterialName,
    decimal InvoicedQuantityValue,
    string InvoicedQuantityUnit,
    decimal UnitPriceAmount,
    string UnitPriceCurrency,
    string? Notes);
