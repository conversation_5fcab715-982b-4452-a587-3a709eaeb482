using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Tebipaints.Application.Procurement.PurchaseOrders.AddPurchaseOrderItem;
using Tebipaints.Application.Procurement.PurchaseOrders.ApprovePurchaseOrder;
using Tebipaints.Application.Procurement.PurchaseOrders.CancelPurchaseOrder;
using Tebipaints.Application.Procurement.PurchaseOrders.ClosePurchaseOrder;
using Tebipaints.Application.Procurement.PurchaseOrders.CreatePurchaseOrder;
using Tebipaints.Application.Procurement.PurchaseOrders.GeneratePurchaseOrderPdf;
using Tebipaints.Application.Procurement.PurchaseOrders.GetPurchaseOrder;
using Tebipaints.Application.Procurement.PurchaseOrders.ListPurchaseOrders;
using Tebipaints.Application.Procurement.PurchaseOrders.RecordMaterialReceipt;
using Tebipaints.Application.Procurement.PurchaseOrders.RemovePurchaseOrderItem;
using Tebipaints.Application.Procurement.Suppliers.ActivateContract;
using Tebipaints.Application.Procurement.Suppliers.AddSupplierContract;
using Tebipaints.Application.Procurement.Suppliers.CreateSupplier;
using Tebipaints.Application.Procurement.Suppliers.DeactivateSupplier;
using Tebipaints.Application.Procurement.Suppliers.GetSupplier;
using Tebipaints.Application.Procurement.Suppliers.ListSuppliers;
using Tebipaints.Application.Procurement.Suppliers.RenewContract;
using Tebipaints.Application.Procurement.Suppliers.TerminateContract;
using Tebipaints.Application.Procurement.GoodsReceipts.CreateGoodsReceipt;
using Tebipaints.Application.Procurement.GoodsReceipts.ConfirmGoodsReceipt;
using Tebipaints.Application.Procurement.GoodsReceipts.GenerateGoodsReceiptPdf;
using Tebipaints.Application.Procurement.GoodsReceipts.ListGoodsReceipts;
using Tebipaints.Application.Procurement.GoodsReceipts.GetGoodsReceipt;
using Tebipaints.Application.Procurement.PurchaseInvoices.CreatePurchaseInvoice;
using Tebipaints.Application.Procurement.PurchaseInvoices.ApprovePurchaseInvoice;
using Tebipaints.Application.Procurement.PurchaseInvoices.ListPurchaseInvoices;
using Tebipaints.Application.Procurement.PurchaseInvoices.PerformThreeWayMatching;
using Tebipaints.Application.Procurement.PurchaseInvoices.MarkForReview;
using Tebipaints.Application.Procurement.Metrics.GetProcurementMetrics;
using Tebipaints.Application.Procurement.PurchaseOrders.GetPurchaseOrderActivities;

namespace Tebipaints.Api.Controllers.Procurements;
[ApiController]
[Route("api/procurements")]
public class ProcurementsController : ControllerBase
{
    private readonly ISender _sender;

    public ProcurementsController(ISender sender)
    {
        _sender = sender;
    }
    
    [HttpGet("purchase-orders/{id:guid}")]
    public async Task<IActionResult> GetPurchaseOrder(Guid id)
    {
        var query = new GetPurchaseOrderQuery(id);
        var result = await _sender.Send(query);

        return Ok(result.Value);
    }
    
    [HttpGet("purchase-orders")]
    public async Task<IActionResult> GetAllPurchaseOrders()
    {
        var query = new ListPurchaseOrdersQuery();
        var result = await _sender.Send(query);
        
        return Ok(result.Value);
    }

    [HttpPost("purchase-orders")]
    public async Task<IActionResult> CreatePurchaseOrder([FromBody] CreatePurchaseOrderRequest request)
    {
        var command = new CreatePurchaseOrderCommand(
            request.SupplierId,
            request.RequestBy,
            request.PromiseDate,
            request.DiscountPercent,
            request.FreightChargeAmount,
            request.TaxRate,
            request.PurchaseOrderCurrency,
            request.Items.ConvertAll(i => new CreatePurchaseOrderItemCommand(
                i.MaterialId,
                i.MaterialName,
                i.Quantity,
                i.Units,
                i.UnitPrice,
                i.Notes)));
        
        var result = await _sender.Send(command);
        
        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return CreatedAtAction(
            nameof(GetPurchaseOrder), 
            new { id = result.Value }, 
            result.Value);
    }
    
    [HttpPost("purchase-orders/{id:guid}/items")]
    public async Task<IActionResult> AddPurchaseOrderItem(
        Guid id, 
        [FromBody] AddPurchaseOrderItemRequest request)
    {
        var command = new AddPurchaseOrderItemCommand(
            id,
            request.MaterialId,
            request.MaterialName,
            request.Quantity,
            request.Unit,
            request.UnitPrice,
            request.Notes);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpDelete("purchase-orders/{id:guid}/items")]
    public async Task<IActionResult> RemovePurchaseOrderItem(Guid id, RemovePurchaseOrderItemRequest request)
    {
        var command = new RemovePurchaseOrderItemCommand(id, request.MaterialId);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return NoContent();
    }
    
    [HttpPost("purchase-orders/{id:guid}/approve")]
    public async Task<IActionResult> ApprovePurchaseOrder(Guid id, ApprovePurchaseOrderRequest request)
    {
        var command = new ApprovePurchaseOrderCommand(id, request.ApprovedBy);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpPost("purchase-orders/{id:guid}/cancel")]
    public async Task<IActionResult> CancelPurchaseOrder(Guid id, CancelPurchaseOrderRequest request)
    {
        var command = new CancelPurchaseOrderCommand(id, request.Reason);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpPost("purchase-orders/{id:guid}/close")]
    public async Task<IActionResult> ClosePurchaseOrder(Guid id)
    {
        var command = new ClosePurchaseOrderCommand(id);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpPost("purchase-orders/{id:guid}/receipts")]
    public async Task<IActionResult> RecordMaterialReceipt(
        Guid id, 
        [FromBody] RecordMaterialReceiptRequest request)
    {
        var command = new RecordMaterialReceiptCommand(
            id,
            request.MaterialId,
            request.ReceivedQuantity,
            request.Unit);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpGet("purchase-orders/{id:guid}/pdf")]
    public async Task<IActionResult> GeneratePurchaseOrderDocument(Guid id)
    {
        var query = new GeneratePurchaseOrderPdfQuery(id);
        var result = await _sender.Send(query);
        
        if (result.IsFailure)
            return NotFound();

        var stream = new MemoryStream(result.Value);

        return File(stream, "application/pdf", $"purchase-order-{id}.pdf");
    }
    
    [HttpGet("suppliers/{id:guid}")]
    public async Task<IActionResult> GetSupplier(Guid id)
    {
        var query = new GetSupplierQuery(id);
        var result = await _sender.Send(query);

        return Ok(result.Value);
    }

    [HttpGet("suppliers/{id:guid}/contracts/{contractId:guid}")]
    public async Task<IActionResult> GetSupplierContract(Guid id, Guid contractId)
    {
        // Placeholder for the GET endpoint
        // This is needed for the CreatedAtAction to work
        throw new NotImplementedException();
    }

    [HttpGet("suppliers")]
    public async Task<IActionResult> GetAllSuppliers()
    {
        var query = new ListSuppliersQuery();
        var result = await _sender.Send(query);
        
        return Ok(result.Value);
    }
    
    [HttpPost("suppliers")]
    public async Task<IActionResult> CreateSupplier([FromBody] CreateSupplierRequest request)
    {
        var command = new CreateSupplierCommand(
            request.Code,
            request.Name,
            request.ContactPerson,
            request.Email,
            request.PhoneNumber,
            request.Address);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return CreatedAtAction(
            nameof(GetSupplier), 
            new { id = result.Value }, 
            result.Value);
    }
    
    [HttpPost("suppliers/{id:guid}/contracts")]
    public async Task<IActionResult> AddSupplierContract(
        Guid id, 
        [FromBody] AddSupplierContractRequest request)
    {
        var command = new AddSupplierContractCommand(
            id,
            request.StartDate,
            request.EndDate,
            request.Terms.ConvertAll(t => new AddContractTermCommand(
                t.Description,
                t.Type,
                t.Values,
                t.ExpirationDate)),
            request.Materials.ConvertAll(m => new AddContractedMaterialCommand(
                m.MaterialId,
                m.MaterialName,
                m.UnitPrice,
                m.MinimumOrder,
                m.MinimumOrderUnit,
                m.MaximumOrder,
                m.MaximumOrderUnit,
                m.LeadTimeDays)));

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return CreatedAtAction(
            nameof(GetSupplierContract), 
            new { id = id, contractId = result.Value }, 
            result.Value);
    }
    
    [HttpPost("suppliers/{id:guid}/contracts/{contractId:guid}/activate")]
    public async Task<IActionResult> ActivateContract(Guid id, Guid contractId)
    {
        var command = new ActivateContractCommand(contractId);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpPost("suppliers/{id:guid}/contracts/{contractId:guid}/renew")]
    public async Task<IActionResult> RenewContract(
        Guid id, 
        Guid contractId, 
        [FromBody] RenewContractRequest request)
    {
        var command = new RenewContractCommand(
            contractId,
            request.NewEndDate,
            request.NewEndDate,
            request.UpdatedTerms.ConvertAll(t => new UpdateContractTermCommand(
                t.Description,
                t.Type,
                t.Values,
                t.ExpirationDate)),
            request.UpdatedContractedMaterials.ConvertAll(m => new UpdateContractedMaterialCommand(
                m.MaterialId,
                m.NewUnitPrice,
                m.NewMinimumOrder,
                m.NewMaximumOrder,
                m.NewLeadTimeDays)));

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpPost("suppliers/{id:guid}/contracts/{contractId:guid}/terminate")]
    public async Task<IActionResult> TerminateContract(
        Guid id, 
        Guid contractId, 
        [FromBody] TerminateContractRequest request)
    {
        var command = new TerminateContractCommand(
            contractId,
            request.Reason);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }
    
    [HttpPost("suppliers/{id:guid}/deactivate")]
    public async Task<IActionResult> DeactivateSupplier(
        Guid id, 
        [FromBody] DeactivateSupplierRequest request)
    {
        var command = new DeactivateSupplierCommand(
            id,
            request.Reason);

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }

    // Goods Receipt Endpoints
    
    [HttpGet("goods-receipt/{id:guid}/pdf")]
    public async Task<IActionResult> GenerateGoodsReceiptDocument(Guid id)
    {
        var query = new GenerateGoodsReceiptPdfQuery(id);
        var result = await _sender.Send(query);
        
        if (result.IsFailure)
            return NotFound();

        var stream = new MemoryStream(result.Value);

        return File(stream, "application/pdf", $"goods-receipt-{id}.pdf");
    }

    [HttpGet("goods-receipts/{id:guid}")]
    public async Task<IActionResult> GetGoodsReceipt(Guid id)
    {
        var query = new GetGoodsReceiptQuery { GoodsReceiptId = id };
        var result = await _sender.Send(query);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok(result.Value);
    }

    [HttpGet("goods-receipts")]
    public async Task<IActionResult> GetGoodsReceipts(
        [FromQuery] Guid? purchaseOrderId,
        [FromQuery] Guid? supplierId,
        [FromQuery] string? status,
        [FromQuery] DateTime? fromDate,
        [FromQuery] DateTime? toDate,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50)
    {
        var query = new ListGoodsReceiptsQuery
        {
            PurchaseOrderId = purchaseOrderId,
            SupplierId = supplierId,
            Status = status,
            FromDate = fromDate,
            ToDate = toDate,
            PageNumber = pageNumber,
            PageSize = pageSize
        };

        var result = await _sender.Send(query);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok(result.Value);
    }

    [HttpPost("goods-receipts")]
    public async Task<IActionResult> CreateGoodsReceipt([FromBody] CreateGoodsReceiptRequest request)
    {
        var command = new CreateGoodsReceiptCommand
        {
            PurchaseOrderId = request.PurchaseOrderId,
            ReceivedBy = request.ReceivedBy,
            DeliveryNote = request.DeliveryNote,
            SupplierReference = request.SupplierReference,
            ReceivedDate = request.ReceivedDate,
            Items = request.Items.ConvertAll(i => new CreateGoodsReceiptItemCommand
            {
                MaterialId = i.MaterialId,
                MaterialName = i.MaterialName,
                OrderedQuantityValue = i.OrderedQuantityValue,
                OrderedQuantityUnit = i.OrderedQuantityUnit,
                ReceivedQuantityValue = i.ReceivedQuantityValue,
                ReceivedQuantityUnit = i.ReceivedQuantityUnit,
                UnitPriceAmount = i.UnitPriceAmount,
                UnitPriceCurrency = i.UnitPriceCurrency,
                Notes = i.Notes,
                QualityStatus = i.QualityStatus
            })
        };

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return CreatedAtAction(
            nameof(GetGoodsReceipt),
            new { id = result.Value },
            result.Value);
    }

    [HttpPost("goods-receipts/{id:guid}/confirm")]
    public async Task<IActionResult> ConfirmGoodsReceipt(Guid id, [FromBody] ConfirmGoodsReceiptRequest request)
    {
        var command = new ConfirmGoodsReceiptCommand { GoodsReceiptId = id };

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }

    // Purchase Invoice Endpoints

    [HttpGet("purchase-invoices/{id:guid}")]
    public async Task<IActionResult> GetPurchaseInvoice(Guid id)
    {
        // Placeholder for the GET endpoint
        // This is needed for the CreatedAtAction to work
        throw new NotImplementedException();
    }

    [HttpGet("purchase-invoices")]
    public async Task<IActionResult> GetPurchaseInvoices(
        [FromQuery] Guid? purchaseOrderId,
        [FromQuery] Guid? supplierId,
        [FromQuery] string? status,
        [FromQuery] DateTime? fromDate,
        [FromQuery] DateTime? toDate,
        [FromQuery] DateTime? dueDateFrom,
        [FromQuery] DateTime? dueDateTo,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50)
    {
        var query = new ListPurchaseInvoicesQuery
        {
            PurchaseOrderId = purchaseOrderId,
            SupplierId = supplierId,
            Status = status,
            FromDate = fromDate,
            ToDate = toDate,
            DueDateFrom = dueDateFrom,
            DueDateTo = dueDateTo,
            PageNumber = pageNumber,
            PageSize = pageSize
        };

        var result = await _sender.Send(query);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok(result.Value);
    }

    [HttpPost("purchase-invoices")]
    public async Task<IActionResult> CreatePurchaseInvoice([FromBody] CreatePurchaseInvoiceRequest request)
    {
        var command = new CreatePurchaseInvoiceCommand
        {
            SupplierInvoiceNumber = request.SupplierInvoiceNumber,
            PurchaseOrderId = request.PurchaseOrderId,
            SupplierId = request.SupplierId,
            InvoiceDate = request.InvoiceDate,
            DueDate = request.DueDate,
            TotalAmount = request.TotalAmount,
            TotalCurrency = request.TotalCurrency,
            Notes = request.Notes,
            Items = request.Items.ConvertAll(i => new CreatePurchaseInvoiceItemCommand
            {
                MaterialId = i.MaterialId,
                MaterialName = i.MaterialName,
                InvoicedQuantityValue = i.InvoicedQuantityValue,
                InvoicedQuantityUnit = i.InvoicedQuantityUnit,
                UnitPriceAmount = i.UnitPriceAmount,
                UnitPriceCurrency = i.UnitPriceCurrency,
                Notes = i.Notes
            })
        };

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return CreatedAtAction(
            nameof(GetPurchaseInvoice),
            new { id = result.Value },
            result.Value);
    }

    [HttpPost("purchase-invoices/{id:guid}/approve")]
    public async Task<IActionResult> ApprovePurchaseInvoice(Guid id, [FromBody] ApprovePurchaseInvoiceRequest request)
    {
        var command = new ApprovePurchaseInvoiceCommand
        {
            PurchaseInvoiceId = id,
            ApprovedBy = request.ApprovedBy
        };

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }

    [HttpGet("purchase-invoices/{id:guid}/three-way-matching")]
    public async Task<IActionResult> PerformThreeWayMatching(Guid id)
    {
        var query = new PerformThreeWayMatchingQuery { PurchaseInvoiceId = id };

        var result = await _sender.Send(query);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok(result.Value);
    }

    [HttpPost("purchase-invoices/{id:guid}/mark-for-review")]
    public async Task<IActionResult> MarkPurchaseInvoiceForReview(Guid id, [FromBody] MarkPurchaseInvoiceForReviewRequest request)
    {
        var command = new MarkPurchaseInvoiceForReviewCommand
        {
            PurchaseInvoiceId = id,
            Reason = request.Reason
        };

        var result = await _sender.Send(command);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok();
    }

    [HttpGet("metrics")]
    public async Task<IActionResult> GetProcurementMetrics(
        [FromQuery] DateTime? fromDate,
        [FromQuery] DateTime? toDate,
        [FromQuery] Guid? supplierId,
        [FromQuery] string? currency = "GHS")
    {
        var query = new GetProcurementMetricsQuery
        {
            FromDate = fromDate,
            ToDate = toDate,
            SupplierId = supplierId,
            Currency = currency
        };

        var result = await _sender.Send(query);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok(result.Value);
    }

    [HttpGet("purchase-orders/{id:guid}/activities")]
    public async Task<IActionResult> GetPurchaseOrderActivities(
        Guid id,
        [FromQuery] DateTime? fromDate,
        [FromQuery] DateTime? toDate,
        [FromQuery] string[]? activityTypes,
        [FromQuery] bool includeSystemActivities = true,
        [FromQuery] int? limit = null)
    {
        var query = new GetPurchaseOrderActivitiesQuery
        {
            PurchaseOrderId = id,
            FromDate = fromDate,
            ToDate = toDate,
            ActivityTypes = activityTypes?.ToList(),
            IncludeSystemActivities = includeSystemActivities,
            Limit = limit
        };

        var result = await _sender.Send(query);

        if (result.IsFailure)
        {
            return BadRequest(result.Error);
        }

        return Ok(result.Value);
    }
}