namespace Tebipaints.Api.Controllers.Procurements;

public record AddSupplierContractRequest(
    DateTime StartDate,
    DateTime EndDate,
    List<AddContractTermRequest> Terms,
    List<AddContractedMaterialRequest> Materials);

public record AddContractTermRequest(
    string Description,
    string Type,
    List<string> Values,
    DateTime ExpirationDate);

public record AddContractedMaterialRequest(
    Guid MaterialId,
    string MaterialName,
    decimal UnitPrice,
    decimal MinimumOrder,
    string MinimumOrderUnit,
    decimal? MaximumOrder,
    string? MaximumOrderUnit,
    int LeadTimeDays);