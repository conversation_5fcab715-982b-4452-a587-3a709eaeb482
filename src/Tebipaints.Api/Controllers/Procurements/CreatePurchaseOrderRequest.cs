namespace Tebipaints.Api.Controllers.Procurements;

public record CreatePurchaseOrderRequest(
    Guid SupplierId,
    string RequestBy,
    DateTime PromiseDate,
    decimal DiscountPercent,
    decimal FreightChargeAmount,
    decimal TaxRate,
    string PurchaseOrderCurrency,
    List<CreatePurchaseOrderItemRequest> Items);

public record CreatePurchaseOrderItemRequest(
    Guid MaterialId,
    string MaterialName,
    decimal Quantity,
    string Units,
    decimal UnitPrice,
    string? Notes);