FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base

USER app
WORKDIR /app
EXPOSE 8080
EXPOSE 8081


FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["src/Tebipaints.Api/Tebipaints.Api.csproj", "src/Tebipaints.Api/"]
COPY ["src/Tebipaints.Application/Tebipaints.Application.csproj", "src/Tebipaints.Application/"]
COPY ["src/Tebipaints.Domain/Tebipaints.Domain.csproj", "src/Tebipaints.Domain/"]
COPY ["src/Tebipaints.Infrastructure/Tebipaints.Infrastructure.csproj", "src/Tebipaints.Infrastructure/"]
COPY ["src/Tebipaints.Templates/Tebipaints.Templates.csproj", "src/Tebipaints.Templates/"]
RUN dotnet restore "src/Tebipaints.Api/Tebipaints.Api.csproj"
COPY . .
WORKDIR "/src/src/Tebipaints.Api"
RUN dotnet build "Tebipaints.Api.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "Tebipaints.Api.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .


ENTRYPOINT ["dotnet", "Tebipaints.Api.dll"]
