using System.Data;
using Bogus;
using Dapper;
using Tebipaints.Application.Abstractions.Data;
using Tebipaints.Domain.Shared;

namespace Tebipaints.Api.Extensions;

public static class SeedDataExtensions
{
    private static int _contractSequence = 0;
    private static int _skuSequence = 0;
    private static readonly (string Name, string Unit, string Type)[] PaintMaterials = new[]
    {
        // Pigments and Colorants
        ("Titanium Dioxide", "KG", "Pigment"),
        ("Iron Oxide Red", "KG", "Pigment"),
        ("Carbon Black", "KG", "Pigment"),
        ("Phthalocyanine Blue", "KG", "Pigment"),
        
        // Binders and Resins
        ("Acrylic Resin", "KG", "Resin"),
        ("Alkyd Resin", "KG", "Resin"),
        ("Polyurethane Resin", "KG", "Resin"),
        ("Epoxy Resin", "KG", "Resin"),
        
        // Solvents
        ("Mineral Spirits", "L", "Solvent"),
        ("Xylene", "L", "Solvent"),
        ("Butyl Acetate", "L", "Solvent"),
        
        // Additives
        ("Genapol (Surfactant)", "KG", "Additive"),
        ("Anti-Foaming Agent", "KG", "Additive"),
        ("UV Stabilizer", "KG", "Additive"),
        ("Dispersing Agent", "KG", "Additive"),
        
        // Fillers
        ("River Sand", "KG", "Filler"),
        ("Calcium Carbonate", "KG", "Filler"),
        ("Talc Powder", "KG", "Filler"),
        ("Silica Powder", "KG", "Filler"),
        ("Mica Powder", "KG", "Filler")
    };
    public static void SeedData(this IApplicationBuilder app)
    {
        using var scope = app.ApplicationServices.CreateScope();

        var sqlConnectionFactory = scope.ServiceProvider.GetRequiredService<ISqlConnectionFactory>();
        using var connection = sqlConnectionFactory.CreateConnection();

        var faker = new Faker();

        //var formulationInfos = GetFormulationInfos(connection);
        //var materialInfos = GetMaterialInfos(connection);

        // Complete Procurement-to-Payment Lifecycle Seeding (includes materials and suppliers)
        // SeedCompleteProcurementLifecycle(connection);

        // Seed realistic material inventory data based on procurement data
        // SeedRealisticMaterialInventory(connection);

        // Seed realistic formulations using materials from procurement data
        SeedRealisticFormulations(connection);

        //SeedProductsAndVariants(connection, formulationInfos);
        //SeedProductionLines(connection);
        //SeedWorkOrders(connection);
        //SeedProductInventoriesAndStockLevels(connection);
        //SeedInvoices(connection);
    }

    private static void SeedInvoices(IDbConnection connection)
    {
        var faker = new Faker();
        // 1. Fetch products for line items
        var products = connection.Query<(Guid Id, string Name, decimal Price, string Currency)>(
            "SELECT id, name_value, price_amount, price_currency FROM products WHERE status = 'Active'").ToList();
        if (!products.Any()) return;

        // 2. Optionally fetch customer IDs (if available)
        var customerIds = connection.Query<Guid>("SELECT id FROM customers").ToList();

        // 3. Seed 20 invoices
        var invoiceIds = new List<Guid>();
        for (int i = 0; i < 20; i++)
        {
            var invoiceId = Guid.NewGuid();
            invoiceIds.Add(invoiceId);
            var isWalkIn = faker.Random.Bool(0.5f) || !customerIds.Any();
            var customerId = isWalkIn ? (Guid?)null : faker.PickRandom(customerIds);
            var walkInName = isWalkIn ? faker.Name.FullName() : null;
            var createdOn = faker.Date.Between(DateTime.UtcNow.AddMonths(-3), DateTime.UtcNow);
            var dueDate = createdOn.AddDays(faker.Random.Int(7, 30));

            // 4. Add 1-5 invoice lines
            var numLines = faker.Random.Int(1, 5);
            var lines = new List<dynamic>();
            decimal subtotal = 0;
            for (int l = 0; l < numLines; l++)
            {
                var prod = faker.PickRandom(products);
                var qty = faker.Random.Int(1, 10);
                var unitPrice = prod.Price;
                var discount = faker.Random.Bool(0.3f) ? Math.Round(unitPrice * faker.Random.Decimal(0.05m, 0.15m), 2) : 0m;
                var lineTotal = (unitPrice - discount) * qty;
                subtotal += lineTotal;
                lines.Add(new
                {
                    Id = Guid.NewGuid(),
                    ProductId = prod.Id,
                    SkuValue = $"SKU-{faker.Random.AlphaNumeric(6).ToUpper()}",
                    Description = prod.Name,
                    UnitPriceAmount = unitPrice,
                    UnitPriceCurrency = prod.Currency,
                    LineItemDiscountAmount = discount,
                    LineItemDiscountCurrency = prod.Currency,
                    Quantity = qty,
                    InvoiceId = invoiceId
                });
            }
            var tax = Math.Round(subtotal * 0.12m, 2); // 12% VAT
            var grandTotal = subtotal + tax;
            var paid = faker.Random.Bool(0.7f) ? grandTotal : faker.Random.Decimal(0, grandTotal);
            var due = grandTotal - paid;

            // 5. Insert invoice
            connection.Execute(
                "INSERT INTO invoices (id, customer_id, walk_in_customer_name, invoice_number_value, subtotal_amount, subtotal_currency, tax_amount, tax_currency, grand_total_amount, grand_total_currency, amount_paid_amount, amount_paid_currency, amount_due_amount, amount_due_currency, due_date, created_on_utc, status) " +
                "VALUES (@Id, @CustomerId, @WalkInName, @InvoiceNumber, @Subtotal, @Currency, @Tax, @Currency, @GrandTotal, @Currency, @Paid, @Currency, @Due, @Currency, @DueDate, @CreatedOn, @Status);",
                new
                {
                    Id = invoiceId,
                    CustomerId = customerId,
                    WalkInName = walkInName,
                    InvoiceNumber = $"INV-{createdOn:yyyyMMdd}-{i + 1:D4}",
                    Subtotal = subtotal,
                    Currency = "USD",
                    Tax = tax,
                    GrandTotal = grandTotal,
                    Paid = paid,
                    Due = due,
                    DueDate = dueDate,
                    CreatedOn = createdOn,
                    Status = due == 0 ? "Paid" : (paid > 0 ? "PartiallyPaid" : "Unpaid")
                }
            );

            // 6. Insert invoice lines
            foreach (var line in lines)
            {
                // connection.Execute(
                //     "INSERT INTO invoice_lines (id, product_id, sku_value, description, unit_price_amount, unit_price_currency, line_item_discount_amount, line_item_discount_currency, quantity, invoice_id) " +
                //     "VALUES (@Id, @ProductId, @SkuValue, @Description, @UnitPriceAmount, @UnitPriceCurrency, @LineItemDiscountAmount, @LineItemDiscountCurrency, @Quantity, @InvoiceId);",
                //     line
                // );
            }

            // 7. Insert 0-2 payments
            var numPayments = faker.Random.Int(0, 2);
            decimal paidSoFar = 0;
            for (int p = 0; p < numPayments && paidSoFar < grandTotal; p++)
            {
                var paymentAmount = Math.Min(Math.Round(faker.Random.Decimal(1, grandTotal - paidSoFar), 2), grandTotal - paidSoFar);
                paidSoFar += paymentAmount;
                var paymentDate = createdOn.AddDays(faker.Random.Int(0, 30));
                connection.Execute(
                    "INSERT INTO invoice_payments (id, invoice_id, amount_amount, amount_currency, date, payment_method, reference, receipt_number_value, receipt_invoice_id, receipt_issued_date, receipt_amount_amount, receipt_amount_currency) " +
                    "VALUES (@Id, @InvoiceId, @Amount, @Currency, @Date, @Method, @Reference, @ReceiptNumber, @ReceiptInvoiceId, @ReceiptIssuedDate, @ReceiptAmount, @ReceiptCurrency);",
                    new
                    {
                        Id = Guid.NewGuid(),
                        InvoiceId = invoiceId,
                        Amount = paymentAmount,
                        Currency = "USD",
                        Date = paymentDate,
                        Method = faker.PickRandom(new[] { "Cash", "Card", "Transfer" }),
                        Reference = faker.Random.AlphaNumeric(10).ToUpper(),
                        ReceiptNumber = $"RCPT-{faker.Random.AlphaNumeric(6).ToUpper()}",
                        ReceiptInvoiceId = invoiceId,
                        ReceiptIssuedDate = paymentDate,
                        ReceiptAmount = paymentAmount,
                        ReceiptCurrency = "USD"
                    }
                );
            }
        }

        // 8. Insert 2-3 refunds for random invoices
        var refundInvoices = invoiceIds.OrderBy(_ => faker.Random.Int()).Take(3).ToList();
        foreach (var invId in refundInvoices)
        {
            var refundAmount = Math.Round(faker.Random.Decimal(5, 50), 2);
            var refundDate = DateTime.UtcNow.AddDays(-faker.Random.Int(0, 30));
            connection.Execute(
                "INSERT INTO invoice_refunds (id, invoice_id, amount_amount, amount_currency, reason, refund_date, status, processed_date) " +
                "VALUES (@Id, @InvoiceId, @Amount, @Currency, @Reason, @RefundDate, @Status, @ProcessedDate);",
                new
                {
                    Id = Guid.NewGuid(),
                    InvoiceId = invId,
                    Amount = refundAmount,
                    Currency = "USD",
                    Reason = faker.PickRandom(new[] { "Customer Return", "Overpayment", "Product Defect" }),
                    RefundDate = refundDate,
                    Status = "Processed",
                    ProcessedDate = refundDate.AddDays(1)
                }
            );
        }
    }

    private static void SeedRealisticMaterialInventory(IDbConnection connection)
    {
        Console.WriteLine("📦 Seeding realistic material inventory data based on procurement history...");

        var faker = new Faker();

        // 0. Clean up existing inventory data first
        CleanupInventoryData(connection);

        // 1. Get inventory locations (we create individual inventories for each material-location combination)
        var inventoryLocations = GetInventoryLocations();

        // 2. Get all materials from procurement data
        var materials = GetMaterialsFromProcurement(connection);
        if (!materials.Any())
        {
            Console.WriteLine("❌ No materials found from procurement data. Cannot seed inventory.");
            return;
        }

        // 3. Create material inventories for each location
        var materialInventories = CreateMaterialInventories(connection, materials, inventoryLocations, faker);

        // 4. Seed historical transactions based on goods receipts
        SeedHistoricalTransactionsFromGoodsReceipts(connection, materialInventories, faker);

        // 5. Seed production consumption transactions
        SeedProductionConsumptionTransactions(connection, materialInventories, faker);

        // 6. Seed stock adjustments and transfers
        SeedStockAdjustmentsAndTransfers(connection, materialInventories, faker);

        // 7. Create realistic reservations for upcoming production
        SeedProductionReservations(connection, materialInventories, faker);

        Console.WriteLine($"✅ Successfully seeded inventory for {materials.Count} materials across {inventoryLocations.Count} locations");
    }

    private static void SeedProductInventoriesAndStockLevels(IDbConnection connection)
    {
        var faker = new Faker();
        // 1. Seed 2 locations
        var locations = new[] { "Factory", "SalesPoint" };
        var locationIds = new Dictionary<string, Guid>();
        foreach (var loc in locations)
        {
            var id = Guid.NewGuid();
            locationIds[loc] = id;
            connection.Execute(
                "INSERT INTO inventory_int (id, location) VALUES (@Id, @Location) ON CONFLICT DO NOTHING;",
                new { Id = id, Location = loc }
            );
        }

        // 2. Fetch products and variants
        var products = connection.Query<(Guid Id, string Name)>("SELECT id, name_value FROM products WHERE status = 'Active'").ToList();
        var variants = connection.Query<(Guid Id, Guid ProductId, string Sku, string Color, decimal Volume, string Unit)>(
            "SELECT id, product_id, sku_value, color_value, volume_value, volume_unit FROM product_variants WHERE status = 'Active'").ToList();

        // 3. For each location, create 3 product inventories
        var rand = new Random();
        foreach (var (loc, locId) in locationIds)
        {
            var productSample = products.OrderBy(_ => rand.Next()).Take(3).ToList();
            foreach (var prod in productSample)
            {
                // Insert into product_inventories: id = locId, product_id = prod.Id
                connection.Execute(
                    "INSERT INTO product_inventories (id, product_id) VALUES (@InventoryId, @ProductId) ON CONFLICT DO NOTHING;",
                    new { InventoryId = locId, ProductId = prod.Id }
                );

                // 4. For each inventory, create 2-3 stock levels for random variants of this product
                var prodVariants = variants.Where(v => v.ProductId == prod.Id).OrderBy(_ => rand.Next()).Take(3).ToList();
                foreach (var variant in prodVariants)
                {
                    var stockLevelId = Guid.NewGuid();
                    var packagingType = faker.PickRandom(new[] { "Bucket" });
                    var packagingCapacity = faker.PickRandom(new[] { 10m, 20m });
                    var packagingUnit = "L";
                    var stockLevel = faker.Random.Int(10, 100);
                    connection.Execute(
                        "INSERT INTO product_variant_stock_levels (inventory_id, variant_packaging_variant_id, variant_packaging_type, variant_packaging_capacity_value, variant_packaging_capacity_unit, stock_level) " +
                        "VALUES (@InventoryId, @VariantId, @PackagingType, @CapacityValue, @CapacityUnit, @StockLevel) ON CONFLICT DO NOTHING;",
                        new
                        {
                            InventoryId = locId, // Use locId as inventory_id
                            VariantId = variant.Id,
                            PackagingType = packagingType,
                            CapacityValue = packagingCapacity,
                            CapacityUnit = packagingUnit,
                            StockLevel = stockLevel
                        }
                    );

                    // 5. Add 1-2 transactions for this stock level
                    for (int t = 0; t < faker.Random.Int(1, 2); t++)
                    {
                        var txnId = Guid.NewGuid();
                        var txnType = faker.PickRandom(new[] { "StockIn", "StockOut" });
                        var txnQty = faker.Random.Int(1, 10);
                        var txnRef = faker.Random.AlphaNumeric(8).ToUpper();
                        var txnTime = DateTime.UtcNow.AddDays(-faker.Random.Int(0, 30));
                        connection.Execute(
                            "INSERT INTO product_variant_transactions (id, base_transaction_transaction_id, base_transaction_type, base_transaction_quantity, base_transaction_reference, base_transaction_timestamp, variant_id, inventory_id) " +
                            "VALUES (@Id, @TxnId, @Type, @Qty, @Ref, @Timestamp, @VariantId, @InventoryId);",
                            new
                            {
                                Id = txnId,
                                TxnId = Guid.NewGuid(),
                                Type = txnType,
                                Qty = txnQty,
                                Ref = txnRef,
                                Timestamp = txnTime,
                                VariantId = variant.Id,
                                InventoryId = locId // Use locId as inventory_id
                            }
                        );
                    }
                }
            }
        }
    }

    // --- Replace the existing SeedProductsAndVariants method ---
    private static void SeedProductsAndVariants(IDbConnection connection, Dictionary<Guid, string> formulationInfos)
    {
        if (!formulationInfos.Any())
        {
            Console.WriteLine("Cannot seed products without formulations.");
            return;
        }

        Console.WriteLine("Seeding Products, Variants, and Packaging Options...");
        var faker = new Faker();

        // Map formulation name to a base product type
        var formulationProductTypes = new Dictionary<string, string> {
            { "Standard White Emulsion", "Interior Emulsion" },
            { "High-Gloss Red Enamel", "Exterior Enamel" },
            { "Acrylic Wall Primer", "Primer" },
            { "Rough Texture Exterior Paint", "Exterior Textured" }
        };

        // Define some standard variants and packaging
        var variantColors = new Dictionary<string, string[]> {
            { "Interior Emulsion", new[] { "White", "Magnolia", "Cream" } },
            { "Exterior Enamel", new[] { "Signal Red", "Gloss Black", "Oxford Blue" } },
            { "Primer", new[] { "White", "Grey" } },
            { "Exterior Textured", new[] { "Sandstone", "Terracotta", "Charcoal" } }
        };

        var packagingOptions = new List<(string Type, string Material, decimal Capacity, string Unit, decimal PriceModifier)> {
            ("Can", "Metal", 10m, "L", 1.0m),
            ("Pail", "Plastic", 15m, "L", 4.5m),
            ("Drum", "Metal", 20m, "L", 16.0m)
        };

        foreach (var formulationInfo in formulationInfos)
        {
            var formulationId = formulationInfo.Key;
            var formulationName = formulationInfo.Value;
            var productType = formulationProductTypes.GetValueOrDefault(formulationName, "General Purpose");

            // 1. Create Product
            var product = new ProductSeed_New
            {
                Id = Guid.NewGuid(),
                FormulationId = formulationId, // Base formulation
                NameValue = formulationName.Replace("Formulation", "Paint").Replace("Primer", "Paint Primer"), // Product line name
                ProductSku = $"PROD-{faker.Random.AlphaNumeric(6).ToUpper()}", // Simple Product Line SKU (if needed)
                ColorValue = "Various", // Base product covers multiple colors
                VolumeValue = 0, // Not specific to base product
                VolumeUnit = "L", // Default unit
                Type = productType,
                PriceAmount = faker.Random.Decimal(10, 30), // Base price factor (per liter maybe?)
                PriceCurrency = "USD",
                CreatedOnUtc = DateTime.UtcNow.AddDays(-faker.Random.Int(10, 90)),
                Status = "Active"
            };

            const string productSql = """
                INSERT INTO products (
                    id, formulation_id, name_value, sku_value, color_value, volume_value, volume_unit,
                    type, price_amount, price_currency, created_on_utc, status
                ) VALUES (
                    @Id, @FormulationId, @NameValue, @ProductSku, @ColorValue, @VolumeValue, @VolumeUnit,
                    @Type, @PriceAmount, @PriceCurrency, @CreatedOnUtc, @Status
                );
                """; // Adjust table/column names
            connection.Execute(productSql, product);

            // 2. Create Variants for the Product
            var colorsForProduct = variantColors.GetValueOrDefault(productType, new[] { "Standard" });
            foreach (var color in colorsForProduct)
            {
                var variantVolumeValue = 20m; // Standard base volume for SKU generation (e.g., 1L)
                var variantVolumeUnit = "L";
                var volumeStringForSku = $"{variantVolumeValue}{variantVolumeUnit}"; // e.g., "1L"

                // Generate SKU using the domain logic
                var sequence = Interlocked.Increment(ref _skuSequence);
                var now = DateTime.UtcNow;
                var skuResult = SKU.Create(color, volumeStringForSku, now.Year, now.Month, sequence);

                if (skuResult.IsFailure)
                {
                    Console.WriteLine($"SKU generation failed for {color}: {skuResult.Error}"); // Log error if SKU creation fails
                    continue;
                }

                var variant = new ProductVariantSeed_New
                {
                    Id = Guid.NewGuid(),
                    ProductId = product.Id,
                    VariantSku = skuResult.Value.Value, // Use the generated SKU
                    VolumeValue = variantVolumeValue, // Base volume for this variant line (packaging differs)
                    VolumeUnit = variantVolumeUnit,
                    ColorValue = color,
                    Status = "Active"
                };

                const string variantSql = """
                    INSERT INTO product_variants (
                        id, product_id, sku_value, volume_value, volume_unit, color_value, status
                    ) VALUES (
                        @Id, @ProductId, @VariantSku, @VolumeValue, @VolumeUnit, @ColorValue, @Status
                    );
                    """; // Adjust table/column names
                connection.Execute(variantSql, variant);

                // 3. Create Packaging Options for the Variant
                foreach (var pkg in packagingOptions)
                {
                    // Only add relevant packaging (e.g., maybe not drums for small primers)
                    if ((productType == "Primer" || productType == "Interior Emulsion") && pkg.Capacity > 5) continue;

                    var packagingOption = new PackagingOptionSeed
                    {
                        Id = Guid.NewGuid(),
                        ProductVariantId = variant.Id,
                        Type = pkg.Type,
                        Material = pkg.Material,
                        CapacityValue = pkg.Capacity,
                        CapacityUnit = pkg.Unit,
                        // Price based on product base price + packaging modifier
                        PriceAmount = Math.Round(product.PriceAmount * pkg.Capacity * pkg.PriceModifier, 2),
                        PriceCurrency = product.PriceCurrency,
                        Status = "Active"
                    };

                    const string packagingSql = """
                        INSERT INTO product_variant_packaging_options (
                            product_variant_id, type, material, capacity_value, capacity_unit,
                            price_amount, price_currency, status
                        ) VALUES (
                            @ProductVariantId, @Type, @Material, @CapacityValue, @CapacityUnit,
                            @PriceAmount, @PriceCurrency, @Status
                        );
                        """; // Adjust table/column names
                    connection.Execute(packagingSql, packagingOption);
                }
            }
        }
    }

    private static void SeedSuppliers(IDbConnection connection, List<Guid> materialIds)
    {
        Console.WriteLine("🏢 Seeding realistic paint industry suppliers...");

        // Create realistic paint industry suppliers
        var realisticSuppliers = CreateRealisticPaintSuppliers();

        const string supplierSql = """
            INSERT INTO suppliers (id, code, name, contact_info_contact_person, contact_info_email, contact_info_phone, contact_info_address, status)
            VALUES (@Id, @Code, @Name, @ContactPerson, @Email, @Phone, @Address, @Status)
            RETURNING id;
            """;

        foreach (var supplier in realisticSuppliers)
        {
            var supplierId = connection.QuerySingle<Guid>(supplierSql, supplier);
            Console.WriteLine($"   ✅ Created supplier: {supplier.Name}");

            // Create realistic contract for this supplier
            SeedRealisticSupplierContract(connection, supplierId, supplier, materialIds);
        }

        Console.WriteLine($"✅ Successfully seeded {realisticSuppliers.Count} paint industry suppliers");
    }

    private static List<RealisticSupplierSeed> CreateRealisticPaintSuppliers()
    {
        return new List<RealisticSupplierSeed>
        {
            new()
            {
                Id = Guid.NewGuid(),
                Code = "SUP-001",
                Name = "ChemWorld Global Ltd",
                ContactPerson = "Sarah Johnson",
                Email = "<EMAIL>",
                Phone = "+233-24-123-4567",
                Address = "Industrial Area, Tema, Greater Accra Region, Ghana",
                Status = "Active",
                Specialization = "Pigments and Colorants",
                MaterialTypes = new[] { "Pigment" }
            },
            new()
            {
                Id = Guid.NewGuid(),
                Code = "SUP-002",
                Name = "Polymer Solutions Africa",
                ContactPerson = "Michael Asante",
                Email = "<EMAIL>",
                Phone = "+233-20-987-6543",
                Address = "Spintex Road, Accra, Greater Accra Region, Ghana",
                Status = "Active",
                Specialization = "Resins and Binders",
                MaterialTypes = new[] { "Resin" }
            },
            new()
            {
                Id = Guid.NewGuid(),
                Code = "SUP-003",
                Name = "West African Solvents Co.",
                ContactPerson = "Fatima Al-Hassan",
                Email = "<EMAIL>",
                Phone = "+233-26-555-7890",
                Address = "Kumasi Industrial Estate, Ashanti Region, Ghana",
                Status = "Active",
                Specialization = "Solvents and Thinners",
                MaterialTypes = new[] { "Solvent" }
            },
            new()
            {
                Id = Guid.NewGuid(),
                Code = "SUP-004",
                Name = "Premium Additives International",
                ContactPerson = "James Osei",
                Email = "<EMAIL>",
                Phone = "+233-24-333-2211",
                Address = "East Legon, Accra, Greater Accra Region, Ghana",
                Status = "Active",
                Specialization = "Paint Additives and Fillers",
                MaterialTypes = new[] { "Additive", "Filler" }
            },
            new()
            {
                Id = Guid.NewGuid(),
                Code = "SUP-005",
                Name = "Universal Paint Materials Ltd",
                ContactPerson = "Grace Mensah",
                Email = "<EMAIL>",
                Phone = "+233-27-444-5566",
                Address = "Takoradi Industrial Zone, Western Region, Ghana",
                Status = "Active",
                Specialization = "Multi-category Supplier",
                MaterialTypes = new[] { "Pigment", "Resin", "Solvent", "Additive" }
            }
        };
    }

    private static void SeedRealisticSupplierContract(IDbConnection connection, Guid supplierId, RealisticSupplierSeed supplier, List<Guid> materialIds)
    {
        static int GetNextContractSequence()
        {
            return Interlocked.Increment(ref _contractSequence);
        }

        // Create realistic contract dates
        var startDate = DateTime.UtcNow.AddMonths(-6); // Contract started 6 months ago
        var endDate = DateTime.UtcNow.AddMonths(18); // Contract ends in 18 months
        var yearMonth = startDate.ToString("yyyyMM");
        var sequence = GetNextContractSequence().ToString("000000");

        var contract = new ContractSeed
        {
            Id = Guid.NewGuid(),
            SupplierId = supplierId,
            ContractNumber = $"TBP-CO-{yearMonth}-{sequence}",
            StartDate = startDate,
            EndDate = endDate,
            Status = "Active"
        };

        const string contractSql = """
            INSERT INTO contracts (id, supplier_id, contract_number_value, start_date, end_date, status)
            VALUES (@Id, @SupplierId, @ContractNumber, @StartDate, @EndDate, @Status)
            RETURNING id;
            """;

        var contractId = connection.QuerySingle<Guid>(contractSql, contract);

        // Create realistic contract terms based on supplier specialization
        var realisticTerms = CreateRealisticContractTerms(contractId, supplier.Specialization, contract.EndDate);

        const string termSql = """
            INSERT INTO contract_terms (contract_id, description, type, expiration_date)
            VALUES (@ContractId, @Description, @Type, @ExpirationDate);
            """;

        connection.Execute(termSql, realisticTerms);

        // Create contracted materials based on supplier specialization
        var contractedMaterials = CreateRealisticContractedMaterials(connection, contractId, supplier, materialIds);

        const string contractedMaterialSql = """
            INSERT INTO contract_materials (contract_id, material_id, material_name, unit_price_amount, unit_price_currency, minimum_order_value, minimum_order_unit, maximum_order_value, maximum_order_unit, lead_time_days)
            VALUES (@ContractId, @MaterialId, @MaterialName, @UnitPriceAmount, @UnitPriceCurrency, @MinimumOrderValue, @MinimumOrderUnit, @MaximumOrderValue, @MaximumOrderUnit, @LeadTimeDays);
            """;

        connection.Execute(contractedMaterialSql, contractedMaterials);

        Console.WriteLine($"      📋 Contract {contract.ContractNumber} with {contractedMaterials.Count} materials");
    }

    private static List<ContractTermSeed> CreateRealisticContractTerms(Guid contractId, string specialization, DateTime contractEndDate)
    {
        var terms = new List<ContractTermSeed>
        {
            new()
            {
                ContractId = contractId,
                Description = "Payment terms: Net 30 days from invoice date",
                Type = "Payment",
                ExpirationDate = contractEndDate
            },
            new()
            {
                ContractId = contractId,
                Description = "Standard delivery within 7-14 business days",
                Type = "Delivery",
                ExpirationDate = contractEndDate
            },
            new()
            {
                ContractId = contractId,
                Description = "All materials must meet ISO 9001 quality standards",
                Type = "Quality",
                ExpirationDate = contractEndDate
            }
        };

        // Add specialization-specific terms
        switch (specialization)
        {
            case "Pigments and Colorants":
                terms.Add(new ContractTermSeed
                {
                    ContractId = contractId,
                    Description = "Color matching tolerance: ΔE ≤ 1.0 under D65 illuminant",
                    Type = "Quality",
                    ExpirationDate = contractEndDate
                });
                break;
            case "Resins and Binders":
                terms.Add(new ContractTermSeed
                {
                    ContractId = contractId,
                    Description = "Viscosity specifications must be maintained within ±5%",
                    Type = "Quality",
                    ExpirationDate = contractEndDate
                });
                break;
            case "Solvents and Thinners":
                terms.Add(new ContractTermSeed
                {
                    ContractId = contractId,
                    Description = "Purity level minimum 99.5% with certified analysis",
                    Type = "Quality",
                    ExpirationDate = contractEndDate
                });
                break;
        }

        return terms;
    }

    private static List<ContractedMaterialSeed> CreateRealisticContractedMaterials(
        IDbConnection connection,
        Guid contractId,
        RealisticSupplierSeed supplier,
        List<Guid> materialIds)
    {
        // Get materials that match supplier's specialization
        var materialInfoSql = """
            SELECT id, name, type, default_unit
            FROM materials
            WHERE id = ANY(@MaterialIds) AND type = ANY(@MaterialTypes)
            """;

        var availableMaterials = connection.Query<(Guid Id, string Name, string Type, string DefaultUnit)>(
            materialInfoSql,
            new { MaterialIds = materialIds.ToArray(), MaterialTypes = supplier.MaterialTypes });

        var contractedMaterials = new List<ContractedMaterialSeed>();

        foreach (var material in availableMaterials)
        {
            var contractedMaterial = new ContractedMaterialSeed
            {
                ContractId = contractId,
                MaterialId = material.Id,
                MaterialName = material.Name,
                UnitPriceCurrency = "GHS", // Ghana Cedis
                MinimumOrderUnit = material.DefaultUnit,
                MaximumOrderUnit = material.DefaultUnit,
                LeadTimeDays = GetRealisticLeadTime(supplier.Specialization)
            };

            // Set realistic pricing based on material type and supplier
            SetRealisticPricingAndQuantities(contractedMaterial, material.Type, supplier.Specialization);

            contractedMaterials.Add(contractedMaterial);
        }

        return contractedMaterials;
    }

    private static int GetRealisticLeadTime(string specialization)
    {
        return specialization switch
        {
            "Pigments and Colorants" => 10, // Pigments often need more processing time
            "Resins and Binders" => 7,      // Standard industrial chemicals
            "Solvents and Thinners" => 5,   // More readily available
            "Paint Additives and Fillers" => 8,
            "Multi-category Supplier" => 12, // Longer due to variety
            _ => 7
        };
    }

    private static void SetRealisticPricingAndQuantities(ContractedMaterialSeed material, string materialType, string specialization)
    {
        // Realistic pricing in Ghana Cedis based on material type
        var (basePrice, minOrder, maxOrder) = materialType switch
        {
            "Pigment" => (85.50m, 25m, 500m),    // Premium pigments - higher cost
            "Resin" => (45.75m, 50m, 1000m),     // Bulk resins - moderate cost
            "Solvent" => (28.25m, 100m, 2000m),  // Solvents - lower cost, higher volume
            "Additive" => (125.80m, 10m, 200m),  // Specialty additives - highest cost
            "Filler" => (18.50m, 200m, 5000m),   // Fillers - lowest cost, highest volume
            _ => (50.00m, 50m, 1000m)
        };

        // Apply supplier-specific adjustments
        var priceMultiplier = specialization switch
        {
            "Multi-category Supplier" => 1.05m,  // Slight premium for convenience
            "Pigments and Colorants" => 0.95m,   // Specialist discount
            "Resins and Binders" => 0.97m,       // Specialist discount
            "Solvents and Thinners" => 0.93m,    // Volume discount
            _ => 1.00m
        };

        material.UnitPriceAmount = Math.Round(basePrice * priceMultiplier, 2);
        material.MinimumOrderValue = minOrder;
        material.MaximumOrderValue = maxOrder;
    }

    private static List<Guid> SeedMaterials(IDbConnection connection)
    {
        var materialIds = new List<Guid>();
        var faker = new Faker();

        foreach (var (name, unit, type) in PaintMaterials)
        {
            var material = new
            {
                Id = Guid.NewGuid(),
                Code = $"MAT-{faker.Random.Number(1000, 9999)}",
                Name = name,
                Description = GetMaterialDescription(name),
                DefaultUnit = unit,
                Type = type,
                Status = "Active"
            };

            const string materialSql = """
                                       INSERT INTO materials (id, code, name, description, type, default_unit, status)
                                       VALUES (@Id, @Code, @Name, @Description, @Type, @DefaultUnit, @Status)
                                       RETURNING id;
                                       """;

            var id = connection.QuerySingle<Guid>(materialSql, material);
            materialIds.Add(id);
        }

        return materialIds;
    }

    // === MATERIAL INVENTORY SEEDING METHODS ===

    private static void CleanupInventoryData(IDbConnection connection)
    {
        Console.WriteLine("   🧹 Cleaning up existing inventory data...");

        // Delete in correct order to avoid foreign key constraints
        connection.Execute("DELETE FROM material_inventory_reservations;");
        connection.Execute("DELETE FROM material_inventory_transactions;");
        connection.Execute("DELETE FROM material_inventories;");
        connection.Execute("DELETE FROM inventory_double;");

        Console.WriteLine("   ✅ Inventory data cleanup completed");
    }

    private static Dictionary<string, Guid> GetInventoryLocations()
    {
        // Define the locations we want to create inventories for
        return new Dictionary<string, Guid>
        {
            { "Factory", Guid.Empty },      // We don't need the actual IDs here anymore
            { "SalesPoint", Guid.Empty }    // Each material will get its own inventory ID
        };
    }

    private static List<MaterialInfo> GetMaterialsFromProcurement(IDbConnection connection)
    {
        const string sql = """
            SELECT DISTINCT
                m.id, m.code, m.name, m.type, m.default_unit,
                AVG(cm.unit_price_amount) as avg_unit_price,
                cm.unit_price_currency
            FROM materials m
            JOIN contract_materials cm ON cm.material_id = m.id
            WHERE m.status = 'Active'
            GROUP BY m.id, m.code, m.name, m.type, m.default_unit, cm.unit_price_currency
            ORDER BY m.type, m.name;
            """;

        return connection.Query<MaterialInfo>(sql).ToList();
    }

    private static Dictionary<(Guid MaterialId, string Location), Guid> CreateMaterialInventories(
        IDbConnection connection,
        List<MaterialInfo> materials,
        Dictionary<string, Guid> locations,
        Faker faker)
    {
        var materialInventories = new Dictionary<(Guid MaterialId, string Location), Guid>();

        foreach (var material in materials)
        {
            foreach (var (location, _) in locations)
            {
                // Create a unique inventory ID for each material-location combination
                var inventoryId = Guid.NewGuid();

                // Set realistic reorder points based on material type
                var (minStock, reorderPoint) = GetRealisticStockLevels(material.Type, faker);

                // Create base inventory record in inventory_double table
                connection.Execute(
                    "INSERT INTO inventory_double (id, location, minimum_stock_level, reorder_point) VALUES (@Id, @Location, @MinStock, @ReorderPoint);",
                    new { Id = inventoryId, Location = location, MinStock = minStock, ReorderPoint = reorderPoint }
                );

                // Create material inventory record that inherits from the base inventory
                connection.Execute(
                    "INSERT INTO material_inventories (id, material_id) VALUES (@Id, @MaterialId);",
                    new { Id = inventoryId, MaterialId = material.Id }
                );

                materialInventories[(material.Id, location)] = inventoryId;
            }
        }

        Console.WriteLine($"   📋 Created inventories for {materials.Count} materials across {locations.Count} locations");
        return materialInventories;
    }

    private static (double MinStock, double ReorderPoint) GetRealisticStockLevels(string materialType, Faker faker)
    {
        return materialType switch
        {
            "Pigment" => (faker.Random.Double(50, 100), faker.Random.Double(150, 300)),     // High-value, moderate usage
            "Resin" => (faker.Random.Double(100, 200), faker.Random.Double(300, 500)),     // Bulk usage, higher stock
            "Solvent" => (faker.Random.Double(200, 400), faker.Random.Double(500, 800)),   // High volume usage
            "Additive" => (faker.Random.Double(10, 30), faker.Random.Double(50, 100)),     // Low usage, expensive
            "Filler" => (faker.Random.Double(500, 1000), faker.Random.Double(1000, 2000)), // Very high volume
            _ => (faker.Random.Double(50, 150), faker.Random.Double(150, 300))
        };
    }

    private static void SeedHistoricalTransactionsFromGoodsReceipts(
        IDbConnection connection,
        Dictionary<(Guid MaterialId, string Location), Guid> materialInventories,
        Faker faker)
    {
        // Get all confirmed goods receipts with their items
        const string goodsReceiptsSql = """
            SELECT
                gr.id as ReceiptId,
                gr.receipt_number,
                gr.received_date,
                gr.status,
                gri.material_id,
                gri.received_quantity_value,
                gri.received_quantity_unit,
                gri.quality_status
            FROM goods_receipts gr
            JOIN goods_receipt_items gri ON gri.goods_receipt_id = gr.id
            WHERE gr.status = 'Confirmed' AND gri.quality_status = 'Approved'
            ORDER BY gr.received_date;
            """;

        var goodsReceiptItems = connection.Query(goodsReceiptsSql).ToList();
        var transactionCount = 0;

        foreach (var item in goodsReceiptItems)
        {
            var materialId = (Guid)item.material_id;
            var receivedQuantity = (decimal)item.received_quantity_value;
            var receivedDate = (DateTime)item.received_date;
            var receiptNumber = (string)item.receipt_number;

            // Add stock to Factory location (goods are received at factory)
            if (materialInventories.TryGetValue((materialId, "Factory"), out var factoryInventoryId))
            {
                var transactionId = Guid.NewGuid();
                var reference = $"Goods Receipt: {receiptNumber}";

                connection.Execute(
                    """
                    INSERT INTO material_inventory_transactions
                    (transaction_id, inventory_double_id, type, quantity, reference, timestamp)
                    VALUES (@TransactionId, @InventoryId, @Type, @Quantity, @Reference, @Timestamp);
                    """,
                    new
                    {
                        TransactionId = transactionId,
                        InventoryId = factoryInventoryId,
                        Type = "StockIn",
                        Quantity = (double)receivedQuantity,
                        Reference = reference,
                        Timestamp = receivedDate
                    }
                );

                transactionCount++;

                // Sometimes transfer some stock to SalesPoint (realistic distribution)
                if (faker.Random.Bool(0.3f) && materialInventories.TryGetValue((materialId, "SalesPoint"), out var salesInventoryId))
                {
                    var transferQuantity = (double)(receivedQuantity * faker.Random.Decimal(0.1m, 0.4m));
                    var transferDate = receivedDate.AddDays(faker.Random.Int(1, 7));

                    // Remove from Factory
                    connection.Execute(
                        """
                        INSERT INTO material_inventory_transactions
                        (transaction_id, inventory_double_id, type, quantity, reference, timestamp)
                        VALUES (@TransactionId, @InventoryId, @Type, @Quantity, @Reference, @Timestamp);
                        """,
                        new
                        {
                            TransactionId = Guid.NewGuid(),
                            InventoryId = factoryInventoryId,
                            Type = "StockOut",
                            Quantity = transferQuantity,
                            Reference = $"Transfer to Sales Point: {receiptNumber}",
                            Timestamp = transferDate
                        }
                    );

                    // Add to SalesPoint
                    connection.Execute(
                        """
                        INSERT INTO material_inventory_transactions
                        (transaction_id, inventory_double_id, type, quantity, reference, timestamp)
                        VALUES (@TransactionId, @InventoryId, @Type, @Quantity, @Reference, @Timestamp);
                        """,
                        new
                        {
                            TransactionId = Guid.NewGuid(),
                            InventoryId = salesInventoryId,
                            Type = "StockIn",
                            Quantity = transferQuantity,
                            Reference = $"Transfer from Factory: {receiptNumber}",
                            Timestamp = transferDate
                        }
                    );

                    transactionCount += 2;
                }
            }
        }

        Console.WriteLine($"   📥 Created {transactionCount} stock-in transactions from {goodsReceiptItems.Count} goods receipt items");
    }

    private static void SeedProductionConsumptionTransactions(
        IDbConnection connection,
        Dictionary<(Guid MaterialId, string Location), Guid> materialInventories,
        Faker faker)
    {
        var transactionCount = 0;
        var productionScenarios = new[]
        {
            ("Batch: B-2024-001", "White Interior Paint", 150.0),
            ("Batch: B-2024-002", "Blue Exterior Paint", 200.0),
            ("Batch: B-2024-003", "Red Primer", 100.0),
            ("Batch: B-2024-004", "Green Topcoat", 175.0),
            ("Batch: B-2024-005", "Yellow Emulsion", 120.0)
        };

        foreach (var (batchNumber, productName, batchSize) in productionScenarios)
        {
            var productionDate = faker.Date.Between(DateTime.UtcNow.AddDays(-60), DateTime.UtcNow.AddDays(-5));

            // Simulate realistic material consumption for paint production
            var materialConsumption = GenerateRealisticMaterialConsumption(batchSize, faker);

            foreach (var (materialType, consumptionPercentage) in materialConsumption)
            {
                // Find materials of this type that have inventory
                var materialsOfType = materialInventories.Keys
                    .Where(k => k.Location == "Factory")
                    .Take(faker.Random.Int(1, 3)) // Use 1-3 materials of each type
                    .ToList();

                foreach (var (materialId, location) in materialsOfType)
                {
                    if (materialInventories.TryGetValue((materialId, location), out var inventoryId))
                    {
                        var consumedQuantity = batchSize * consumptionPercentage * faker.Random.Double(0.8, 1.2);

                        connection.Execute(
                            """
                            INSERT INTO material_inventory_transactions
                            (transaction_id, inventory_double_id, type, quantity, reference, timestamp)
                            VALUES (@TransactionId, @InventoryId, @Type, @Quantity, @Reference, @Timestamp);
                            """,
                            new
                            {
                                TransactionId = Guid.NewGuid(),
                                InventoryId = inventoryId,
                                Type = "StockOut",
                                Quantity = consumedQuantity,
                                Reference = $"Production: {batchNumber} - {productName}",
                                Timestamp = productionDate
                            }
                        );

                        transactionCount++;
                    }
                }
            }
        }

        Console.WriteLine($"   🏭 Created {transactionCount} production consumption transactions for {productionScenarios.Length} batches");
    }

    private static Dictionary<string, double> GenerateRealisticMaterialConsumption(double batchSize, Faker faker)
    {
        // Realistic material consumption percentages for paint production
        return new Dictionary<string, double>
        {
            { "Resin", faker.Random.Double(0.25, 0.35) },      // 25-35% resin
            { "Pigment", faker.Random.Double(0.15, 0.25) },    // 15-25% pigments
            { "Solvent", faker.Random.Double(0.20, 0.30) },    // 20-30% solvents
            { "Filler", faker.Random.Double(0.10, 0.20) },     // 10-20% fillers
            { "Additive", faker.Random.Double(0.02, 0.05) }    // 2-5% additives
        };
    }

    private static void SeedStockAdjustmentsAndTransfers(
        IDbConnection connection,
        Dictionary<(Guid MaterialId, string Location), Guid> materialInventories,
        Faker faker)
    {
        var transactionCount = 0;
        var adjustmentReasons = new[]
        {
            "Physical count adjustment",
            "Damaged goods write-off",
            "Expired material disposal",
            "Quality control rejection",
            "Spillage during handling",
            "Inventory reconciliation",
            "System correction"
        };

        // Create some realistic adjustments
        var adjustmentCount = faker.Random.Int(5, 15);
        for (int i = 0; i < adjustmentCount; i++)
        {
            var inventoryId = faker.PickRandom(materialInventories.Values.ToList());
            var adjustmentDate = faker.Date.Between(DateTime.UtcNow.AddDays(-30), DateTime.UtcNow.AddDays(-1));
            var reason = faker.PickRandom(adjustmentReasons);

            // Most adjustments are small corrections, occasionally larger write-offs
            var isLargeAdjustment = faker.Random.Bool(0.2f);
            var adjustmentQuantity = isLargeAdjustment
                ? faker.Random.Double(50, 200)
                : faker.Random.Double(1, 20);

            // 70% negative adjustments (losses), 30% positive (found stock)
            var isNegative = faker.Random.Bool(0.7f);
            var transactionType = isNegative ? "StockOut" : "StockIn";

            connection.Execute(
                """
                INSERT INTO material_inventory_transactions
                (transaction_id, inventory_double_id, type, quantity, reference, timestamp)
                VALUES (@TransactionId, @InventoryId, @Type, @Quantity, @Reference, @Timestamp);
                """,
                new
                {
                    TransactionId = Guid.NewGuid(),
                    InventoryId = inventoryId,
                    Type = transactionType,
                    Quantity = adjustmentQuantity,
                    Reference = $"Adjustment: {reason}",
                    Timestamp = adjustmentDate
                }
            );

            transactionCount++;
        }

        Console.WriteLine($"   ⚖️ Created {transactionCount} stock adjustment transactions");
    }

    private static void SeedProductionReservations(
        IDbConnection connection,
        Dictionary<(Guid MaterialId, string Location), Guid> materialInventories,
        Faker faker)
    {
        var reservationCount = 0;
        var upcomingBatches = new[]
        {
            ("Batch: B-2024-006", "Premium White Paint", 180.0, DateTime.UtcNow.AddDays(2)),
            ("Batch: B-2024-007", "Metallic Silver", 90.0, DateTime.UtcNow.AddDays(5)),
            ("Batch: B-2024-008", "Deep Blue Marine", 150.0, DateTime.UtcNow.AddDays(7)),
            ("Batch: B-2024-009", "Forest Green", 120.0, DateTime.UtcNow.AddDays(10))
        };

        foreach (var (batchNumber, productName, batchSize, scheduledDate) in upcomingBatches)
        {
            var materialRequirements = GenerateRealisticMaterialConsumption(batchSize, faker);

            foreach (var (materialType, consumptionPercentage) in materialRequirements)
            {
                // Find factory materials of this type for reservation
                var factoryMaterials = materialInventories.Keys
                    .Where(k => k.Location == "Factory")
                    .Take(faker.Random.Int(1, 2)) // Reserve 1-2 materials of each type
                    .ToList();

                foreach (var (materialId, location) in factoryMaterials)
                {
                    if (materialInventories.TryGetValue((materialId, location), out var inventoryId))
                    {
                        var reservedQuantity = batchSize * consumptionPercentage * faker.Random.Double(0.9, 1.1);
                        var reservationDate = faker.Date.Between(DateTime.UtcNow.AddDays(-3), DateTime.UtcNow);

                        connection.Execute(
                            """
                            INSERT INTO material_inventory_reservations
                            (id, material_id, quantity, purpose, created_at, inventory_id)
                            VALUES (@Id, @MaterialId, @Quantity, @Purpose, @CreatedAt, @InventoryId);
                            """,
                            new
                            {
                                Id = Guid.NewGuid(),
                                MaterialId = materialId,
                                Quantity = reservedQuantity,
                                Purpose = $"Production: {batchNumber} - {productName}",
                                CreatedAt = reservationDate,
                                InventoryId = inventoryId
                            }
                        );

                        reservationCount++;
                    }
                }
            }
        }

        // Add some additional reservations for quality control and R&D
        var additionalReservations = new[]
        {
            ("Quality Control Testing", faker.Random.Double(5, 15)),
            ("R&D Color Matching", faker.Random.Double(2, 8)),
            ("Customer Sample Preparation", faker.Random.Double(3, 12)),
            ("Process Validation", faker.Random.Double(10, 25))
        };

        foreach (var (purpose, quantity) in additionalReservations)
        {
            var randomMaterial = faker.PickRandom(materialInventories.Keys.Where(k => k.Location == "Factory").ToList());
            if (materialInventories.TryGetValue(randomMaterial, out var inventoryId))
            {
                connection.Execute(
                    """
                    INSERT INTO material_inventory_reservations
                    (id, material_id, quantity, purpose, created_at, inventory_id)
                    VALUES (@Id, @MaterialId, @Quantity, @Purpose, @CreatedAt, @InventoryId);
                    """,
                    new
                    {
                        Id = Guid.NewGuid(),
                        MaterialId = randomMaterial.MaterialId,
                        Quantity = quantity,
                        Purpose = purpose,
                        CreatedAt = faker.Date.Between(DateTime.UtcNow.AddDays(-7), DateTime.UtcNow),
                        InventoryId = inventoryId
                    }
                );

                reservationCount++;
            }
        }

        Console.WriteLine($"   📋 Created {reservationCount} material reservations for upcoming production and operations");
    }

    // === FORMULATION SEEDING METHODS ===

    private static void SeedRealisticFormulations(IDbConnection connection)
    {
        Console.WriteLine("🧪 Seeding realistic paint formulations using procurement materials...");

        var faker = new Faker();

        // 1. Get materials from procurement data grouped by type
        var materialsByType = GetMaterialsByType(connection);
        if (!materialsByType.Any())
        {
            Console.WriteLine("❌ No materials found from procurement data. Cannot seed formulations.");
            return;
        }

        // 2. Create realistic paint formulations
        var formulations = CreateRealisticPaintFormulations(connection, materialsByType, faker);

        // 3. Calculate and update formulation costs
        CalculateAndUpdateFormulationCosts(connection, formulations, faker);

        // 4. Create formulation versions and approval workflow
        CreateFormulationVersionsAndApprovals(connection, formulations, faker);

        Console.WriteLine($"✅ Successfully seeded {formulations.Count} realistic paint formulations");
    }

    private static Dictionary<string, List<MaterialInfo>> GetMaterialsByType(IDbConnection connection)
    {
        const string sql = """
            SELECT DISTINCT
                m.id, m.code, m.name, m.type, m.default_unit,
                AVG(cm.unit_price_amount) as avg_unit_price,
                cm.unit_price_currency
            FROM materials m
            JOIN contract_materials cm ON cm.material_id = m.id
            WHERE m.status = 'Active'
            GROUP BY m.id, m.code, m.name, m.type, m.default_unit, cm.unit_price_currency
            ORDER BY m.type, m.name;
            """;

        var materials = connection.Query<MaterialInfo>(sql).ToList();

        return materials.GroupBy(m => m.Type)
                      .ToDictionary(g => g.Key, g => g.ToList());
    }

    private static List<FormulationInfo> CreateRealisticPaintFormulations(
        IDbConnection connection,
        Dictionary<string, List<MaterialInfo>> materialsByType,
        Faker faker)
    {
        var formulations = new List<FormulationInfo>();

        // Define realistic paint formulations with their characteristics
        var paintFormulations = new[]
        {
            new PaintSpecification { Name = "Premium White Interior Emulsion", Type = "Interior", BaseColor = "White", Finish = "Matt", ShelfLife = 24, ProductionTime = 4 },
            new PaintSpecification { Name = "Exterior Weather Shield Blue", Type = "Exterior", BaseColor = "Blue", Finish = "Satin", ShelfLife = 36, ProductionTime = 6 },
            new PaintSpecification { Name = "Anti-Rust Red Primer", Type = "Primer", BaseColor = "Red", Finish = "Primer", ShelfLife = 18, ProductionTime = 3 },
            new PaintSpecification { Name = "High-Gloss Green Topcoat", Type = "Topcoat", BaseColor = "Green", Finish = "Gloss", ShelfLife = 30, ProductionTime = 5 },
            new PaintSpecification { Name = "Quick-Dry Yellow Enamel", Type = "Enamel", BaseColor = "Yellow", Finish = "Semi-Gloss", ShelfLife = 24, ProductionTime = 4 },
            new PaintSpecification { Name = "Metallic Silver Automotive", Type = "Automotive", BaseColor = "Silver", Finish = "Metallic", ShelfLife = 12, ProductionTime = 8 },
            new PaintSpecification { Name = "Deep Black Matte Finish", Type = "Interior", BaseColor = "Black", Finish = "Matt", ShelfLife = 24, ProductionTime = 4 },
            new PaintSpecification { Name = "Clear Protective Coating", Type = "Protective", BaseColor = "Clear", Finish = "Clear", ShelfLife = 36, ProductionTime = 3 },
            new PaintSpecification { Name = "Textured Beige Wall Paint", Type = "Textured", BaseColor = "Beige", Finish = "Textured", ShelfLife = 18, ProductionTime = 6 },
            new PaintSpecification { Name = "Marine Grade Blue Coating", Type = "Marine", BaseColor = "Blue", Finish = "Semi-Gloss", ShelfLife = 48, ProductionTime = 7 }
        };

        foreach (var paintSpec in paintFormulations)
        {
            var formulationId = Guid.NewGuid();
            var description = $"Professional grade {paintSpec.Type.ToLower()} paint with {paintSpec.Finish.ToLower()} finish. " +
                            $"Designed for {GetApplicationDescription(paintSpec.Type)} with excellent durability and coverage.";

            // Create realistic ingredient list for this paint type
            var ingredients = CreateRealisticIngredients(materialsByType, paintSpec, faker);
            var instructions = CreateProductionInstructions(paintSpec, faker);

            // Insert formulation record
            connection.Execute(
                """
                INSERT INTO formulations (id, name, description, shelf_life, estimated_production_time, status)
                VALUES (@Id, @Name, @Description, @ShelfLife, @ProductionTime, @Status);
                """,
                new
                {
                    Id = formulationId,
                    Name = paintSpec.Name,
                    Description = description,
                    ShelfLife = paintSpec.ShelfLife,
                    ProductionTime = TimeSpan.FromHours(paintSpec.ProductionTime),
                    Status = "Draft"
                }
            );

            // Insert ingredients with formulation_id (they are owned entities)
            foreach (var ingredient in ingredients)
            {
                connection.Execute(
                    """
                    INSERT INTO ingredients (id, material_id, quantity_value, quantity_unit, formulation_id)
                    VALUES (@Id, @MaterialId, @QuantityValue, @QuantityUnit, @FormulationId);
                    """,
                    new
                    {
                        Id = ingredient.Id,
                        MaterialId = ingredient.MaterialId,
                        QuantityValue = ingredient.QuantityValue,
                        QuantityUnit = ingredient.QuantityUnit,
                        FormulationId = formulationId
                    }
                );
            }

            formulations.Add(new FormulationInfo
            {
                Id = formulationId,
                Name = paintSpec.Name,
                Description = description,
                Type = paintSpec.Type,
                Status = "Draft",
                ShelfLife = paintSpec.ShelfLife,
                ProductionTime = paintSpec.ProductionTime,
                Ingredients = ingredients,
                Instructions = instructions
            });
        }

        Console.WriteLine($"   🎨 Created {formulations.Count} realistic paint formulations");
        return formulations;
    }

    private static string GetApplicationDescription(string paintType)
    {
        return paintType switch
        {
            "Interior" => "interior walls and ceilings",
            "Exterior" => "external surfaces exposed to weather",
            "Primer" => "surface preparation and adhesion",
            "Topcoat" => "final protective and decorative coating",
            "Enamel" => "high-durability surfaces requiring gloss",
            "Automotive" => "vehicle painting and refinishing",
            "Protective" => "industrial and protective applications",
            "Textured" => "decorative textured wall finishes",
            "Marine" => "marine and coastal environments",
            _ => "general purpose applications"
        };
    }

    private static List<IngredientInfo> CreateRealisticIngredients(
        Dictionary<string, List<MaterialInfo>> materialsByType,
        PaintSpecification paintSpec,
        Faker faker)
    {
        var ingredients = new List<IngredientInfo>();

        // Base paint formulation percentages (realistic for paint manufacturing)
        var baseFormulation = GetBaseFormulationPercentages(paintSpec.Type, paintSpec.Finish);

        foreach (var (materialType, percentage) in baseFormulation)
        {
            if (!materialsByType.ContainsKey(materialType)) continue;

            // Select 1-2 materials of this type for the formulation
            var availableMaterials = materialsByType[materialType];
            var selectedMaterials = availableMaterials.OrderBy(_ => faker.Random.Int()).Take(faker.Random.Int(1, 3)).ToList();

            foreach (var material in selectedMaterials)
            {
                // Calculate quantity based on percentage and add some realistic variation
                var baseQuantity = percentage * faker.Random.Double(0.8, 1.2);
                var quantity = Math.Round(baseQuantity, 2);

                ingredients.Add(new IngredientInfo
                {
                    Id = Guid.NewGuid(),
                    MaterialId = material.Id,
                    MaterialName = material.Name,
                    QuantityValue = (decimal)quantity,
                    QuantityUnit = GetRealisticUnit(materialType, material.DefaultUnit)
                });
            }
        }

        return ingredients;
    }

    private static Dictionary<string, double> GetBaseFormulationPercentages(string paintType, string finish)
    {
        // Realistic paint formulation percentages based on paint chemistry
        return paintType switch
        {
            "Interior" => new Dictionary<string, double>
            {
                { "Resin", 25.0 },
                { "Pigment", finish == "Matt" ? 20.0 : 15.0 },
                { "Solvent", 30.0 },
                { "Filler", finish == "Matt" ? 20.0 : 15.0 },
                { "Additive", 5.0 }
            },
            "Exterior" => new Dictionary<string, double>
            {
                { "Resin", 30.0 },
                { "Pigment", 18.0 },
                { "Solvent", 25.0 },
                { "Filler", 20.0 },
                { "Additive", 7.0 }
            },
            "Primer" => new Dictionary<string, double>
            {
                { "Resin", 20.0 },
                { "Pigment", 25.0 },
                { "Solvent", 35.0 },
                { "Filler", 15.0 },
                { "Additive", 5.0 }
            },
            "Automotive" => new Dictionary<string, double>
            {
                { "Resin", 35.0 },
                { "Pigment", 20.0 },
                { "Solvent", 25.0 },
                { "Filler", 12.0 },
                { "Additive", 8.0 }
            },
            _ => new Dictionary<string, double>
            {
                { "Resin", 28.0 },
                { "Pigment", 17.0 },
                { "Solvent", 28.0 },
                { "Filler", 20.0 },
                { "Additive", 7.0 }
            }
        };
    }

    private static string GetRealisticUnit(string materialType, string defaultUnit)
    {
        return materialType switch
        {
            "Pigment" => "kg",
            "Resin" => "kg",
            "Solvent" => "L",
            "Filler" => "kg",
            "Additive" => "kg",
            _ => defaultUnit ?? "kg"
        };
    }

    private static List<string> CreateProductionInstructions(PaintSpecification paintSpec, Faker faker)
    {
        var baseInstructions = new List<string>
        {
            "1. Pre-heat mixing vessel to 25°C and ensure all equipment is clean",
            "2. Add solvents to the mixing vessel first to prevent material waste",
            "3. Slowly add resin while maintaining constant agitation at 200 RPM"
        };

        // Add paint-type specific instructions
        var specificInstructions = paintSpec.Type switch
        {
            "Interior" => new[]
            {
                "4. Gradually incorporate pigments while increasing mixing speed to 400 RPM",
                "5. Add fillers in small batches to prevent lumping",
                "6. Incorporate additives and mix for additional 15 minutes",
                "7. Check viscosity and adjust with solvent if needed (target: 85-95 KU)",
                "8. Strain through 100-mesh filter to remove any agglomerates",
                "9. Final quality check for color match and consistency"
            },
            "Exterior" => new[]
            {
                "4. Add UV stabilizers and weather-resistant additives first",
                "5. Incorporate pigments gradually while monitoring temperature",
                "6. Add fillers and maintain mixing for 20 minutes minimum",
                "7. Check for proper dispersion using Hegman gauge (target: 6-7)",
                "8. Add final additives and mix for 10 minutes",
                "9. Test for weather resistance and adhesion properties"
            },
            "Primer" => new[]
            {
                "4. Add anti-corrosive pigments first for maximum effectiveness",
                "5. Ensure thorough wetting of all pigment particles",
                "6. Add adhesion promoters and mix thoroughly",
                "7. Check pH level (target: 8.5-9.5 for optimal performance)",
                "8. Test for proper substrate adhesion",
                "9. Verify coverage and hiding power specifications"
            },
            _ => new[]
            {
                "4. Add pigments gradually while monitoring color development",
                "5. Incorporate fillers and maintain consistent mixing speed",
                "6. Add performance additives according to specification",
                "7. Check viscosity and adjust as needed",
                "8. Perform quality control tests",
                "9. Package according to storage requirements"
            }
        };

        baseInstructions.AddRange(specificInstructions);
        return baseInstructions;
    }

    private static void CreateFormulationVersionsAndApprovals(
        IDbConnection connection,
        List<FormulationInfo> formulations,
        Faker faker)
    {
        var authors = new[] { "Dr. Sarah Chen", "Mike Rodriguez", "Emma Thompson", "James Wilson", "Lisa Park" };
        var approvers = new[] { "Dr. Robert Smith", "Maria Garcia", "David Johnson" };

        foreach (var formulation in formulations)
        {
            // Create initial version
            var versionId = Guid.NewGuid();
            var snapshotId = Guid.NewGuid();
            var createdDate = faker.Date.Between(DateTime.UtcNow.AddDays(-90), DateTime.UtcNow.AddDays(-30));

            // Create version with embedded snapshot data
            connection.Execute(
                """
                INSERT INTO versions (id, time_stamp, is_finalized, formulation_id,
                                    snapshot_minimum_production_quantity_value, snapshot_minimum_production_quantity_unit,
                                    snapshot_estimated_production_time, snapshot_shelf_life, snapshot_instructions)
                VALUES (@Id, @TimeStamp, @IsFinalized, @FormulationId,
                        @MinProdQty, @MinProdUnit, @ProductionTime, @ShelfLife, @Instructions);
                """,
                new
                {
                    Id = versionId,
                    TimeStamp = createdDate,
                    IsFinalized = false,
                    FormulationId = formulation.Id,
                    MinProdQty = faker.Random.Decimal(100, 500),
                    MinProdUnit = "L",
                    ProductionTime = TimeSpan.FromHours(formulation.ProductionTime),
                    ShelfLife = formulation.ShelfLife,
                    Instructions = string.Join(";", formulation.Instructions)
                }
            );

            // Create ingredient snapshots linked to the version
            foreach (var ingredient in formulation.Ingredients)
            {
                connection.Execute(
                    """
                    INSERT INTO ingredient_snapshot (formulation_snapshot_version_id, ingredient_id, raw_material_id, quantity_value, quantity_unit)
                    VALUES (@VersionId, @IngredientId, @MaterialId, @QuantityValue, @QuantityUnit);
                    """,
                    new
                    {
                        VersionId = versionId,
                        IngredientId = ingredient.Id,
                        MaterialId = ingredient.MaterialId,
                        QuantityValue = ingredient.QuantityValue,
                        QuantityUnit = ingredient.QuantityUnit
                    }
                );
            }



            // Create approval workflow for some formulations
            if (faker.Random.Bool(0.7f)) // 70% of formulations go through approval
            {
                CreateApprovalWorkflow(connection, formulation, authors, approvers, faker);
            }
        }

        Console.WriteLine($"   📋 Created versions and approval workflows for {formulations.Count} formulations");
    }

    private static void CalculateAndUpdateFormulationCosts(
        IDbConnection connection,
        List<FormulationInfo> formulations,
        Faker faker)
    {
        // Cost calculation settings (realistic for Ghana paint industry)
        var laborCostPerHour = 15.00m; // GHS per hour
        var overheadPercentage = 0.20m; // 20% overhead
        var currency = "GHS";

        foreach (var formulation in formulations)
        {
            // Get material costs from contracts
            var materialCosts = GetMaterialCostsForFormulation(connection, formulation.Ingredients);

            // Calculate total material cost
            decimal totalMaterialCost = 0;
            foreach (var ingredient in formulation.Ingredients)
            {
                var materialCost = materialCosts.FirstOrDefault(mc => mc.MaterialId == ingredient.MaterialId);
                if (materialCost != null)
                {
                    totalMaterialCost += ingredient.QuantityValue * materialCost.AverageUnitPrice;
                }
            }

            // Calculate labor cost based on production time
            var totalLaborCost = (decimal)formulation.ProductionTime * laborCostPerHour;

            // Calculate overhead cost
            var totalOverheadCost = (totalMaterialCost + totalLaborCost) * overheadPercentage;

            // Calculate total cost and cost per unit (assuming 100L production batch)
            var totalCost = totalMaterialCost + totalLaborCost + totalOverheadCost;
            var productionQuantity = 100m; // 100 liters standard batch
            var costPerUnit = totalCost / productionQuantity;

            // Create calculation basis
            var calculationBasis = $"Production quantity: {productionQuantity} L, " +
                                 $"Materials: {formulation.Ingredients.Count}, " +
                                 $"Production time: {formulation.ProductionTime} hours, " +
                                 $"Calculated at: {DateTime.UtcNow:yyyy-MM-dd HH:mm}";

            // Update formulation with cost data
            connection.Execute(
                """
                UPDATE formulations
                SET
                    cost_calculated_at = @CalculatedAt,
                    cost_calculation_basis = @CalculationBasis,
                    cost_per_unit_amount = @CostPerUnit,
                    cost_per_unit_currency = @Currency,
                    material_cost_amount = @MaterialCost,
                    material_cost_currency = @Currency,
                    labor_cost_amount = @LaborCost,
                    labor_cost_currency = @Currency,
                    overhead_cost_amount = @OverheadCost,
                    overhead_cost_currency = @Currency,
                    total_cost_amount = @TotalCost,
                    total_cost_currency = @Currency
                WHERE id = @FormulationId;
                """,
                new
                {
                    FormulationId = formulation.Id,
                    CalculatedAt = DateTime.UtcNow,
                    CalculationBasis = calculationBasis,
                    CostPerUnit = costPerUnit,
                    Currency = currency,
                    MaterialCost = totalMaterialCost,
                    LaborCost = totalLaborCost,
                    OverheadCost = totalOverheadCost,
                    TotalCost = totalCost
                }
            );
        }

        Console.WriteLine($"   💰 Calculated and updated costs for {formulations.Count} formulations");
    }

    private static List<MaterialCostInfo> GetMaterialCostsForFormulation(
        IDbConnection connection,
        List<IngredientInfo> ingredients)
    {
        if (!ingredients.Any()) return new List<MaterialCostInfo>();

        const string sql = """
            SELECT
                cm.material_id as MaterialId,
                AVG(cm.unit_price_amount) as AverageUnitPrice,
                cm.unit_price_currency as Currency,
                m.name as MaterialName
            FROM contract_materials cm
            JOIN contracts c ON c.id = cm.contract_id
            JOIN materials m ON m.id = cm.material_id
            WHERE c.status = 'Active'
              AND cm.material_id = ANY(@MaterialIds)
              AND c.end_date > @CurrentDate
            GROUP BY cm.material_id, cm.unit_price_currency, m.name
            """;

        var materialIds = ingredients.Select(i => i.MaterialId).ToArray();
        return connection.Query<MaterialCostInfo>(
            sql,
            new { MaterialIds = materialIds, CurrentDate = DateTime.UtcNow }).ToList();
    }

    private static void CreateApprovalWorkflow(
        IDbConnection connection,
        FormulationInfo formulation,
        string[] authors,
        string[] approvers,
        Faker faker)
    {
        var author = faker.PickRandom(authors);
        var approver = faker.PickRandom(approvers);

        // Simulate realistic approval timeline
        var submissionDate = faker.Date.Between(DateTime.UtcNow.AddDays(-60), DateTime.UtcNow.AddDays(-10));
        var reviewDate = submissionDate.AddDays(faker.Random.Int(1, 7));

        // Determine approval outcome (85% approved, 15% rejected/pending)
        var approvalOutcome = faker.Random.WeightedRandom(
            new[] { "Approved", "Rejected", "UnderReview" },
            new[] { 0.85f, 0.10f, 0.05f }
        );

        // Update formulation status
        var finalStatus = approvalOutcome switch
        {
            "Approved" => "Approved",
            "Rejected" => "Rejected",
            "UnderReview" => "UnderReview",
            _ => "Draft"
        };

        connection.Execute(
            "UPDATE formulations SET status = @Status WHERE id = @Id;",
            new { Status = finalStatus, Id = formulation.Id }
        );

        // Create approval records
        if (approvalOutcome == "Approved")
        {
            var approvalNotes = faker.PickRandom(new[]
            {
                "Formulation meets all quality specifications. Approved for production.",
                "Excellent color stability and coverage. Ready for manufacturing.",
                "All tests passed. Formulation approved with minor cost optimization suggestions.",
                "Quality parameters within acceptable range. Approved for immediate use.",
                "Formulation demonstrates superior performance characteristics. Approved."
            });

            // You would create approval records here if you have an approvals table
            // For now, we'll just update the status
        }
        else if (approvalOutcome == "Rejected")
        {
            var rejectionReason = faker.PickRandom(new[]
            {
                "Viscosity outside acceptable range. Requires reformulation.",
                "Color match does not meet specification. Please adjust pigment ratios.",
                "Drying time exceeds maximum allowed. Review solvent composition.",
                "Cost analysis shows formulation exceeds budget constraints.",
                "Adhesion test results below minimum requirements."
            });

            // You would create rejection records here if you have a rejections table
        }
    }

    private static void SeedCompleteProcurementLifecycle(IDbConnection connection)
    {
        Console.WriteLine("🚀 Starting Complete Procurement-to-Payment Lifecycle Seeding...");

        // Clear existing data to ensure consistency
        ClearProcurementData(connection);

        // Seed fresh materials and suppliers
        Console.WriteLine("📦 Seeding materials...");
        var materialIds = SeedMaterials(connection);

        Console.WriteLine("🏢 Seeding suppliers...");
        SeedSuppliers(connection, materialIds);

        // Get supplier and material data
        var supplierMaterials = GetSupplierMaterialData(connection);
        if (!supplierMaterials.Any())
        {
            Console.WriteLine("❌ No supplier materials found. Cannot seed procurement data.");
            return;
        }

        var faker = new Faker();
        var procurementScenarios = new List<ProcurementScenario>();

        // Generate 25 realistic procurement scenarios
        foreach (var supplierGroup in supplierMaterials.Take(5)) // 5 suppliers
        {
            var supplierId = supplierGroup.Key;
            var materials = supplierGroup.Value;

            // Create 5 scenarios per supplier with different states and complexities
            procurementScenarios.AddRange(GenerateProcurementScenarios(faker, supplierId, materials));
        }

        Console.WriteLine($"📋 Generated {procurementScenarios.Count} procurement scenarios");

        // Execute scenarios in chronological order
        var sortedScenarios = procurementScenarios.OrderBy(s => s.PurchaseOrder.OrderDate).ToList();

        foreach (var scenario in sortedScenarios)
        {
            ExecuteProcurementScenario(connection, scenario, faker);
        }

        Console.WriteLine("✅ Complete Procurement-to-Payment Lifecycle Seeding completed!");
    }

    private static void ClearProcurementData(IDbConnection connection)
    {
        Console.WriteLine("🧹 Clearing existing procurement and supplier data...");

        // Clear in dependency order (most dependent first)
        var clearSql = """
            DELETE FROM purchase_invoice_items;
            DELETE FROM purchase_invoices;
            DELETE FROM goods_receipt_items;
            DELETE FROM goods_receipts;
            DELETE FROM purchase_order_items;
            DELETE FROM purchase_orders;
            DELETE FROM contract_materials;
            DELETE FROM contract_terms;
            DELETE FROM contracts;
            DELETE FROM suppliers;
            DELETE FROM materials;
            """;

        connection.Execute(clearSql);
        Console.WriteLine("✅ Procurement, supplier, and material data cleared");
    }

    private static Dictionary<Guid, List<SupplierMaterialInfo>> GetSupplierMaterialData(IDbConnection connection)
    {
        const string sql = """
            SELECT
                s.id as SupplierId,
                cm.material_id as MaterialId,
                cm.material_name as MaterialName,
                cm.unit_price_amount as UnitPriceAmount,
                cm.unit_price_currency as UnitPriceCurrency,
                cm.minimum_order_unit as MinimumOrderUnit,
                cm.minimum_order_value as MinimumOrderValue,
                m.type as MaterialType
            FROM suppliers s
            JOIN contracts c ON c.supplier_id = s.id
            JOIN contract_materials cm ON cm.contract_id = c.id
            JOIN materials m ON m.id = cm.material_id
            WHERE s.status = 'Active' AND c.status = 'Active';
            """;

        return connection.Query<SupplierMaterialInfo>(sql)
            .GroupBy(sm => sm.SupplierId)
            .ToDictionary(g => g.Key, g => g.ToList());
    }

    private static List<ProcurementScenario> GenerateProcurementScenarios(
        Faker faker,
        Guid supplierId,
        List<SupplierMaterialInfo> materials)
    {
        var scenarios = new List<ProcurementScenario>();
        var scenarioTypes = new[]
        {
            ProcurementScenarioType.HappyPath,
            ProcurementScenarioType.PartialDeliveries,
            ProcurementScenarioType.QualityIssues,
            ProcurementScenarioType.InvoiceDiscrepancies,
            ProcurementScenarioType.PendingApproval
        };

        foreach (var scenarioType in scenarioTypes)
        {
            var scenario = CreateProcurementScenario(faker, supplierId, materials, scenarioType);
            scenarios.Add(scenario);
        }

        return scenarios;
    }

    private static ProcurementScenario CreateProcurementScenario(
        Faker faker,
        Guid supplierId,
        List<SupplierMaterialInfo> materials,
        ProcurementScenarioType scenarioType)
    {
        var orderDate = faker.Date.Between(DateTime.UtcNow.AddMonths(-6), DateTime.UtcNow.AddDays(-1));
        var promiseDate = orderDate.AddDays(faker.Random.Int(7, 21)); // 1-3 weeks delivery

        // Select 2-4 materials for this order
        var selectedMaterials = materials
            .OrderBy(x => faker.Random.Int())
            .Take(faker.Random.Int(2, Math.Min(4, materials.Count)))
            .ToList();

        var purchaseOrder = new PurchaseOrderSeed
        {
            Id = Guid.NewGuid(),
            PurchaseOrderNumber = GeneratePurchaseOrderNumber(orderDate, faker),
            SupplierId = supplierId,
            OrderDate = orderDate,
            PromiseDate = promiseDate,
            RequestBy = faker.Name.FullName(),
            Status = "Draft", // Will be updated based on scenario
            DiscountPercentage = faker.Random.Decimal(0, 5), // 0-5% discount
            FreightChargeAmount = faker.Random.Decimal(50, 200),
            FreightChargeCurrency = "GHS",
            OrderCurrency = "GHS",
            TaxRate = 12.5m, // Ghana VAT
            Items = CreatePurchaseOrderItems(faker, selectedMaterials, scenarioType)
        };

        return new ProcurementScenario
        {
            Type = scenarioType,
            PurchaseOrder = purchaseOrder,
            Timeline = CreateScenarioTimeline(faker, orderDate, promiseDate, scenarioType)
        };
    }

    private static void SeedPurchaseOrders(IDbConnection connection)
    {
        // First, get existing supplier IDs and their contracted materials
        const string supplierMaterialsSql = """
            SELECT 
                s.id as SupplierId,
                cm.material_id as MaterialId,
                cm.material_name as MaterialName,
                cm.unit_price_amount as UnitPriceAmount,
                cm.unit_price_currency as UnitPriceCurrency,
                cm.minimum_order_unit as MinimumOrderUnit,
                cm.minimum_order_value as MinimumOrderValue
            FROM suppliers s
            JOIN contracts c ON c.supplier_id = s.id
            JOIN contracted_material cm ON cm.contract_id = c.id
            WHERE s.status = 'Active' AND c.status = 'Active';
        """;

        var supplierMaterials = connection.Query<SupplierMaterialInfo>(supplierMaterialsSql)
            .GroupBy(sm => sm.SupplierId)
            .ToDictionary(g => g.Key, g => g.ToList());

        var faker = new Faker();
        var purchaseOrders = new List<PurchaseOrderSeed>();

        // Generate 20 purchase orders across different states
        foreach (var supplierGroup in supplierMaterials)
        {
            var supplierId = supplierGroup.Key;
            var materials = supplierGroup.Value;

            // Create 4 orders per supplier with different states
            var orderStates = new[] { "Draft", "Pending", "Approved", "Completed", "Cancelled" };
            foreach (var state in orderStates)
            {
                var orderDate = faker.Date.Past(1);
                var purchaseOrder = new PurchaseOrderSeed
                {
                    Id = Guid.NewGuid(),
                    PurchaseOrderNumber = $"TBP-PO-{orderDate:yyyyMM}-{faker.Random.Number(100000, 999999)}",
                    SupplierId = supplierId,
                    OrderDate = orderDate,
                    RequestBy = faker.Name.FullName(),
                    ApprovedDate = state is "Approved" or "Completed" ? faker.Date.Between(orderDate, DateTime.Now) : null,
                    ApprovedBy = state is "Approved" or "Completed" ? faker.Name.FullName() : null,
                    Status = state
                };

                // Add 2-5 items per order
                var maxItems = Math.Min(materials.Count, 5);
                var itemCount = faker.Random.Int(Math.Min(2, maxItems), maxItems);
                var selectedMaterials = materials
                    .OrderBy(x => faker.Random.Int())
                    .Take(itemCount)
                    .ToList();

                purchaseOrder.Items = selectedMaterials.Select(m =>
                {
                    var quantity = faker.Random.Decimal(m.MinimumOrderValue * 1.5m, m.MinimumOrderValue * 5m);
                    var receivedQuantity = state == "Completed" ? quantity :
                                         state == "Approved" ? faker.Random.Decimal(0, quantity) :
                                         0;

                    return new PurchaseOrderItemSeed
                    {
                        MaterialId = m.MaterialId,
                        MaterialName = m.MaterialName,
                        QuantityValue = quantity,
                        QuantityUnit = m.MinimumOrderUnit,
                        UnitPriceAmount = m.UnitPriceAmount,
                        UnitPriceCurrency = m.UnitPriceCurrency,
                        Notes = faker.Commerce.ProductDescription(),
                        ReceivedQuantityValue = receivedQuantity,
                        ReceivedQuantityUnit = m.MinimumOrderUnit
                    };
                }).ToList();

                const string purchaseOrderSql = """
                    INSERT INTO purchase_orders (
                        id, order_number_value, supplier_id, order_date, 
                        requested_by, approved_date, approved_by, status
                    ) VALUES (
                        @Id, @PurchaseOrderNumber, @SupplierId, @OrderDate,
                        @RequestBy, @ApprovedDate, @ApprovedBy, @Status
                    );
                    """;

                connection.Execute(purchaseOrderSql, purchaseOrder);

                const string purchaseOrderItemSql = """
                    INSERT INTO purchase_order_item (
                        purchase_order_id, material_id, material_name,
                        quantity_value, quantity_unit,
                        unit_price_amount, unit_price_currency,
                        notes, received_quantity_value, received_quantity_unit
                    ) VALUES (
                        @PurchaseOrderId, @MaterialId, @MaterialName,
                        @QuantityValue, @QuantityUnit,
                        @UnitPriceAmount, @UnitPriceCurrency,
                        @Notes, @ReceivedQuantityValue, @ReceivedQuantityUnit
                    );
                    """;

                foreach (var item in purchaseOrder.Items)
                {
                    item.Id = Guid.NewGuid();
                    item.PurchaseOrderId = purchaseOrder.Id;
                    connection.Execute(purchaseOrderItemSql, item);
                }
            }
        }
    }

    private static string GetMaterialDescription(string name) => name switch
    {
        "Titanium Dioxide" => "Premium white pigment providing excellent opacity and brightness",
        "Iron Oxide Red" => "Synthetic inorganic pigment for red coloration with high tinting strength",
        "Carbon Black" => "Fine particle carbon powder for black pigmentation",
        "Phthalocyanine Blue" => "Organic blue pigment with high tinting strength",
        "Acrylic Resin" => "Water-based binder for exterior and interior paints",
        "Alkyd Resin" => "Oil-based binder for high-gloss finishes",
        "Polyurethane Resin" => "High-performance binder for durable coatings",
        "Epoxy Resin" => "Two-component binder for industrial coatings",
        "Mineral Spirits" => "Petroleum-based solvent for oil-based paints",
        "Xylene" => "Strong solvent for industrial coatings",
        "Butyl Acetate" => "Fast-evaporating solvent for lacquers",
        "Genapol (Surfactant)" => "Wetting agent for improved paint flow and stability",
        "Anti-Foaming Agent" => "Silicone-based additive to prevent foam formation",
        "UV Stabilizer" => "Additive to prevent UV degradation of coatings",
        "Dispersing Agent" => "Additive for proper pigment dispersion",
        "River Sand" => "Natural silica aggregate for textured finishes",
        "Calcium Carbonate" => "Natural mineral filler for paint extension",
        "Talc Powder" => "Fine mineral powder for paint matting",
        "Silica Powder" => "Fine silica powder for rheology control",
        "Mica Powder" => "Platy mineral for decorative effects",
        _ => "Paint production material"
    };

    // Helper method to get existing material IDs and names
    private static Dictionary<Guid, (string Name, string Unit)> GetMaterialInfos(IDbConnection connection)
    {
        try
        {
            return connection.Query<(Guid Id, string Name, string DefaultUnit)>(
                "SELECT id, name, default_unit FROM materials WHERE status = 'Active';"
            ).ToDictionary(m => m.Id, m => (m.Name, m.DefaultUnit));
        }
        catch (Exception ex) // Handle case where table might not exist yet during initial migrations
        {
            Console.WriteLine($"Error querying materials (might be during initial migration): {ex.Message}");
            return new Dictionary<Guid, (string Name, string Unit)>();
        }
    }

    private static string GeneratePurchaseOrderNumber(DateTime orderDate, Faker faker)
    {
        return $"TBP-PO-{orderDate:yyyyMM}-{faker.Random.Number(100000, 999999)}";
    }

    private static List<PurchaseOrderItemSeed> CreatePurchaseOrderItems(
        Faker faker,
        List<SupplierMaterialInfo> materials,
        ProcurementScenarioType scenarioType)
    {
        return materials.Select(material => new PurchaseOrderItemSeed
        {
            Id = Guid.NewGuid(),
            MaterialId = material.MaterialId,
            MaterialName = material.MaterialName,
            QuantityValue = CalculateOrderQuantity(faker, material, scenarioType),
            QuantityUnit = GetConsistentUnit(material.MaterialType),
            UnitPriceAmount = material.UnitPriceAmount,
            UnitPriceCurrency = material.UnitPriceCurrency,
            Notes = GenerateItemNotes(faker, material.MaterialName, scenarioType),
            ReceivedQuantityValue = 0, // Will be updated during goods receipt
            ReceivedQuantityUnit = GetConsistentUnit(material.MaterialType)
        }).ToList();
    }

    private static decimal CalculateOrderQuantity(Faker faker, SupplierMaterialInfo material, ProcurementScenarioType scenarioType)
    {
        var baseQuantity = material.MinimumOrderValue * faker.Random.Decimal(1.5m, 4m);

        return scenarioType switch
        {
            ProcurementScenarioType.HappyPath => Math.Round(baseQuantity, 2),
            ProcurementScenarioType.PartialDeliveries => Math.Round(baseQuantity * 1.5m, 2), // Larger order for partials
            ProcurementScenarioType.QualityIssues => Math.Round(baseQuantity, 2),
            ProcurementScenarioType.InvoiceDiscrepancies => Math.Round(baseQuantity, 2),
            ProcurementScenarioType.PendingApproval => Math.Round(baseQuantity * 0.8m, 2),
            _ => Math.Round(baseQuantity, 2)
        };
    }

    private static string GetConsistentUnit(string materialType)
    {
        return materialType switch
        {
            "Pigment" => "kg",
            "Resin" => "kg",
            "Solvent" => "L",
            "Additive" => "kg",
            "Filler" => "kg",
            _ => "kg"
        };
    }

    private static string GenerateItemNotes(Faker faker, string materialName, ProcurementScenarioType scenarioType)
    {
        var baseNotes = $"Standard {materialName.ToLower()} for production";

        return scenarioType switch
        {
            ProcurementScenarioType.QualityIssues => $"{baseNotes} - Quality inspection required",
            ProcurementScenarioType.PartialDeliveries => $"{baseNotes} - Delivery in batches acceptable",
            ProcurementScenarioType.InvoiceDiscrepancies => $"{baseNotes} - Verify pricing on delivery",
            _ => baseNotes
        };
    }

    private static ScenarioTimeline CreateScenarioTimeline(
        Faker faker,
        DateTime orderDate,
        DateTime promiseDate,
        ProcurementScenarioType scenarioType)
    {
        var timeline = new ScenarioTimeline
        {
            OrderCreated = orderDate,
            OrderApproved = scenarioType != ProcurementScenarioType.PendingApproval
                ? orderDate.AddDays(faker.Random.Int(1, 3))
                : (DateTime?)null
        };

        if (timeline.OrderApproved.HasValue)
        {
            // Calculate delivery dates based on scenario
            var deliveryVariance = scenarioType switch
            {
                ProcurementScenarioType.HappyPath => faker.Random.Int(-2, 1), // On time or early
                ProcurementScenarioType.PartialDeliveries => faker.Random.Int(0, 5), // Slightly delayed
                ProcurementScenarioType.QualityIssues => faker.Random.Int(2, 7), // Delayed due to quality
                ProcurementScenarioType.InvoiceDiscrepancies => faker.Random.Int(-1, 3), // Mixed
                _ => faker.Random.Int(0, 3)
            };

            timeline.FirstDelivery = promiseDate.AddDays(deliveryVariance);

            // Add additional deliveries for partial delivery scenarios
            if (scenarioType == ProcurementScenarioType.PartialDeliveries)
            {
                timeline.SecondDelivery = timeline.FirstDelivery?.AddDays(faker.Random.Int(3, 10));
                if (faker.Random.Bool(0.3f)) // 30% chance of third delivery
                {
                    timeline.ThirdDelivery = timeline.SecondDelivery?.AddDays(faker.Random.Int(3, 7));
                }
            }

            // Calculate invoice dates
            var lastDelivery = timeline.ThirdDelivery ?? timeline.SecondDelivery ?? timeline.FirstDelivery;
            if (lastDelivery.HasValue)
            {
                timeline.InvoiceReceived = lastDelivery.Value.AddDays(faker.Random.Int(1, 5));

                if (scenarioType != ProcurementScenarioType.InvoiceDiscrepancies)
                {
                    timeline.InvoiceApproved = timeline.InvoiceReceived?.AddDays(faker.Random.Int(1, 3));
                }
            }
        }

        return timeline;
    }

    private static void ExecuteProcurementScenario(IDbConnection connection, ProcurementScenario scenario, Faker faker)
    {
        Console.WriteLine($"📦 Executing {scenario.Type} scenario for PO {scenario.PurchaseOrder.PurchaseOrderNumber}");

        // 1. Create Purchase Order
        CreatePurchaseOrder(connection, scenario.PurchaseOrder);

        // 2. Approve if needed
        if (scenario.Timeline.OrderApproved.HasValue)
        {
            ApprovePurchaseOrder(connection, scenario.PurchaseOrder.Id, scenario.Timeline.OrderApproved.Value, faker);
        }

        // 3. Create Goods Receipts
        if (scenario.Timeline.FirstDelivery.HasValue)
        {
            CreateGoodsReceipts(connection, scenario, faker);
        }

        // 4. Create Purchase Invoices
        if (scenario.Timeline.InvoiceReceived.HasValue)
        {
            CreatePurchaseInvoices(connection, scenario, faker);
        }
    }

    private static void CreatePurchaseOrder(IDbConnection connection, PurchaseOrderSeed purchaseOrder)
    {
        const string purchaseOrderSql = """
            INSERT INTO purchase_orders (
                id, order_number_value, supplier_id, order_date, promise_date,
                requested_by, status, discount_percentage, freight_charge_amount,
                freight_charge_currency, order_currency, tax_rate
            ) VALUES (
                @Id, @PurchaseOrderNumber, @SupplierId, @OrderDate, @PromiseDate,
                @RequestBy, @Status, @DiscountPercentage, @FreightChargeAmount,
                @FreightChargeCurrency, @OrderCurrency, @TaxRate
            );
            """;

        connection.Execute(purchaseOrderSql, purchaseOrder);

        const string itemSql = """
            INSERT INTO purchase_order_items (
                id, purchase_order_id, material_id, material_name,
                quantity_value, quantity_unit, unit_price_amount, unit_price_currency,
                notes, received_quantity_value, received_quantity_unit
            ) VALUES (
                @Id, @PurchaseOrderId, @MaterialId, @MaterialName,
                @QuantityValue, @QuantityUnit, @UnitPriceAmount, @UnitPriceCurrency,
                @Notes, @ReceivedQuantityValue, @ReceivedQuantityUnit
            );
            """;

        foreach (var item in purchaseOrder.Items)
        {
            item.PurchaseOrderId = purchaseOrder.Id;
            connection.Execute(itemSql, item);
        }
    }

    private static void ApprovePurchaseOrder(IDbConnection connection, Guid purchaseOrderId, DateTime approvedDate, Faker faker)
    {
        const string sql = """
            UPDATE purchase_orders
            SET status = 'Approved', approved_date = @ApprovedDate, approved_by = @ApprovedBy
            WHERE id = @Id;
            """;

        connection.Execute(sql, new
        {
            Id = purchaseOrderId,
            ApprovedDate = approvedDate,
            ApprovedBy = faker.Name.FullName()
        });
    }

    private static void CreateGoodsReceipts(IDbConnection connection, ProcurementScenario scenario, Faker faker)
    {
        var deliveries = new List<(DateTime Date, string Type)>();

        if (scenario.Timeline.FirstDelivery.HasValue)
            deliveries.Add((scenario.Timeline.FirstDelivery.Value, "Full"));

        if (scenario.Timeline.SecondDelivery.HasValue)
            deliveries.Add((scenario.Timeline.SecondDelivery.Value, "Partial"));

        if (scenario.Timeline.ThirdDelivery.HasValue)
            deliveries.Add((scenario.Timeline.ThirdDelivery.Value, "Final"));

        foreach (var (deliveryDate, deliveryType) in deliveries)
        {
            CreateGoodsReceipt(connection, scenario, deliveryDate, deliveryType, faker);
        }
    }

    private static void CreateGoodsReceipt(
        IDbConnection connection,
        ProcurementScenario scenario,
        DateTime receivedDate,
        string deliveryType,
        Faker faker)
    {
        var goodsReceiptId = Guid.NewGuid();
        var receiptNumber = $"TBP-GR-{receivedDate:yyyyMM}-{faker.Random.Number(100000, 999999)}";

        // Determine quality status based on scenario
        var qualityStatus = scenario.Type == ProcurementScenarioType.QualityIssues && deliveryType == "Full"
            ? faker.PickRandom("Rejected", "Pending")
            : "Approved";

        var status = qualityStatus == "Approved" ? "Confirmed" : "Pending";

        const string receiptSql = """
            INSERT INTO goods_receipts (
                id, purchase_order_id, purchase_order_number, supplier_id, receipt_number,
                received_date, received_by, status, delivery_note, supplier_reference
            ) VALUES (
                @Id, @PurchaseOrderId, @PurchaseOrderNumber, @SupplierId, @ReceiptNumber,
                @ReceivedDate, @ReceivedBy, @Status, @DeliveryNote, @SupplierReference
            );
            """;

        connection.Execute(receiptSql, new
        {
            Id = goodsReceiptId,
            PurchaseOrderId = scenario.PurchaseOrder.Id,
            PurchaseOrderNumber = scenario.PurchaseOrder.PurchaseOrderNumber,
            SupplierId = scenario.PurchaseOrder.SupplierId,
            ReceiptNumber = receiptNumber,
            ReceivedDate = receivedDate,
            ReceivedBy = faker.Name.FullName(),
            Status = status,
            DeliveryNote = $"DN-{faker.Random.AlphaNumeric(8).ToUpper()}",
            SupplierReference = $"SR-{faker.Random.AlphaNumeric(6).ToUpper()}"
        });

        // Create goods receipt items
        CreateGoodsReceiptItems(connection, goodsReceiptId, scenario, deliveryType, qualityStatus, faker);
    }

    private static void CreateGoodsReceiptItems(
        IDbConnection connection,
        Guid goodsReceiptId,
        ProcurementScenario scenario,
        string deliveryType,
        string qualityStatus,
        Faker faker)
    {
        foreach (var orderItem in scenario.PurchaseOrder.Items)
        {
            var receivedQuantity = CalculateReceivedQuantity(orderItem.QuantityValue, deliveryType, scenario.Type, faker);

            const string itemSql = """
                INSERT INTO goods_receipt_items (
                    id, goods_receipt_id, material_id, material_name,
                    ordered_quantity_value, ordered_quantity_unit,
                    received_quantity_value, received_quantity_unit,
                    unit_price_amount, unit_price_currency,
                    quality_status, notes
                ) VALUES (
                    @Id, @GoodsReceiptId, @MaterialId, @MaterialName,
                    @OrderedQuantityValue, @OrderedQuantityUnit,
                    @ReceivedQuantityValue, @ReceivedQuantityUnit,
                    @UnitPriceAmount, @UnitPriceCurrency,
                    @QualityStatus, @Notes
                );
                """;

            connection.Execute(itemSql, new
            {
                Id = Guid.NewGuid(),
                GoodsReceiptId = goodsReceiptId,
                MaterialId = orderItem.MaterialId,
                MaterialName = orderItem.MaterialName,
                OrderedQuantityValue = orderItem.QuantityValue,
                OrderedQuantityUnit = orderItem.QuantityUnit,
                ReceivedQuantityValue = receivedQuantity,
                ReceivedQuantityUnit = orderItem.QuantityUnit,
                UnitPriceAmount = orderItem.UnitPriceAmount,
                UnitPriceCurrency = orderItem.UnitPriceCurrency,
                QualityStatus = qualityStatus,
                Notes = GenerateReceiptNotes(deliveryType, qualityStatus, faker)
            });

            // Update purchase order item received quantity
            UpdatePurchaseOrderItemReceivedQuantity(connection, scenario.PurchaseOrder.Id, orderItem.MaterialId, receivedQuantity);
        }
    }

    private static decimal CalculateReceivedQuantity(decimal orderedQuantity, string deliveryType, ProcurementScenarioType scenarioType, Faker faker)
    {
        var baseQuantity = deliveryType switch
        {
            "Full" => orderedQuantity,
            "Partial" => orderedQuantity * faker.Random.Decimal(0.4m, 0.7m),
            "Final" => orderedQuantity * faker.Random.Decimal(0.2m, 0.4m),
            _ => orderedQuantity
        };

        // Add scenario-specific variations
        return scenarioType switch
        {
            ProcurementScenarioType.QualityIssues => Math.Round(baseQuantity * faker.Random.Decimal(0.85m, 0.95m), 2), // Some rejected
            ProcurementScenarioType.InvoiceDiscrepancies => Math.Round(baseQuantity * faker.Random.Decimal(1.02m, 1.08m), 2), // Over delivery
            _ => Math.Round(baseQuantity, 2)
        };
    }

    private static string GenerateReceiptNotes(string deliveryType, string qualityStatus, Faker faker)
    {
        var baseNote = $"{deliveryType} delivery received";

        return qualityStatus switch
        {
            "Rejected" => $"{baseNote} - Quality issues detected, material rejected",
            "Pending" => $"{baseNote} - Quality inspection in progress",
            _ => $"{baseNote} - Quality approved"
        };
    }

    private static void UpdatePurchaseOrderItemReceivedQuantity(IDbConnection connection, Guid purchaseOrderId, Guid materialId, decimal additionalQuantity)
    {
        const string sql = """
            UPDATE purchase_order_items
            SET received_quantity_value = COALESCE(received_quantity_value, 0) + @AdditionalQuantity
            WHERE purchase_order_id = @PurchaseOrderId AND material_id = @MaterialId;
            """;

        connection.Execute(sql, new
        {
            PurchaseOrderId = purchaseOrderId,
            MaterialId = materialId,
            AdditionalQuantity = additionalQuantity
        });
    }

    private static void CreatePurchaseInvoices(IDbConnection connection, ProcurementScenario scenario, Faker faker)
    {
        // Get all goods receipts for this purchase order
        const string receiptsSql = """
            SELECT id, received_date, receipt_number
            FROM goods_receipts
            WHERE purchase_order_id = @PurchaseOrderId
            ORDER BY received_date;
            """;

        var receipts = connection.Query(receiptsSql, new { PurchaseOrderId = scenario.PurchaseOrder.Id }).ToList();

        // Create one invoice per goods receipt (as specified)
        foreach (var receipt in receipts)
        {
            CreatePurchaseInvoice(connection, scenario, (Guid)receipt.id, (DateTime)receipt.received_date, faker);
        }
    }

    private static void CreatePurchaseInvoice(
        IDbConnection connection,
        ProcurementScenario scenario,
        Guid goodsReceiptId,
        DateTime goodsReceiptDate,
        Faker faker)
    {
        var invoiceId = Guid.NewGuid();
        var invoiceDate = scenario.Timeline.InvoiceReceived ?? goodsReceiptDate.AddDays(faker.Random.Int(1, 5));
        var dueDate = invoiceDate.AddDays(30); // 30-day payment terms
        var supplierInvoiceNumber = $"INV-{faker.Random.AlphaNumeric(8).ToUpper()}";

        // Calculate invoice totals and matching results
        var (totalAmount, matchingResult, variancePercentage) = CalculateInvoiceAmountAndMatching(
            connection, goodsReceiptId, scenario.Type, faker);

        var status = DetermineInvoiceStatus(scenario, matchingResult, variancePercentage);
        var approvedDate = scenario.Timeline.InvoiceApproved;
        var approvedBy = approvedDate.HasValue ? faker.Name.FullName() : null;

        const string invoiceSql = """
            INSERT INTO purchase_invoices (
                id, purchase_order_id, purchase_order_number, supplier_id, supplier_invoice_number,
                invoice_date, due_date, total_amount, total_currency, status,
                last_matching_result, last_matching_variance_percentage, last_matching_date,
                approved_date, approved_by
            ) VALUES (
                @Id, @PurchaseOrderId, @PurchaseOrderNumber, @SupplierId, @SupplierInvoiceNumber,
                @InvoiceDate, @DueDate, @TotalAmount, @TotalCurrency, @Status,
                @LastMatchingResult, @LastMatchingVariancePercentage, @LastMatchingDate,
                @ApprovedDate, @ApprovedBy
            );
            """;

        connection.Execute(invoiceSql, new
        {
            Id = invoiceId,
            PurchaseOrderId = scenario.PurchaseOrder.Id,
            PurchaseOrderNumber = scenario.PurchaseOrder.PurchaseOrderNumber,
            SupplierId = scenario.PurchaseOrder.SupplierId,
            SupplierInvoiceNumber = supplierInvoiceNumber,
            InvoiceDate = invoiceDate,
            DueDate = dueDate,
            TotalAmount = totalAmount,
            TotalCurrency = "GHS",
            Status = status,
            LastMatchingResult = matchingResult,
            LastMatchingVariancePercentage = variancePercentage,
            LastMatchingDate = invoiceDate.AddHours(faker.Random.Int(1, 24)),
            ApprovedDate = approvedDate,
            ApprovedBy = approvedBy
        });

        // Create invoice items based on goods receipt items
        CreatePurchaseInvoiceItems(connection, invoiceId, goodsReceiptId, scenario.Type, faker);
    }

    private static (decimal TotalAmount, bool MatchingResult, decimal VariancePercentage) CalculateInvoiceAmountAndMatching(
        IDbConnection connection,
        Guid goodsReceiptId,
        ProcurementScenarioType scenarioType,
        Faker faker)
    {
        const string sql = """
            SELECT
                SUM(received_quantity_value * unit_price_amount) as ExpectedTotal
            FROM goods_receipt_items
            WHERE goods_receipt_id = @GoodsReceiptId;
            """;

        var expectedTotal = connection.QuerySingle<decimal>(sql, new { GoodsReceiptId = goodsReceiptId });

        // Apply scenario-specific discrepancies
        var actualTotal = scenarioType switch
        {
            ProcurementScenarioType.InvoiceDiscrepancies => expectedTotal * faker.Random.Decimal(1.03m, 1.12m), // 3-12% over
            ProcurementScenarioType.QualityIssues => expectedTotal * faker.Random.Decimal(0.95m, 1.02m), // Slight variance
            _ => expectedTotal * faker.Random.Decimal(0.98m, 1.02m) // Normal variance
        };

        var variancePercentage = Math.Abs((actualTotal - expectedTotal) / expectedTotal * 100);
        var matchingResult = variancePercentage <= 2.5m; // 2.5% tolerance

        return (Math.Round(actualTotal, 2), matchingResult, Math.Round(variancePercentage, 2));
    }

    private static string DetermineInvoiceStatus(ProcurementScenario scenario, bool matchingResult, decimal variancePercentage)
    {
        if (scenario.Type == ProcurementScenarioType.InvoiceDiscrepancies || !matchingResult)
        {
            return variancePercentage > 5m ? "RequiresReview" : "PendingApproval";
        }

        return scenario.Timeline.InvoiceApproved.HasValue ? "Approved" : "PendingApproval";
    }

    private static void CreatePurchaseInvoiceItems(
        IDbConnection connection,
        Guid invoiceId,
        Guid goodsReceiptId,
        ProcurementScenarioType scenarioType,
        Faker faker)
    {
        const string receiptItemsSql = """
            SELECT
                material_id, material_name, received_quantity_value, received_quantity_unit,
                unit_price_amount, unit_price_currency
            FROM goods_receipt_items
            WHERE goods_receipt_id = @GoodsReceiptId;
            """;

        var receiptItems = connection.Query(receiptItemsSql, new { GoodsReceiptId = goodsReceiptId });

        foreach (var item in receiptItems)
        {
            // Apply price discrepancies for certain scenarios
            var invoicePrice = scenarioType == ProcurementScenarioType.InvoiceDiscrepancies
                ? (decimal)item.unit_price_amount * faker.Random.Decimal(1.02m, 1.08m)
                : (decimal)item.unit_price_amount;

            const string itemSql = """
                INSERT INTO purchase_invoice_items (
                    id, purchase_invoice_id, material_id, material_name,
                    invoiced_quantity_value, invoiced_quantity_unit,
                    unit_price_amount, unit_price_currency
                ) VALUES (
                    @Id, @PurchaseInvoiceId, @MaterialId, @MaterialName,
                    @InvoicedQuantityValue, @InvoicedQuantityUnit,
                    @UnitPriceAmount, @UnitPriceCurrency
                );
                """;

            connection.Execute(itemSql, new
            {
                Id = Guid.NewGuid(),
                PurchaseInvoiceId = invoiceId,
                MaterialId = item.material_id,
                MaterialName = item.material_name,
                InvoicedQuantityValue = item.received_quantity_value,
                InvoicedQuantityUnit = item.received_quantity_unit,
                UnitPriceAmount = Math.Round(invoicePrice, 2),
                UnitPriceCurrency = item.unit_price_currency
            });
        }
    }

    // Enums and Classes
    private enum ProcurementScenarioType
    {
        HappyPath,
        PartialDeliveries,
        QualityIssues,
        InvoiceDiscrepancies,
        PendingApproval
    }

    private class ProcurementScenario
    {
        public ProcurementScenarioType Type { get; set; }
        public PurchaseOrderSeed PurchaseOrder { get; set; } = new();
        public ScenarioTimeline Timeline { get; set; } = new();
    }

    private class ScenarioTimeline
    {
        public DateTime OrderCreated { get; set; }
        public DateTime? OrderApproved { get; set; }
        public DateTime? FirstDelivery { get; set; }
        public DateTime? SecondDelivery { get; set; }
        public DateTime? ThirdDelivery { get; set; }
        public DateTime? InvoiceReceived { get; set; }
        public DateTime? InvoiceApproved { get; set; }
    }

    private class ContractSeed
    {
        public Guid Id { get; set; }
        public Guid SupplierId { get; set; }
        public string ContractNumber { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Status { get; set; }
    }

    private class ContractTermSeed
    {
        public Guid ContractId { get; set; }
        public string Description { get; set; }
        public string Type { get; set; }
        public DateTime ExpirationDate { get; set; }
    }

    private class ContractedMaterialSeed
    {
        public Guid ContractId { get; set; }
        public Guid MaterialId { get; set; }
        public string MaterialName { get; set; } = string.Empty;
        public decimal UnitPriceAmount { get; set; }
        public string UnitPriceCurrency { get; set; } = string.Empty;
        public decimal MinimumOrderValue { get; set; }
        public string MinimumOrderUnit { get; set; } = string.Empty;
        public decimal MaximumOrderValue { get; set; }
        public string MaximumOrderUnit { get; set; } = string.Empty;
        public int LeadTimeDays { get; set; }
    }

    private class MaterialSeed
    {
        public Guid Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Type { get; set; }
        public string DefaultUnit { get; set; }
        public string Status { get; set; }
    }

    private class RealisticSupplierSeed
    {
        public Guid Id { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string ContactPerson { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Specialization { get; set; } = string.Empty;
        public string[] MaterialTypes { get; set; } = Array.Empty<string>();
    }

    private class SupplierMaterialInfo
    {
        public Guid SupplierId { get; set; }
        public Guid MaterialId { get; set; }
        public string MaterialName { get; set; } = string.Empty;
        public decimal UnitPriceAmount { get; set; }
        public string UnitPriceCurrency { get; set; } = string.Empty;
        public string MinimumOrderUnit { get; set; } = string.Empty;
        public decimal MinimumOrderValue { get; set; }
        public string MaterialType { get; set; } = string.Empty;
    }

    private class PurchaseOrderSeed
    {
        public Guid Id { get; set; }
        public string PurchaseOrderNumber { get; set; } = string.Empty;
        public Guid SupplierId { get; set; }
        public DateTime OrderDate { get; set; }
        public DateTime PromiseDate { get; set; }
        public string RequestBy { get; set; } = string.Empty;
        public DateTime? ApprovedDate { get; set; }
        public string? ApprovedBy { get; set; }
        public string Status { get; set; } = string.Empty;
        public decimal DiscountPercentage { get; set; }
        public decimal FreightChargeAmount { get; set; }
        public string FreightChargeCurrency { get; set; } = string.Empty;
        public string OrderCurrency { get; set; } = string.Empty;
        public decimal TaxRate { get; set; }
        public List<PurchaseOrderItemSeed> Items { get; set; } = new();
    }

    private class PurchaseOrderItemSeed
    {
        public Guid Id { get; set; }
        public Guid PurchaseOrderId { get; set; }
        public Guid MaterialId { get; set; }
        public string MaterialName { get; set; }
        public decimal QuantityValue { get; set; }
        public string QuantityUnit { get; set; }
        public decimal UnitPriceAmount { get; set; }
        public string UnitPriceCurrency { get; set; }
        public string? Notes { get; set; }
        public decimal ReceivedQuantityValue { get; set; }
        public string ReceivedQuantityUnit { get; set; }
    }

    private static void SeedFormulations(IDbConnection connection, Dictionary<Guid, (string Name, string Unit)> materialInfos)
    {
        if (!materialInfos.Any())
        {
            Console.WriteLine("Cannot seed formulations without material data.");
            return;
        }

        var faker = new Faker();

        var formulations = new List<FormulationSeed>
        {
            // Example 1: Basic White Wall Paint
            new FormulationSeed
            {
                Id = Guid.NewGuid(),
                Name = "Standard White Emulsion",
                Description = "Economical water-based white paint for interior walls.",
                ShelfLife = 12, // months
                EstimatedProductionTime = TimeSpan.FromMinutes(120), // minutes
                Status = "Active",
                Ingredients = new List<IngredientSeed>
                {
                    new IngredientSeed { MaterialName = "Titanium Dioxide", QuantityValue = 25.0m }, // % or KG? Assume KG for now
                    new IngredientSeed { MaterialName = "Acrylic Resin", QuantityValue = 15.0m },
                    new IngredientSeed { MaterialName = "Calcium Carbonate", QuantityValue = 30.0m },
                    new IngredientSeed { MaterialName = "Genapol (Surfactant)", QuantityValue = 0.5m },
                    new IngredientSeed { MaterialName = "Anti-Foaming Agent", QuantityValue = 0.2m }
                    // Water would typically be added, but might not be a tracked 'material'
                },
                Versions = new List<VersionSeed> // Initial Version
                {
                    new VersionSeed
                    {
                        Id = Guid.NewGuid(),
                        TimeStamp = faker.Date.Past(1),
                        IsFinalized = true,
                        Snapshot = new SnapshotSeed
                        {
                            MinimumProductionQuantityValue = 100.0m, // Liters
                            MinimumProductionQuantityUnit = "L",
                            EstimatedProductionTime = TimeSpan.FromMinutes(120), // minutes
                            ShelfLife = 12, // months
                            Instructions = new List<string> { "Mix pigments slowly.", "Add resin under agitation.", "Adjust viscosity.", "Filter final product." },
                            // Ingredients snapshot matches the main list for v1
                        },
                        ChangeLog = new ChangeLogSeed
                        {
                            Id = Guid.NewGuid(),
                            Entries = new List<ChangeLogEntrySeed> { new ChangeLogEntrySeed { TimeStamp = DateTime.UtcNow, Description = "Initial formulation release.", Author = "System" } }
                        }
                    }
                }
            },
            // Example 2: High-Gloss Red Enamel
            new FormulationSeed
            {
                Id = Guid.NewGuid(),
                Name = "High-Gloss Red Enamel",
                Description = "Durable solvent-based red enamel for metal and wood.",
                ShelfLife = 24,
                EstimatedProductionTime = TimeSpan.FromMinutes(180),
                Status = "Active",
                Ingredients = new List<IngredientSeed>
                {
                    new IngredientSeed { MaterialName = "Iron Oxide Red", QuantityValue = 18.0m },
                    new IngredientSeed { MaterialName = "Alkyd Resin", QuantityValue = 40.0m },
                    new IngredientSeed { MaterialName = "Mineral Spirits", QuantityValue = 35.0m },
                    new IngredientSeed { MaterialName = "Talc Powder", QuantityValue = 5.0m },
                    new IngredientSeed { MaterialName = "Dispersing Agent", QuantityValue = 0.8m }
                },
                Versions = new List<VersionSeed> // Version 1
                {
                    new VersionSeed
                    {
                        Id = Guid.NewGuid(),
                        TimeStamp = faker.Date.Past(2),
                        IsFinalized = false, // Not final yet
                        Snapshot = new SnapshotSeed
                        {
                            MinimumProductionQuantityValue = 50.0m,
                            MinimumProductionQuantityUnit = "L",
                            EstimatedProductionTime = TimeSpan.FromMinutes(180),
                            ShelfLife = 24,
                            Instructions = new List<string> { "Disperse pigment in resin.", "Add solvent carefully.", "Check gloss level.", "Package in airtight containers." },
                        },
                        ChangeLog = new ChangeLogSeed
                        {
                             Id = Guid.NewGuid(),
                             Entries = new List<ChangeLogEntrySeed> { new ChangeLogEntrySeed { TimeStamp = DateTime.UtcNow.AddDays(-60), Description = "Initial Draft.", Author = "R&D Dept." } }
                        }
                    },
                    new VersionSeed // Version 2 (Finalized)
                    {
                        Id = Guid.NewGuid(),
                        TimeStamp = faker.Date.Past(1),
                        IsFinalized = true,
                        Snapshot = new SnapshotSeed // Slightly adjusted snapshot for v2
                        {
                            MinimumProductionQuantityValue = 50.0m,
                            MinimumProductionQuantityUnit = "L",
                            EstimatedProductionTime = TimeSpan.FromMinutes(175), // Improved time
                            ShelfLife = 24,
                            Instructions = new List<string> { "Disperse pigment in resin.", "Add solvent carefully.", "Add UV Stabilizer.", "Check gloss level.", "Package in airtight containers." },
                            // Snapshot Ingredients for V2 (Assume UV Stabilizer was added)
                            SnapshotIngredients = new List<SnapshotIngredientSeed>
                            {
                                 new SnapshotIngredientSeed { MaterialName = "Iron Oxide Red", QuantityValue = 18.0m },
                                 new SnapshotIngredientSeed { MaterialName = "Alkyd Resin", QuantityValue = 40.0m },
                                 new SnapshotIngredientSeed { MaterialName = "Mineral Spirits", QuantityValue = 34.5m }, // Adjusted solvent
                                 new SnapshotIngredientSeed { MaterialName = "Talc Powder", QuantityValue = 5.0m },
                                 new SnapshotIngredientSeed { MaterialName = "Dispersing Agent", QuantityValue = 0.8m },
                                 new SnapshotIngredientSeed { MaterialName = "UV Stabilizer", QuantityValue = 0.7m } // Added
                            }
                        },
                        ChangeLog = new ChangeLogSeed
                        {
                             Id = Guid.NewGuid(),
                             Entries = new List<ChangeLogEntrySeed>
                             {
                                 new ChangeLogEntrySeed { TimeStamp = DateTime.UtcNow.AddDays(-30), Description = "Added UV Stabilizer for better durability.", Author = "R&D Dept." },
                                 new ChangeLogEntrySeed { TimeStamp = DateTime.UtcNow.AddDays(-29), Description = "Finalized for production.", Author = "System" }
                             }
                        }
                    }
                }
            },
            // Example 3: Acrylic Primer
            new FormulationSeed
            {
                Id = Guid.NewGuid(),
                Name = "Acrylic Wall Primer",
                Description = "Water-based primer for preparing interior walls before painting.",
                ShelfLife = 18, // months
                EstimatedProductionTime = TimeSpan.FromMinutes(90), // minutes
                Status = "Active",
                Ingredients = new List<IngredientSeed>
                {
                    new IngredientSeed { MaterialName = "Acrylic Resin", QuantityValue = 20.0m },
                    new IngredientSeed { MaterialName = "Titanium Dioxide", QuantityValue = 10.0m }, // Lower pigment for primer
                    new IngredientSeed { MaterialName = "Calcium Carbonate", QuantityValue = 40.0m }, // Higher filler
                    new IngredientSeed { MaterialName = "Talc Powder", QuantityValue = 10.0m },
                    new IngredientSeed { MaterialName = "Genapol (Surfactant)", QuantityValue = 0.6m },
                    new IngredientSeed { MaterialName = "Anti-Foaming Agent", QuantityValue = 0.3m }
                },
                Versions = new List<VersionSeed> // Initial Version
                {
                    new VersionSeed
                    {
                        Id = Guid.NewGuid(),
                        TimeStamp = faker.Date.Past(1, DateTime.UtcNow.AddMonths(-1)), // Ensure it's reasonably old
                        IsFinalized = true,
                        Snapshot = new SnapshotSeed
                        {
                            MinimumProductionQuantityValue = 150.0m, // Liters
                            MinimumProductionQuantityUnit = "L",
                            EstimatedProductionTime = TimeSpan.FromMinutes(90),
                            ShelfLife = 18,
                            Instructions = new List<string> { "Ensure proper dispersion of fillers.", "Add resin component.", "Adjust for primer consistency.", "Strain before packaging." },
                            // Snapshot ingredients match the main list for v1
                        },
                        ChangeLog = new ChangeLogSeed
                        {
                            Id = Guid.NewGuid(),
                            Entries = new List<ChangeLogEntrySeed> { new ChangeLogEntrySeed { TimeStamp = DateTime.UtcNow.AddMonths(-3), Description = "Primer formulation created and finalized.", Author = "System" } }
                        }
                    }
                }
            },
            // Example 4: Textured Exterior Paint
            new FormulationSeed
            {
                Id = Guid.NewGuid(),
                Name = "Rough Texture Exterior Paint",
                Description = "Durable water-based exterior paint with a rough, sandy texture.",
                ShelfLife = 12,
                EstimatedProductionTime = TimeSpan.FromMinutes(150), // Longer due to texture component
                Status = "Active",
                Ingredients = new List<IngredientSeed>
                {
                    new IngredientSeed { MaterialName = "Acrylic Resin", QuantityValue = 22.0m },
                    new IngredientSeed { MaterialName = "Titanium Dioxide", QuantityValue = 15.0m },
                    new IngredientSeed { MaterialName = "River Sand", QuantityValue = 45.0m }, // Key texture ingredient
                    new IngredientSeed { MaterialName = "Calcium Carbonate", QuantityValue = 10.0m },
                    new IngredientSeed { MaterialName = "Dispersing Agent", QuantityValue = 1.0m },
                    new IngredientSeed { MaterialName = "UV Stabilizer", QuantityValue = 0.5m } // Important for exterior
                },
                Versions = new List<VersionSeed> // Initial Version
                {
                    new VersionSeed
                    {
                        Id = Guid.NewGuid(),
                        TimeStamp = faker.Date.Past(1, DateTime.UtcNow.AddMonths(-2)), // Reasonably old
                        IsFinalized = true,
                        Snapshot = new SnapshotSeed
                        {
                            MinimumProductionQuantityValue = 80.0m, // Liters
                            MinimumProductionQuantityUnit = "L",
                            EstimatedProductionTime = TimeSpan.FromMinutes(150),
                            ShelfLife = 12,
                            Instructions = new List<string> { "Pre-mix resin and pigments.", "Slowly add River Sand while mixing.", "Ensure even texture distribution.", "Add UV Stabilizer last.", "Test texture consistency." },
                            // Snapshot ingredients match the main list for v1
                        },
                        ChangeLog = new ChangeLogSeed
                        {
                            Id = Guid.NewGuid(),
                            Entries = new List<ChangeLogEntrySeed> { new ChangeLogEntrySeed { TimeStamp = DateTime.UtcNow.AddMonths(-6), Description = "Textured exterior paint formulated and tested.", Author = "R&D Dept." } }
                        }
                    }
                }
            }
        };

        // --- Insertion Logic ---
        foreach (var formulation in formulations)
        {
            // 1. Insert Formulation
            const string formulationSql = """
                INSERT INTO formulations (id, name, description, shelf_life, estimated_production_time, status)
                VALUES (@Id, @Name, @Description, @ShelfLife, @EstimatedProductionTime, @Status);
                """;
            connection.Execute(formulationSql, formulation);

            // 2. Insert Main Ingredients (Current/Master List)
            var ingredientSeeds = formulation.Ingredients.Select(i =>
            {
                var material = materialInfos.FirstOrDefault(m => m.Value.Name == i.MaterialName);
                if (material.Key == Guid.Empty) return null; // Skip if material not found
                return new
                {
                    Id = Guid.NewGuid(),
                    formulation_id = formulation.Id,
                    MaterialId = material.Key,
                    QuantityValue = i.QuantityValue,
                    QuantityUnit = material.Value.Unit // Use default unit from material
                };
            }).Where(i => i != null).ToList();

            if (ingredientSeeds.Any())
            {
                const string ingredientSql = """
                    INSERT INTO ingredients (id, formulation_id, material_id, quantity_value, quantity_unit)
                    VALUES (@Id, @formulation_id, @MaterialId, @QuantityValue, @QuantityUnit);
                    """;
                connection.Execute(ingredientSql, ingredientSeeds);
            }

            // 3. Insert Versions and their related data
            foreach (var version in formulation.Versions)
            {
                // 3a. Insert Version (including flattened Snapshot properties)
                const string versionSql = """
                    INSERT INTO versions (
                        id, formulation_id, time_stamp, is_finalized,
                        snapshot_minimum_production_quantity_value, snapshot_minimum_production_quantity_unit,
                        snapshot_estimated_production_time, snapshot_shelf_life, snapshot_instructions
                    ) VALUES (
                        @Id, @formulation_id, @TimeStamp, @IsFinalized,
                        @Snapshot_MinimumProductionQuantity_Value, @Snapshot_MinimumProductionQuantity_Unit,
                        @Snapshot_EstimatedProductionTime, @Snapshot_ShelfLife, @Snapshot_Instructions
                    );
                    """;
                // Prepare version data for insertion
                var versionData = new
                {
                    version.Id,
                    formulation_id = formulation.Id,
                    version.TimeStamp,
                    version.IsFinalized,
                    Snapshot_MinimumProductionQuantity_Value = version.Snapshot.MinimumProductionQuantityValue,
                    Snapshot_MinimumProductionQuantity_Unit = version.Snapshot.MinimumProductionQuantityUnit,
                    Snapshot_EstimatedProductionTime = version.Snapshot.EstimatedProductionTime,
                    Snapshot_ShelfLife = version.Snapshot.ShelfLife,
                    Snapshot_Instructions = string.Join(";", version.Snapshot.Instructions ?? new List<string>()) // Convert instructions list to string
                };
                connection.Execute(versionSql, versionData);

                // 3b. Insert Snapshot Ingredients
                List<object> snapshotIngredientSeeds;
                // Check if specific snapshot ingredients are provided
                if (version.Snapshot.SnapshotIngredients != null && version.Snapshot.SnapshotIngredients.Any())
                {
                    // Use the specific snapshot ingredients list
                    snapshotIngredientSeeds = version.Snapshot.SnapshotIngredients.Select(si =>
                    {
                        var material = materialInfos.FirstOrDefault(m => m.Value.Name == si.MaterialName);
                        if (material.Key == Guid.Empty) return null; // Skip if material not found
                        // Create anonymous type for insertion
                        return new
                        {
                            Id = Guid.NewGuid(), // PK for ingredient_snapshot table
                            formulation_snapshot_version_id = version.Id, // FK to Versions table
                            RawMaterialId = material.Key, // Link to materials table ID
                            QuantityValue = si.QuantityValue,
                            QuantityUnit = material.Value.Unit // Get unit from material info
                        };
                    }).Where(si => si != null).Cast<object>().ToList(); // Ensure nulls are filtered and cast correctly
                }
                else
                {
                    // Default to using the main formulation's ingredients for the snapshot
                    snapshotIngredientSeeds = formulation.Ingredients.Select(i => // Note: iterating over formulation.Ingredients (List<IngredientSeed>)
                    {
                        var material = materialInfos.FirstOrDefault(m => m.Value.Name == i.MaterialName);
                        if (material.Key == Guid.Empty) return null; // Skip if material not found
                        // Create anonymous type for insertion
                        return new
                        {
                            Id = Guid.NewGuid(), // PK for ingredient_snapshot table
                            formulation_snapshot_version_id = version.Id, // FK to Versions table
                            RawMaterialId = material.Key, // Link to materials table ID
                            QuantityValue = i.QuantityValue, // Get value from IngredientSeed
                            QuantityUnit = material.Value.Unit // Get unit from material info
                        };
                    }).Where(i => i != null).Cast<object>().ToList(); // Ensure nulls are filtered and cast correctly
                }

                if (snapshotIngredientSeeds.Any())
                {
                    // IMPORTANT: Verify table and column names ('ingredient_snapshot', 'formulation_snapshot_version_id', 'raw_material_id')
                    const string snapshotIngredientSql = """
                        INSERT INTO ingredient_snapshot (ingredient_id, formulation_snapshot_version_id, raw_material_id, quantity_value, quantity_unit)
                        VALUES (@Id, @formulation_snapshot_version_id, @RawMaterialId, @QuantityValue, @QuantityUnit);
                        """;
                    connection.Execute(snapshotIngredientSql, snapshotIngredientSeeds);
                }

                // 3c. Insert ChangeLog
                const string changeLogSql = """
                    INSERT INTO change_logs (id, version_id) VALUES (@Id, @version_id);
                    """;
                connection.Execute(changeLogSql, new { version.ChangeLog.Id, version_id = version.Id });

                // 3d. Insert ChangeLog Entries
                var changeLogEntrySeeds = version.ChangeLog.Entries.Select(cle => new
                {
                    Id = Guid.NewGuid(), // PK for change_log_entry table
                    change_log_id = version.ChangeLog.Id, // FK to ChangeLog table
                    cle.TimeStamp,
                    cle.Description,
                    cle.Author
                }).ToList();

                if (changeLogEntrySeeds.Any())
                {
                    // IMPORTANT: Verify table and column names ('change_log_entry', 'change_log_id')
                    const string changeLogEntrySql = """
                        INSERT INTO change_log_entry ( change_log_id, time_stamp, description, author)
                        VALUES ( @change_log_id, @TimeStamp, @Description, @Author);
                        """;
                    connection.Execute(changeLogEntrySql, changeLogEntrySeeds);
                }
            }
        }
    }

    private static Dictionary<Guid, string> GetFormulationInfos(IDbConnection connection)
    {
        try
        {
            return connection.Query<(Guid Id, string Name)>(
                "SELECT id, name FROM formulations WHERE status = 'Active';"
            ).ToDictionary(f => f.Id, f => f.Name);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error querying formulations: {ex.Message}");
            return new Dictionary<Guid, string>();
        }
    }

    private static Dictionary<Guid, List<WorkOrderVariantInfo>> GetProductVariantInfos(IDbConnection connection)
    {
        try
        {
            const string sql = @"
            SELECT
                pv.id as VariantId,
                pv.product_id as ProductId,
                p.formulation_id as FormulationId,
                pvpo.type as PackagingType,
                pvpo.capacity_value as PackagingCapacityValue,
                pvpo.capacity_unit as PackagingCapacityUnit
            FROM product_variants pv
            JOIN products p ON p.id = pv.product_id
            JOIN product_variant_packaging_options pvpo ON pvpo.product_variant_id = pv.id
            WHERE pv.status = 'Active' AND p.status = 'Active' AND pvpo.status = 'Active';";

            var results = connection.Query<WorkOrderVariantInfo>(sql);
            return results.GroupBy(x => x.VariantId)
                .ToDictionary(
                    g => g.Key,
                    g => g.ToList()
                );
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error querying product variants with packaging: {ex.Message}");
            return new Dictionary<Guid, List<WorkOrderVariantInfo>>();
        }
    }

    private static string SelectWithProbability(IEnumerable<(string Value, double Weight)> items, Faker faker)
    {
        var total = items.Sum(x => x.Weight);
        var random = faker.Random.Double() * total;
        var current = 0.0;

        foreach (var (value, weight) in items)
        {
            current += weight;
            if (random <= current)
                return value;
        }

        return items.First().Value; // fallback
    }

    private static void SeedWorkOrders(IDbConnection connection)
    {
        var faker = new Faker();
        Console.WriteLine("Starting Work Order seeding...");

        // First, get our product variants with their packaging options
        var variantInfos = GetProductVariantInfos(connection);
        if (!variantInfos.Any())
        {
            Console.WriteLine("No product variants found. Cannot seed work orders.");
            return;
        }

        // Get available production lines with their capabilities
        var productionLines = connection.Query<(Guid Id, string Name, string ProductType, decimal MinBatchSize, decimal MaxBatchSize, string Unit)>("""
            SELECT 
                pl.id,
                pl.name,
                c.product_type,
                c.min_batch_size_value as MinBatchSize,
                c.max_batch_size_value as MaxBatchSize,
                c.min_batch_size_unit as Unit
            FROM production_lines pl
            JOIN capabilities c ON c.production_line_id = pl.id
            WHERE pl.status = 'Active'
        """).ToList();

        if (!productionLines.Any())
        {
            Console.WriteLine("No production lines found. Cannot seed work orders.");
            return;
        }

        // Define possible work order statuses and their weights
        var workOrderStatuses = new[]
        {
            ("Draft", 0.1),        // 10% chance
            ("Planned", 0.2),      // 20% chance
            ("InProgress", 0.3),   // 30% chance
            ("Completed", 0.3),    // 30% chance
            ("Cancelled", 0.1)     // 10% chance
        };

        // Generate work orders for the next 2 weeks
        var endDate = DateTime.UtcNow.AddDays(14);
        var startDate = DateTime.UtcNow;
        var currentSequence = 1;

        // Create 100 work orders spread across the last 6 months
        for (int i = 0; i < 100; i++)
        {
            var orderDate = faker.Date.Between(startDate, endDate);
            // Select a random variant and one of its packaging options
            var variantId = faker.PickRandom(variantInfos.Keys.ToList());
            var variantPackagingOptions = variantInfos[variantId];
            var variant = faker.PickRandom(variantPackagingOptions);
            var status = SelectWithProbability(workOrderStatuses, faker);

            // Get product type for the variant
            var productType = connection.QuerySingle<string>(
                "SELECT type FROM products WHERE id = @ProductId",
                new { variant.ProductId });

            // Find suitable production lines for this product type
            var suitableLines = productionLines
                .Where(l => l.ProductType == productType)
                .ToList();

            if (!suitableLines.Any())
            {
                Console.WriteLine($"No suitable production line found for product type {productType}. Skipping work order.");
                continue;
            }

            // Select a production line based on batch size constraints
            var productionLine = faker.PickRandom(suitableLines);

            // Generate work order number
            var workOrderNumber = $"WO-{orderDate:yyMM}-{currentSequence:D5}";
            currentSequence++;

            var workOrder = new WorkOrderSeed
            {
                Id = Guid.NewGuid(),
                WorkOrderNumber = workOrderNumber,
                ProductId = variant.ProductId,
                ProductVariantId = variant.VariantId,
                FormulationId = variant.FormulationId,
                ProductionLineId = productionLine.Id,
                PackagingType = variant.PackagingType,
                PackagingCapacityValue = variant.PackagingCapacityValue,
                PackagingCapacityUnit = variant.PackagingCapacityUnit,
                TotalQuantityValue = faker.Random.Decimal(
                    productionLine.MinBatchSize,
                    Math.Min(productionLine.MaxBatchSize * 2, 1000)), // Ensure within line capacity
                TotalQuantityUnit = productionLine.Unit,
                Status = status,
                StartDate = orderDate,
                DueDate = orderDate.AddDays(faker.Random.Int(14, 30))
            };

            // Insert work order
            const string workOrderSql = """
                INSERT INTO work_orders (
                    id, work_order_number, product_id, variant_id, formulation_id, packaging_type, 
                    packaging_capacity_value, packaging_capacity_unit,
                    total_quantity_value, total_quantity_unit,
                    status, start_date, due_date
                ) VALUES (
                    @Id, @WorkOrderNumber, @ProductId, @ProductVariantId, @FormulationId,
                    @PackagingType, @PackagingCapacityValue, @PackagingCapacityUnit,
                    @TotalQuantityValue, @TotalQuantityUnit,
                    @Status, @StartDate, @DueDate
                );
                """;

            connection.Execute(workOrderSql, workOrder);

            // Create batches for work orders that are InProgress or Completed
            if (status is "InProgress" or "Completed" or "Planned")
            {
                var batches = SeedWorkOrderBatches(connection, workOrder, variant);

                // Schedule batches on the production line
                var currentBatchStart = workOrder.StartDate;
                foreach (var batch in batches)
                {
                    // Estimated duration based on quantity (1 hour per 100 units, minimum 4 hours)
                    var estimatedHours = Math.Max(4, (int)Math.Ceiling(batch.TargetQuantityValue / 100.0m * 1));
                    var batchEndTime = currentBatchStart.AddHours(estimatedHours);

                    // Insert production line schedule
                    const string scheduleSql = """
                        INSERT INTO production_line_schedules (
                            batch_id,
                            production_line_id,
                            start_time,
                            end_time
                        ) VALUES (
                            @BatchId,
                            @ProductionLineId,
                            @StartTime,
                            @EndTime
                        );
                        """;

                    connection.Execute(scheduleSql, new
                    {
                        BatchId = batch.Id,
                        ProductionLineId = productionLine.Id,
                        StartTime = currentBatchStart,
                        EndTime = batchEndTime
                    });

                    // Update start time for next batch
                    // Add 1 hour buffer between batches
                    currentBatchStart = batchEndTime.AddHours(1);
                }
            }
        }

        Console.WriteLine("Finished seeding Work Orders, Batches, and Production Line Schedules.");
    }

    private static List<BatchSeed> SeedWorkOrderBatches(IDbConnection connection, WorkOrderSeed workOrder, WorkOrderVariantInfo variant)
    {
        var faker = new Faker();
        var batches = new List<BatchSeed>();

        // Get formulation ingredients for the batch
        var formulationIngredients = connection.Query<(Guid MaterialId, decimal QuantityValue, string QuantityUnit)>("""
                SELECT material_id, quantity_value, quantity_unit 
                FROM ingredients 
                WHERE formulation_id = @FormulationId
            """, new { FormulationId = workOrder.FormulationId }).ToList();

        // Determine number of batches based on planned quantity
        var batchSize = faker.Random.Decimal(100, Math.Min(200, workOrder.TotalQuantityValue));
        var numberOfBatches = (int)Math.Ceiling(workOrder.TotalQuantityValue / batchSize);

        for (int batchNumber = 1; batchNumber <= numberOfBatches; batchNumber++)
        {
            var isLastBatch = batchNumber == numberOfBatches;
            var batchQuantity = isLastBatch
                ? workOrder.TotalQuantityValue - (batchSize * (batchNumber - 1))
                : batchSize;

            var batchStatus = workOrder.Status == "Completed" ? "Completed" :
                             workOrder.Status == "Planned" ? "Planned" :
                             batchNumber < numberOfBatches ? "Completed" : "InProgress";

            var estimatedDuration = TimeSpan.FromHours(faker.Random.Int(4, 8));
            TimeSpan? actualDuration = batchStatus == "Completed"
                ? TimeSpan.FromHours(faker.Random.Int(3, 9))
                : null;

            var batch = new BatchSeed
            {
                Id = Guid.NewGuid(),
                WorkOrderId = workOrder.Id,
                BatchNumber = $"{workOrder.WorkOrderNumber}-B{batchNumber:D2}",
                FormulationId = workOrder.FormulationId,
                TargetQuantityValue = batchQuantity,
                TargetQuantityUnit = workOrder.TotalQuantityUnit,
                ActualQuantityValue = batchStatus == "Completed"
                    ? batchQuantity * faker.Random.Decimal(0.95m, 1.02m)
                    : null,
                ActualQuantityUnit = workOrder.TotalQuantityUnit,
                Status = batchStatus,
                PlannedStartDate = workOrder.StartDate.AddDays(batchNumber - 1),
                ActualStartDate = batchStatus != "Draft" ? workOrder.StartDate.AddDays(batchNumber - 1).AddHours(faker.Random.Int(0, 4)) : null,
                CompletionDate = batchStatus == "Completed"
                    ? workOrder.StartDate.AddDays(batchNumber - 1).AddHours(faker.Random.Int(8, 12))
                    : null,
                EstimatedDuration = estimatedDuration,
                ActualDuration = actualDuration,
                ActualYield = batchStatus == "Completed" ? faker.Random.Decimal(0.95m, 1.02m) : null,
                Notes = faker.Random.Bool(0.3f) ? faker.Lorem.Sentence() : null
            };

            // Insert batch
            const string batchSql = """
                INSERT INTO work_order_batches (
                    id, work_order_id, batch_number, formulation_id,
                    target_quantity_value, target_quantity_unit,
                    status, planned_start_date, actual_start_date,
                    completion_date, estimated_duration, actual_duration,
                    actual_yield, notes
                ) VALUES (
                    @Id, @WorkOrderId, @BatchNumber, @FormulationId,
                    @TargetQuantityValue, @TargetQuantityUnit,
                    @Status, @PlannedStartDate, @ActualStartDate,
                    @CompletionDate, @EstimatedDuration, @ActualDuration,
                    @ActualYield, @Notes
                );
                """;

            connection.Execute(batchSql, batch);
            batches.Add(batch);

            // Add bill of materials for the batch
            foreach (var ingredient in formulationIngredients)
            {
                // Scale ingredient quantity based on batch size
                var requiredQuantity = (ingredient.QuantityValue / 100) * batch.TargetQuantityValue;

                var billOfMaterial = new
                {
                    BatchId = batch.WorkOrderId,
                    MaterialId = ingredient.MaterialId,
                    RequiredQuantityValue = requiredQuantity,
                    RequiredQuantityUnit = ingredient.QuantityUnit
                };

                const string bomSql = """
                                      INSERT INTO bill_of_materials (
                                          work_order_id, material_id,
                                          required_quantity_value, required_quantity_unit
                                      ) VALUES (
                                          @BatchId, @MaterialId,
                                          @RequiredQuantityValue, @RequiredQuantityUnit
                                      );
                                      """;

                connection.Execute(bomSql, billOfMaterial);
            }

            // Add batch ingredients
            foreach (var ingredient in formulationIngredients)
            {
                // Scale ingredient quantity based on batch size
                var requiredQuantity = (ingredient.QuantityValue / 100) * batch.TargetQuantityValue;
                decimal? actualQuantity = batchStatus == "Completed"
                    ? requiredQuantity * faker.Random.Decimal(0.98m, 1.02m) // 2% variance
                    : null;

                var batchIngredient = new BatchIngredientSeed
                {
                    Id = Guid.NewGuid(),
                    BatchId = batch.Id,
                    MaterialId = ingredient.MaterialId,
                    RequiredQuantityValue = requiredQuantity,
                    RequiredQuantityUnit = ingredient.QuantityUnit,
                    ActualQuantity = actualQuantity
                };

                const string ingredientSql = """
                                             INSERT INTO batch_ingredients (
                                                 batch_id, material_id,
                                                 required_quantity_value, required_quantity_unit,
                                                 actual_quantity
                                             ) VALUES (
                                                 @BatchId, @MaterialId,
                                                 @RequiredQuantityValue, @RequiredQuantityUnit,
                                                 @ActualQuantity
                                             );
                                             """;

                connection.Execute(ingredientSql, batchIngredient);
            }

            // Add production losses for completed batches (30% chance of having a loss)
            if (batchStatus == "Completed" && faker.Random.Bool(0.3f))
            {
                var lossTypes = new[] { "Spillage", "Equipment Malfunction", "Quality Issue", "Setup Loss" };
                var loss = new ProductionLossSeed
                {
                    Id = Guid.NewGuid(),
                    BatchId = batch.Id,
                    Quantity = faker.Random.Decimal(1, 5), // 1-5 units lost
                    LossType = faker.PickRandom(lossTypes),
                    Reason = faker.Lorem.Sentence(),
                    RecordedAt = batch.CompletionDate?.AddMinutes(-faker.Random.Int(30, 180)) ?? DateTime.UtcNow
                };

                const string lossSql = """
                                       INSERT INTO batch_production_losses (
                                           batch_id, quantity, loss_type,
                                           reason, recorded_at
                                       ) VALUES (
                                           @BatchId, @Quantity, @LossType,
                                           @Reason, @RecordedAt
                                       );
                                       """;

                connection.Execute(lossSql, loss);
            }

            // If batch is Completed or InProgress, create quality checks
            if (batchStatus is "Completed" or "InProgress")
            {
                SeedBatchQualityChecks(connection, batch);
            }
        }

        return batches;
    }

    private static void SeedBatchQualityChecks(IDbConnection connection, BatchSeed batch)
    {
        var faker = new Faker();

        // Define quality check parameters
        var parameters = new[]
        {
            ("Viscosity", "KU", 85m, 95m),
            ("Density", "g/cm³", 1.2m, 1.4m),
            ("pH", "pH", 7.5m, 8.5m),
            ("Solid Content", "%", 45m, 55m)
        };

        foreach (var (parameter, unit, min, max) in parameters)
        {
            var isCompleted = batch.Status == "Completed" || faker.Random.Bool(0.7f);
            var measurementValue = isCompleted
                ? faker.Random.Decimal(min * 0.9m, max * 1.1m)  // Completed checks can be out of range
                : faker.Random.Decimal(min, max);
            var isPassing = measurementValue >= min && measurementValue <= max;

            var checkedAt = batch.CompletionDate?.AddMinutes(-faker.Random.Int(30, 120)) ??
                            DateTime.UtcNow.AddMinutes(-faker.Random.Int(0, 60));

            var qualityCheck = new QualityCheckSeed
            {
                Id = Guid.NewGuid(),
                BatchId = batch.Id,
                Parameter = parameter,
                TargetMinValue = min,
                TargetMaxValue = max,
                MeasurementValue = measurementValue,
                MeasurementUnit = unit,
                Status = isCompleted ? (isPassing ? "Pass" : "Fail") : "Pending",
                CheckedBy = isCompleted ? faker.Name.FullName() : "System",
                CheckedAt = checkedAt,
                Notes = !isPassing && isCompleted ? "Out of specification range" : null
            };

            const string qualityCheckSql = """
                                           INSERT INTO batch_quality_checks (
                                               batch_id, parameter, minimum_value, maximum_value,
                                               measured_value, checked_by, checked_at
                                           ) VALUES (
                                               @BatchId, @Parameter, @TargetMinValue, @TargetMaxValue,
                                               @MeasurementValue, @CheckedBy, @CheckedAt
                                           );
                                           """;

            connection.Execute(qualityCheckSql, qualityCheck);
        }
    }

    private static void SeedProductionLines(IDbConnection connection)
    {
        Console.WriteLine("Seeding Production Lines...");

        // Define production lines with their capabilities
        var productionLines = new[]
        {
            new ProductionLineSeed
            {
                Id = Guid.NewGuid(),
                Name = "Line A - High Volume",
                Description = "Main production line for large batches",
                Status = "Active",
                Capabilities = new[]
                {
                    new CapabilitySeed
                    {
                        ProductType = "Interior Emulsion",
                        MinBatchSizeValue = 500,
                        MinBatchSizeUnit = "L",
                        MaxBatchSizeValue = 2000,
                        MaxBatchSizeUnit = "L"
                    },
                    new CapabilitySeed
                    {
                        ProductType = "Exterior Textured",
                        MinBatchSizeValue = 500,
                        MinBatchSizeUnit = "L",
                        MaxBatchSizeValue = 1500,
                        MaxBatchSizeUnit = "L"
                    }
                }
            },
            new ProductionLineSeed
            {
                Id = Guid.NewGuid(),
                Name = "Line B - Medium Batch",
                Description = "Flexible line for medium-sized batches",
                Status = "Active",
                Capabilities = new[]
                {
                    new CapabilitySeed
                    {
                        ProductType = "Interior Emulsion",
                        MinBatchSizeValue = 200,
                        MinBatchSizeUnit = "L",
                        MaxBatchSizeValue = 1000,
                        MaxBatchSizeUnit = "L"
                    },
                    new CapabilitySeed
                    {
                        ProductType = "Exterior Enamel",
                        MinBatchSizeValue = 200,
                        MinBatchSizeUnit = "L",
                        MaxBatchSizeValue = 800,
                        MaxBatchSizeUnit = "L"
                    },
                    new CapabilitySeed
                    {
                        ProductType = "Primer",
                        MinBatchSizeValue = 200,
                        MinBatchSizeUnit = "L",
                        MaxBatchSizeValue = 1000,
                        MaxBatchSizeUnit = "L"
                    }
                }
            },
            new ProductionLineSeed
            {
                Id = Guid.NewGuid(),
                Name = "Line C - Specialty",
                Description = "Specialized line for high-gloss and textured products",
                Status = "Active",
                Capabilities = new[]
                {
                    new CapabilitySeed
                    {
                        ProductType = "Exterior Enamel",
                        MinBatchSizeValue = 100,
                        MinBatchSizeUnit = "L",
                        MaxBatchSizeValue = 500,
                        MaxBatchSizeUnit = "L"
                    },
                    new CapabilitySeed
                    {
                        ProductType = "Exterior Textured",
                        MinBatchSizeValue = 100,
                        MinBatchSizeUnit = "L",
                        MaxBatchSizeValue = 400,
                        MaxBatchSizeUnit = "L"
                    }
                }
            },
            new ProductionLineSeed
            {
                Id = Guid.NewGuid(),
                Name = "Line D - Small Batch",
                Description = "Small batch line for primers and specialty products",
                Status = "Active",
                Capabilities = new[]
                {
                    new CapabilitySeed
                    {
                        ProductType = "Primer",
                        MinBatchSizeValue = 50,
                        MinBatchSizeUnit = "L",
                        MaxBatchSizeValue = 300,
                        MaxBatchSizeUnit = "L"
                    },
                    new CapabilitySeed
                    {
                        ProductType = "Interior Emulsion",
                        MinBatchSizeValue = 50,
                        MinBatchSizeUnit = "L",
                        MaxBatchSizeValue = 400,
                        MaxBatchSizeUnit = "L"
                    }
                }
            },
            new ProductionLineSeed
            {
                Id = Guid.NewGuid(),
                Name = "Line E - Flex",
                Description = "Flexible line for all product types",
                Status = "Active",
                Capabilities = new[]
                {
                    new CapabilitySeed
                    {
                        ProductType = "Interior Emulsion",
                        MinBatchSizeValue = 200,
                        MinBatchSizeUnit = "L",
                        MaxBatchSizeValue = 800,
                        MaxBatchSizeUnit = "L"
                    },
                    new CapabilitySeed
                    {
                        ProductType = "Exterior Enamel",
                        MinBatchSizeValue = 200,
                        MinBatchSizeUnit = "L",
                        MaxBatchSizeValue = 600,
                        MaxBatchSizeUnit = "L"
                    },
                    new CapabilitySeed
                    {
                        ProductType = "Primer",
                        MinBatchSizeValue = 100,
                        MinBatchSizeUnit = "L",
                        MaxBatchSizeValue = 500,
                        MaxBatchSizeUnit = "L"
                    },
                    new CapabilitySeed
                    {
                        ProductType = "Exterior Textured",
                        MinBatchSizeValue = 200,
                        MinBatchSizeUnit = "L",
                        MaxBatchSizeValue = 600,
                        MaxBatchSizeUnit = "L"
                    }
                }
            }
        };

        foreach (var line in productionLines)
        {
            // Insert production line
            const string productionLineSql = """
                INSERT INTO production_lines (id, name, description, status)
                VALUES (@Id, @Name, @Description, @Status);
                """;

            connection.Execute(productionLineSql, line);

            // Insert capabilities for the line
            const string capabilitySql = """
                INSERT INTO capabilities (
                    production_line_id,
                    product_type,
                    min_batch_size_value,
                    min_batch_size_unit,
                    max_batch_size_value,
                    max_batch_size_unit
                ) VALUES (
                    @ProductionLineId,
                    @ProductType,
                    @MinBatchSizeValue,
                    @MinBatchSizeUnit,
                    @MaxBatchSizeValue,
                    @MaxBatchSizeUnit
                );
                """;

            foreach (var capability in line.Capabilities)
            {
                capability.ProductionLineId = line.Id;
                connection.Execute(capabilitySql, capability);
            }
        }

        Console.WriteLine("Finished seeding Production Lines and Capabilities.");
    }

    private class ProductionLineSeed
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Status { get; set; } = "";
        public CapabilitySeed[] Capabilities { get; set; } = Array.Empty<CapabilitySeed>();
    }

    private class CapabilitySeed
    {
        public Guid ProductionLineId { get; set; }
        public string ProductType { get; set; } = "";
        public decimal MinBatchSizeValue { get; set; }
        public string MinBatchSizeUnit { get; set; } = "";
        public decimal MaxBatchSizeValue { get; set; }
        public string MaxBatchSizeUnit { get; set; } = "";
    }

    private class FormulationSeed
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int ShelfLife { get; set; }
        public TimeSpan EstimatedProductionTime { get; set; }
        public string Status { get; set; }
        public List<IngredientSeed> Ingredients { get; set; } = new();
        public List<VersionSeed> Versions { get; set; } = new();
    }

    private class IngredientSeed // Used for main formulation ingredients
    {
        // We'll look up MaterialId and Unit based on Name
        public string MaterialName { get; set; }
        public decimal QuantityValue { get; set; }
    }

    private class VersionSeed
    {
        public Guid Id { get; set; }
        public DateTime TimeStamp { get; set; }
        public bool IsFinalized { get; set; }
        public SnapshotSeed Snapshot { get; set; }
        public ChangeLogSeed ChangeLog { get; set; }
    }

    private class SnapshotSeed
    {
        public decimal MinimumProductionQuantityValue { get; set; }
        public string MinimumProductionQuantityUnit { get; set; }
        public TimeSpan EstimatedProductionTime { get; set; }
        public int ShelfLife { get; set; }
        public List<string>? Instructions { get; set; }
        // If SnapshotIngredients is null, we assume it uses the main Formulation ingredients for that version
        public List<SnapshotIngredientSeed>? SnapshotIngredients { get; set; }
    }


    private class SnapshotIngredientSeed
    {
        // We'll look up MaterialId and Unit based on Name
        public string MaterialName { get; set; }
        public decimal QuantityValue { get; set; }
        // We don't need MaterialId here directly as we link via MaterialName,
        // but the INSERT statement needs the RawMaterialId column populated.
    }

    private class ChangeLogSeed
    {
        public Guid Id { get; set; }
        public List<ChangeLogEntrySeed> Entries { get; set; } = new();
    }

    private class ChangeLogEntrySeed
    {
        public DateTime TimeStamp { get; set; }
        public string Description { get; set; }
        public string Author { get; set; }
    }

    private class ProductSeed_New
    {
        public Guid Id { get; set; }
        public Guid FormulationId { get; set; }
        public string NameValue { get; set; } = "";
        public string ProductSku { get; set; } = ""; // Product line SKU?
        public string ColorValue { get; set; } = "";
        public decimal VolumeValue { get; set; }
        public string VolumeUnit { get; set; } = "";
        public string Type { get; set; } = "";
        public decimal PriceAmount { get; set; }
        public string PriceCurrency { get; set; } = "";
        public DateTime CreatedOnUtc { get; set; }
        public string Status { get; set; } = "";
    }

    private class ProductVariantSeed_New
    {
        public Guid Id { get; set; }
        public Guid ProductId { get; set; }
        public string VariantSku { get; set; } = ""; // Generated SKU
        public decimal VolumeValue { get; set; } // Base volume (e.g., 1L)
        public string VolumeUnit { get; set; } = "";
        public string ColorValue { get; set; } = "";
        public string Status { get; set; } = "";
    }

    private class PackagingOptionSeed
    {
        public Guid Id { get; set; }
        public Guid ProductVariantId { get; set; }
        public string Type { get; set; } = ""; // Can, Pail, Drum
        public string Material { get; set; } = ""; // Metal, Plastic
        public decimal CapacityValue { get; set; }
        public string CapacityUnit { get; set; } = ""; // L
        public decimal PriceAmount { get; set; }
        public string PriceCurrency { get; set; } = "";
        public string Status { get; set; } = "";
    }

    private class WorkOrderSeed
    {
        public Guid Id { get; set; }
        public string WorkOrderNumber { get; set; }
        public Guid ProductId { get; set; }
        public Guid ProductVariantId { get; set; }
        public Guid FormulationId { get; set; }
        public Guid ProductionLineId { get; set; }
        public string PackagingType { get; set; }
        public decimal PackagingCapacityValue { get; set; }
        public string PackagingCapacityUnit { get; set; }
        public decimal TotalQuantityValue { get; set; }
        public string TotalQuantityUnit { get; set; }
        public string Status { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime DueDate { get; set; }
    }

    private class BatchSeed
    {
        public Guid Id { get; set; }
        public Guid WorkOrderId { get; set; }
        public string BatchNumber { get; set; }
        public Guid FormulationId { get; set; }
        public decimal TargetQuantityValue { get; set; }
        public string TargetQuantityUnit { get; set; }
        public decimal? ActualQuantityValue { get; set; }
        public string ActualQuantityUnit { get; set; }
        public string Status { get; set; }
        public DateTime? PlannedStartDate { get; set; }
        public DateTime? ActualStartDate { get; set; }
        public DateTime? CompletionDate { get; set; }
        public TimeSpan EstimatedDuration { get; set; }
        public TimeSpan? ActualDuration { get; set; }
        public decimal? ActualYield { get; set; }
        public string Notes { get; set; }
    }

    private class QualityCheckSeed
    {
        public Guid Id { get; set; }
        public Guid BatchId { get; set; }
        public string Parameter { get; set; }
        public decimal TargetMinValue { get; set; }
        public decimal TargetMaxValue { get; set; }
        public decimal? MeasurementValue { get; set; }
        public string MeasurementUnit { get; set; }
        public string Status { get; set; }
        public string CheckedBy { get; set; }
        public DateTime? CheckedAt { get; set; }
        public string Notes { get; set; }
    }

    private class WorkOrderVariantInfo
    {
        public Guid VariantId { get; set; }
        public Guid ProductId { get; set; }
        public Guid FormulationId { get; set; }
        public string PackagingType { get; set; } = "";
        public decimal PackagingCapacityValue { get; set; }
        public string PackagingCapacityUnit { get; set; } = "";
    }

    private class BatchIngredientSeed
    {
        public Guid Id { get; set; }
        public Guid BatchId { get; set; }
        public Guid MaterialId { get; set; }
        public decimal RequiredQuantityValue { get; set; }
        public string RequiredQuantityUnit { get; set; }
        public decimal? ActualQuantity { get; set; }
    }

    private class ProductionLossSeed
    {
        public Guid Id { get; set; }
        public Guid BatchId { get; set; }
        public decimal Quantity { get; set; }
        public string LossType { get; set; }
        public string Reason { get; set; }
        public DateTime RecordedAt { get; set; }
    }

    // Material Inventory Seeding Classes
    private class MaterialInfo
    {
        public Guid Id { get; set; }
        public string Code { get; set; } = "";
        public string Name { get; set; } = "";
        public string Type { get; set; } = "";
        public string DefaultUnit { get; set; } = "";
        public decimal? AvgUnitPrice { get; set; }
        public string? UnitPriceCurrency { get; set; }
    }

    // Formulation Seeding Classes
    private class FormulationInfo
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Type { get; set; } = "";
        public string Status { get; set; } = "";
        public int ShelfLife { get; set; }
        public int ProductionTime { get; set; }
        public List<IngredientInfo> Ingredients { get; set; } = new();
        public List<string> Instructions { get; set; } = new();
    }

    private class IngredientInfo
    {
        public Guid Id { get; set; }
        public Guid MaterialId { get; set; }
        public string MaterialName { get; set; } = "";
        public decimal QuantityValue { get; set; }
        public string QuantityUnit { get; set; } = "";
    }

    private class PaintSpecification
    {
        public string Name { get; set; } = "";
        public string Type { get; set; } = "";
        public string BaseColor { get; set; } = "";
        public string Finish { get; set; } = "";
        public int ShelfLife { get; set; }
        public int ProductionTime { get; set; }
    }

    private class MaterialCostInfo
    {
        public Guid MaterialId { get; set; }
        public decimal AverageUnitPrice { get; set; }
        public string Currency { get; set; } = "";
        public string MaterialName { get; set; } = "";
    }
}

