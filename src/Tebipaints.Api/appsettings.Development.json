{"ConnectionStrings": {"Database": "Host=tebipaints-db;Port=5432;Database=tebipaints;Username=********;Password=********;"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Seq"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "Seq", "Args": {"serverUrl": "http://tebipaints-seq:5341"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "Outbox": {"IntervalInSeconds": 10, "BatchSize": 10}, "Puppeteer": {"WebSocketEndpoint": "ws://tebipaints-chrome:3000"}}