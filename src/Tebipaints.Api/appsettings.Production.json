{"ConnectionStrings": {"Database": "Host=srv-captain--tebipaints-test-db;Port=5432;Database=tebipaints;Username=postgres;Password=****************;"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Seq"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "Seq", "Args": {"serverUrl": "http://machomes-seq:5341"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "Outbox": {"IntervalInSeconds": 10, "BatchSize": 10}, "Puppeteer": {"WebSocketEndpoint": "ws://srv-captain--tebipaints-chrome:3000"}}